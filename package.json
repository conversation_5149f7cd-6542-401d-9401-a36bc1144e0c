{"name": "nexusbot", "version": "2.0.0", "description": "A comprehensive Discord bot with advanced moderation, entertainment, utility, music, economy features, and web dashboard", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "docs": "jsdoc -c jsdoc.conf.json", "deploy": "node scripts/deploy-commands.js", "migrate": "node scripts/migrate-database.js", "backup": "node scripts/backup-database.js", "dashboard": "node src/dashboard/server.js", "build": "npm run lint && npm run test", "health": "node scripts/health-check.js"}, "keywords": ["discord", "bot", "moderation", "music", "economy", "entertainment", "dashboard", "ai", "automation"], "author": "NexusBot Team", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "@supabase/supabase-js": "^2.39.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "@discordjs/voice": "^0.16.1", "@discordjs/opus": "^0.9.0", "ytdl-core": "^4.11.5", "play-dl": "^1.9.7", "spotify-web-api-node": "^5.0.2", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "rate-limiter-flexible": "^3.0.8", "node-cron": "^3.0.3", "canvas": "^2.11.2", "sharp": "^0.33.2", "axios": "^1.6.5", "cheerio": "^1.0.0-rc.12", "openai": "^4.24.7", "google-translate-api": "^2.3.0", "weather-js": "^2.0.0", "moment": "^2.30.1", "moment-timezone": "^0.5.44", "node-cache": "^5.1.2", "ioredis": "^5.3.2", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "validator": "^13.11.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "chalk": "^4.1.2", "figlet": "^1.7.0", "progress": "^2.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jsdoc": "^4.0.2", "supertest": "^6.3.4", "@types/node": "^20.11.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/wat40/nexusbot.git"}, "bugs": {"url": "https://github.com/wat40/nexusbot/issues"}, "homepage": "https://wat40.github.io/"}