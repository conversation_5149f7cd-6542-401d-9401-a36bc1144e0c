{"name": "nexusbot", "version": "1.0.0", "description": "A comprehensive Discord bot with moderation, entertainment, utility, music, and economy features", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "db:migrate": "node src/database/migrate.js", "db:seed": "node src/database/seed.js"}, "keywords": ["discord", "bot", "moderation", "music", "economy", "entertainment"], "author": "NexusBot Team", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "@discordjs/builders": "^1.7.0", "@discordjs/rest": "^2.2.0", "discord-api-types": "^0.37.61", "sqlite3": "^5.1.6", "pg": "^8.11.3", "sequelize": "^6.35.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "axios": "^1.6.2", "node-cron": "^3.0.3", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "ytdl-core": "^4.11.5", "@discordjs/voice": "^0.16.1", "play-dl": "^1.9.7", "sodium": "^3.0.2", "ffmpeg-static": "^5.2.0", "prism-media": "^1.3.5", "express": "^4.18.2", "helmet": "^7.1.0", "cors": "^2.8.5", "rate-limiter-flexible": "^4.0.1", "joi": "^17.11.0", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "canvas": "^2.11.2", "sharp": "^0.32.6"}, "devDependencies": {"nodemon": "^3.0.2", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/nexusbot.git"}, "bugs": {"url": "https://github.com/your-username/nexusbot/issues"}, "homepage": "https://github.com/your-username/nexusbot#readme"}