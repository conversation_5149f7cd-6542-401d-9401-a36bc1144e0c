// ===== MAIN JAVASCRIPT FOR NEXUSBOT DASHBOARD =====

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initNavigation();
    initCounters();
    initCommandTabs();
    initScrollEffects();
    initParticles();
});

// ===== NAVIGATION =====
function initNavigation() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    if (navToggle) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }

    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            const href = link.getAttribute('href');
            
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                
                if (target) {
                    const offsetTop = target.offsetTop - 80; // Account for fixed navbar
                    
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
                
                // Close mobile menu if open
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            }
        });
    });

    // Update active nav link on scroll
    window.addEventListener('scroll', updateActiveNavLink);
}

function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.clientHeight;
        
        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
}

// ===== ANIMATED COUNTERS =====
function initCounters() {
    const counters = document.querySelectorAll('[data-count]');
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    counters.forEach(counter => {
        observer.observe(counter);
    });
}

function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-count'));
    const duration = 2000; // 2 seconds
    const increment = target / (duration / 16); // 60fps
    let current = 0;

    const timer = setInterval(() => {
        current += increment;
        
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        
        element.textContent = Math.floor(current).toLocaleString();
    }, 16);
}

// ===== COMMAND TABS =====
function initCommandTabs() {
    const categoryTabs = document.querySelectorAll('.category-tab');
    const commandLists = document.querySelectorAll('.command-list');

    // Command data
    const commandData = {
        moderation: [
            { name: '-kick', description: 'Kick a member from the server' },
            { name: '-ban', description: 'Ban a member from the server' },
            { name: '-warn', description: 'Warn a member' },
            { name: '-clear', description: 'Clear messages from a channel' },
            { name: '-timeout', description: 'Timeout a member' },
            { name: '-warnings', description: 'View warnings for a user' }
        ],
        music: [
            { name: '-play', description: 'Play music from YouTube or Spotify' },
            { name: '-queue', description: 'Show the current music queue' },
            { name: '-skip', description: 'Skip the current song' },
            { name: '-stop', description: 'Stop music and clear queue' },
            { name: '-volume', description: 'Adjust the music volume' },
            { name: '-nowplaying', description: 'Show current song info' }
        ],
        economy: [
            { name: '-balance', description: 'Check your balance' },
            { name: '-daily', description: 'Claim your daily reward' },
            { name: '-pay', description: 'Transfer money to another user' },
            { name: '-work', description: 'Work to earn money' },
            { name: '-shop', description: 'Browse the server shop' },
            { name: '-leaderboard', description: 'View economy leaderboard' }
        ],
        entertainment: [
            { name: '-8ball', description: 'Ask the magic 8-ball a question' },
            { name: '-dice', description: 'Roll dice with custom sides' },
            { name: '-coinflip', description: 'Flip a coin' },
            { name: '-trivia', description: 'Start a trivia game' },
            { name: '-poll', description: 'Create a poll' },
            { name: '-giveaway', description: 'Start a giveaway' }
        ],
        utility: [
            { name: '-help', description: 'Show help information' },
            { name: '-ping', description: 'Check bot latency' },
            { name: '-weather', description: 'Get weather information' },
            { name: '-translate', description: 'Translate text' },
            { name: '-serverinfo', description: 'Show server information' },
            { name: '-userinfo', description: 'Show user information' }
        ]
    };

    // Create command lists for each category
    Object.keys(commandData).forEach(category => {
        let existingList = document.getElementById(category);
        
        if (!existingList) {
            existingList = document.createElement('div');
            existingList.className = 'command-list';
            existingList.id = category;
            document.querySelector('.command-content').appendChild(existingList);
        }

        existingList.innerHTML = commandData[category].map(command => `
            <div class="command-item">
                <div class="command-name">${command.name}</div>
                <div class="command-description">${command.description}</div>
            </div>
        `).join('');
    });

    // Tab switching functionality
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const category = tab.getAttribute('data-category');
            
            // Update active tab
            categoryTabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            
            // Update active command list
            commandLists.forEach(list => list.classList.remove('active'));
            const targetList = document.getElementById(category);
            if (targetList) {
                targetList.classList.add('active');
            }
        });
    });
}

// ===== SCROLL EFFECTS =====
function initScrollEffects() {
    // Navbar background on scroll
    window.addEventListener('scroll', () => {
        const navbar = document.querySelector('.navbar');
        
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(26, 26, 26, 0.98)';
        } else {
            navbar.style.background = 'rgba(26, 26, 26, 0.95)';
        }
    });

    // Fade in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for fade-in animation
    const animatedElements = document.querySelectorAll('.feature-card, .command-item, .section-header');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// ===== PARTICLE ANIMATION =====
function initParticles() {
    const particlesContainer = document.querySelector('.hero-particles');
    
    if (!particlesContainer) return;

    // Create floating particles
    for (let i = 0; i < 20; i++) {
        createParticle(particlesContainer);
    }
}

function createParticle(container) {
    const particle = document.createElement('div');
    particle.style.position = 'absolute';
    particle.style.width = Math.random() * 4 + 2 + 'px';
    particle.style.height = particle.style.width;
    particle.style.background = `rgba(0, 212, 255, ${Math.random() * 0.5 + 0.1})`;
    particle.style.borderRadius = '50%';
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';
    particle.style.pointerEvents = 'none';
    
    // Animate particle
    const duration = Math.random() * 20 + 10; // 10-30 seconds
    particle.style.animation = `float ${duration}s infinite linear`;
    
    container.appendChild(particle);
    
    // Remove particle after animation
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
            createParticle(container); // Create new particle
        }
    }, duration * 1000);
}

// ===== UTILITY FUNCTIONS =====

// Smooth scroll to element
function scrollToElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        const offsetTop = element.offsetTop - 80;
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }
}

// Copy text to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Copied to clipboard!', 'success');
    }).catch(() => {
        showNotification('Failed to copy', 'error');
    });
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        padding: 1rem 1.5rem;
        background: var(--${type === 'success' ? 'success' : type === 'error' ? 'error' : 'info'}-color);
        color: white;
        border-radius: 0.5rem;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// ===== INVITE BOT FUNCTIONALITY =====
document.addEventListener('click', (e) => {
    if (e.target.closest('[href="#invite"]')) {
        e.preventDefault();

        // Replace with your actual bot invite URL
        const inviteUrl = 'https://discord.com/api/oauth2/authorize?client_id=1396212968743505930&permissions=8&scope=bot%20applications.commands';

        window.open(inviteUrl, '_blank');
        showNotification('Opening Discord invite...', 'info');
    }
});

// ===== COMMANDS DATA =====
window.commandsData = {
    moderation: [
        {
            name: 'kick',
            description: 'Kick a member from the server',
            usage: 'kick <user> [reason]',
            examples: ['kick @user Spamming', 'kick 123456789 Breaking rules'],
            aliases: [],
            permissions: ['Kick Members'],
            cooldown: 5,
            category: 'moderation'
        },
        {
            name: 'ban',
            description: 'Ban a member from the server',
            usage: 'ban <user> [reason]',
            examples: ['ban @user Harassment', 'ban 123456789 Repeated violations'],
            aliases: [],
            permissions: ['Ban Members'],
            cooldown: 5,
            category: 'moderation'
        },
        {
            name: 'warn',
            description: 'Warn a member',
            usage: 'warn <user> <reason>',
            examples: ['warn @user Please follow the rules', 'warn 123456789 Inappropriate language'],
            aliases: [],
            permissions: ['Manage Messages'],
            cooldown: 3,
            category: 'moderation'
        },
        {
            name: 'clear',
            description: 'Clear messages from a channel',
            usage: 'clear <amount> [user]',
            examples: ['clear 10', 'clear 50 @user'],
            aliases: ['purge', 'delete'],
            permissions: ['Manage Messages'],
            cooldown: 5,
            category: 'moderation'
        },
        {
            name: 'timeout',
            description: 'Timeout a member',
            usage: 'timeout <user> <duration> [reason]',
            examples: ['timeout @user 10m Spamming', 'timeout 123456789 1h Inappropriate behavior'],
            aliases: ['mute'],
            permissions: ['Moderate Members'],
            cooldown: 3,
            category: 'moderation'
        },
        {
            name: 'warnings',
            description: 'View warnings for a user',
            usage: 'warnings [user]',
            examples: ['warnings', 'warnings @user'],
            aliases: ['warns'],
            permissions: ['Manage Messages'],
            cooldown: 3,
            category: 'moderation'
        }
    ],
    music: [
        {
            name: 'play',
            description: 'Play music from YouTube or Spotify',
            usage: 'play <song name or URL>',
            examples: ['play Never Gonna Give You Up', 'play https://www.youtube.com/watch?v=dQw4w9WgXcQ'],
            aliases: ['p'],
            permissions: [],
            cooldown: 3,
            category: 'music'
        },
        {
            name: 'queue',
            description: 'Show the current music queue',
            usage: 'queue',
            examples: ['queue'],
            aliases: ['q'],
            permissions: [],
            cooldown: 3,
            category: 'music'
        },
        {
            name: 'skip',
            description: 'Skip the current song',
            usage: 'skip',
            examples: ['skip'],
            aliases: ['s'],
            permissions: [],
            cooldown: 3,
            category: 'music'
        },
        {
            name: 'stop',
            description: 'Stop music and clear the queue',
            usage: 'stop',
            examples: ['stop'],
            aliases: ['disconnect', 'leave'],
            permissions: [],
            cooldown: 3,
            category: 'music'
        },
        {
            name: 'volume',
            description: 'Adjust the music volume',
            usage: 'volume <1-100>',
            examples: ['volume 50', 'volume 75'],
            aliases: ['vol'],
            permissions: [],
            cooldown: 2,
            category: 'music'
        },
        {
            name: 'nowplaying',
            description: 'Show current song information',
            usage: 'nowplaying',
            examples: ['nowplaying'],
            aliases: ['np'],
            permissions: [],
            cooldown: 3,
            category: 'music'
        }
    ],
    economy: [
        {
            name: 'balance',
            description: 'Check your or another user\'s balance',
            usage: 'balance [user]',
            examples: ['balance', 'balance @user'],
            aliases: ['bal', 'money'],
            permissions: [],
            cooldown: 3,
            category: 'economy'
        },
        {
            name: 'daily',
            description: 'Claim your daily reward',
            usage: 'daily',
            examples: ['daily'],
            aliases: [],
            permissions: [],
            cooldown: 86400,
            category: 'economy'
        },
        {
            name: 'pay',
            description: 'Transfer money to another user',
            usage: 'pay <user> <amount>',
            examples: ['pay @user 100', 'pay 123456789 500'],
            aliases: ['give', 'transfer'],
            permissions: [],
            cooldown: 5,
            category: 'economy'
        },
        {
            name: 'work',
            description: 'Work to earn money',
            usage: 'work',
            examples: ['work'],
            aliases: [],
            permissions: [],
            cooldown: 3600,
            category: 'economy'
        },
        {
            name: 'shop',
            description: 'Browse the server shop',
            usage: 'shop [item]',
            examples: ['shop', 'shop premium-role'],
            aliases: ['store'],
            permissions: [],
            cooldown: 3,
            category: 'economy'
        },
        {
            name: 'leaderboard',
            description: 'View economy leaderboard',
            usage: 'leaderboard [type]',
            examples: ['leaderboard', 'leaderboard money'],
            aliases: ['lb', 'top'],
            permissions: [],
            cooldown: 5,
            category: 'economy'
        }
    ],
    entertainment: [
        {
            name: '8ball',
            description: 'Ask the magic 8-ball a question',
            usage: '8ball <question>',
            examples: ['8ball Will it rain tomorrow?', '8ball Should I learn JavaScript?'],
            aliases: ['eightball', 'magic8ball'],
            permissions: [],
            cooldown: 3,
            category: 'entertainment'
        },
        {
            name: 'dice',
            description: 'Roll dice with customizable sides and quantity',
            usage: 'dice [quantity]d[sides]',
            examples: ['dice', 'dice 2d6', 'dice 1d20'],
            aliases: ['roll', 'd'],
            permissions: [],
            cooldown: 2,
            category: 'entertainment'
        },
        {
            name: 'coinflip',
            description: 'Flip a coin',
            usage: 'coinflip',
            examples: ['coinflip'],
            aliases: ['flip', 'coin'],
            permissions: [],
            cooldown: 2,
            category: 'entertainment'
        },
        {
            name: 'trivia',
            description: 'Start a trivia game',
            usage: 'trivia [category]',
            examples: ['trivia', 'trivia science'],
            aliases: [],
            permissions: [],
            cooldown: 10,
            category: 'entertainment'
        },
        {
            name: 'poll',
            description: 'Create a poll with multiple options',
            usage: 'poll <question> | <option1> | <option2> | ...',
            examples: ['poll What\'s your favorite color? | Red | Blue | Green'],
            aliases: [],
            permissions: [],
            cooldown: 30,
            category: 'entertainment'
        },
        {
            name: 'giveaway',
            description: 'Start a giveaway',
            usage: 'giveaway <duration> <prize>',
            examples: ['giveaway 1h Discord Nitro', 'giveaway 30m $10 Steam Gift Card'],
            aliases: [],
            permissions: ['Manage Messages'],
            cooldown: 60,
            category: 'entertainment'
        }
    ],
    utility: [
        {
            name: 'help',
            description: 'Show help information for commands',
            usage: 'help [command]',
            examples: ['help', 'help kick'],
            aliases: ['h', 'commands'],
            permissions: [],
            cooldown: 3,
            category: 'utility'
        },
        {
            name: 'ping',
            description: 'Check bot latency and uptime',
            usage: 'ping',
            examples: ['ping'],
            aliases: ['latency', 'pong'],
            permissions: [],
            cooldown: 3,
            category: 'utility'
        },
        {
            name: 'weather',
            description: 'Get weather information for a location',
            usage: 'weather <location>',
            examples: ['weather New York', 'weather London, UK'],
            aliases: [],
            permissions: [],
            cooldown: 5,
            category: 'utility'
        },
        {
            name: 'translate',
            description: 'Translate text to another language',
            usage: 'translate <language> <text>',
            examples: ['translate spanish Hello world', 'translate french Good morning'],
            aliases: [],
            permissions: [],
            cooldown: 3,
            category: 'utility'
        },
        {
            name: 'serverinfo',
            description: 'Show server information and statistics',
            usage: 'serverinfo',
            examples: ['serverinfo'],
            aliases: ['server', 'guild'],
            permissions: [],
            cooldown: 5,
            category: 'utility'
        },
        {
            name: 'userinfo',
            description: 'Show user information and statistics',
            usage: 'userinfo [user]',
            examples: ['userinfo', 'userinfo @user'],
            aliases: ['user', 'whois'],
            permissions: [],
            cooldown: 3,
            category: 'utility'
        }
    ]
};

// ===== PERFORMANCE OPTIMIZATION =====

// Debounce function for scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply debouncing to scroll events
window.addEventListener('scroll', debounce(updateActiveNavLink, 10));

// Preload critical images
function preloadImages() {
    const imageUrls = [
        // Add any critical images here
    ];
    
    imageUrls.forEach(url => {
        const img = new Image();
        img.src = url;
    });
}

// Initialize preloading
preloadImages();
