# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
CLIENT_ID=your_discord_application_id_here
GUILD_ID=your_test_guild_id_here

# Database Configuration
DATABASE_URL=sqlite:./data/nexusbot.db
# For PostgreSQL production:
# DATABASE_URL=postgresql://username:password@localhost:5432/nexusbot

# API Keys
OPENWEATHER_API_KEY=your_openweather_api_key_here
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key_here
YOUTUBE_API_KEY=your_youtube_api_key_here
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here

# Bot Configuration
DEFAULT_PREFIX=!
BOT_OWNER_ID=your_discord_user_id_here
SUPPORT_SERVER_INVITE=https://discord.gg/your_support_server
BOT_WEBSITE=https://your-bot-website.com

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/
MAX_LOG_FILES=14

# Web Dashboard (Optional)
WEB_PORT=3000
WEB_HOST=localhost
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here

# Rate Limiting
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=10

# Economy System
DAILY_REWARD_AMOUNT=100
DAILY_REWARD_COOLDOWN=86400000
STARTING_BALANCE=1000

# Music System
MAX_QUEUE_SIZE=50
DEFAULT_VOLUME=50
MUSIC_TIMEOUT=300000

# Moderation
MAX_WARNINGS_BEFORE_ACTION=3
AUTO_MOD_ENABLED=true
SPAM_THRESHOLD=5
SPAM_WINDOW=10000

# Development
NODE_ENV=development
DEBUG=false
