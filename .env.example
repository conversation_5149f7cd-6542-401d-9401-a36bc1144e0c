# ===== DISCORD CONFIGURATION =====
DISCORD_TOKEN=your_discord_bot_token_here
CLIENT_ID=your_discord_application_id_here
GUILD_ID=your_test_guild_id_here

# ===== SUPABASE CONFIGURATION (PRIMARY) =====
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_KEY=your_supabase_service_role_key_here

# ===== LEGACY DATABASE CONFIGURATION (OPTIONAL) =====
DATABASE_URL=sqlite:./data/nexusbot.db
# For PostgreSQL production:
# DATABASE_URL=postgresql://username:password@localhost:5432/nexusbot

# ===== HUGGING FACE AI (FREE) =====
# Get your free API key from: https://huggingface.co/settings/tokens
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
HUGGINGFACE_API_URL=https://api-inference.huggingface.co

# Hugging Face Model Configuration (Optional - defaults provided)
HF_TOXICITY_MODEL=unitary/toxic-bert
HF_SENTIMENT_MODEL=cardiffnlp/twitter-roberta-base-sentiment-latest
HF_TEXT_MODEL=microsoft/DialoGPT-medium
HF_CLASSIFICATION_MODEL=facebook/bart-large-mnli
HF_IMAGE_CAPTION_MODEL=Salesforce/blip-image-captioning-large
HF_OBJECT_DETECTION_MODEL=facebook/detr-resnet-50
HF_NSFW_MODEL=Falconsai/nsfw_image_detection
HF_TRANSLATION_MODEL=Helsinki-NLP/opus-mt-en-es
HF_SUMMARIZATION_MODEL=facebook/bart-large-cnn
HF_QA_MODEL=deepset/roberta-base-squad2

# ===== AI FEATURES =====
AI_FEATURES_ENABLED=true
AI_CHATBOT_ENABLED=true
AI_PERSONALITY=helpful
AI_MAX_HISTORY=10
AI_MODERATION_ENABLED=true
AI_TOXICITY_THRESHOLD=0.7

# ===== API KEYS =====
OPENWEATHER_API_KEY=your_openweather_api_key_here
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key_here
YOUTUBE_API_KEY=your_youtube_api_key_here
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
SPOTIFY_REDIRECT_URI=http://localhost:3000/callback

# ===== BOT CONFIGURATION =====
DEFAULT_PREFIX=!
BOT_OWNER_ID=your_discord_user_id_here
SUPPORT_SERVER_INVITE=https://discord.gg/your_support_server
BOT_WEBSITE=https://your-bot-website.com

# ===== LOGGING CONFIGURATION =====
LOG_LEVEL=info
LOG_FILE_PATH=./logs/
MAX_LOG_FILES=14

# ===== WEB DASHBOARD CONFIGURATION (OPTIONAL) =====
WEB_PORT=3000
WEB_HOST=localhost
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here

# ===== RATE LIMITING CONFIGURATION =====
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=10

# ===== ECONOMY SYSTEM CONFIGURATION =====
DAILY_REWARD_AMOUNT=100
DAILY_REWARD_COOLDOWN=86400000
STARTING_BALANCE=1000

# ===== MUSIC SYSTEM CONFIGURATION =====
MAX_QUEUE_SIZE=50
DEFAULT_VOLUME=50
MUSIC_TIMEOUT=300000

# ===== MODERATION CONFIGURATION =====
MAX_WARNINGS_BEFORE_ACTION=3
AUTO_MOD_ENABLED=true
SPAM_THRESHOLD=5
SPAM_WINDOW=10000

# ===== REDIS CONFIGURATION (OPTIONAL) =====
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ===== BACKUP CONFIGURATION =====
BACKUP_ENABLED=true
MAX_BACKUPS=30
BACKUP_COMPRESSION=true

# ===== ADVANCED FEATURES =====
REMINDERS_ENABLED=true
MAX_REMINDERS_PER_USER=10
CUSTOM_COMMANDS_ENABLED=true
MAX_CUSTOM_COMMANDS_PER_GUILD=50

# ===== SECURITY =====
RATE_LIMITING_ENABLED=true
STRICT_RATE_LIMITING=false
ENCRYPTION_KEY=your_32_character_encryption_key_here

# ===== MONITORING =====
MONITORING_ENABLED=true
HEALTH_CHECKS_ENABLED=true
METRICS_ENABLED=true

# ===== DEVELOPMENT =====
NODE_ENV=development
DEBUG=false
TEST_MODE=false
