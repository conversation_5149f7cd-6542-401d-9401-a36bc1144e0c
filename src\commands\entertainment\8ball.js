const { SlashCommandBuilder } = require('discord.js');
const embeds = require('../../utils/embeds');

module.exports = {
  name: '8ball',
  description: 'Ask the magic 8-ball a question',
  aliases: ['eightball', 'magic8ball'],
  usage: '8ball <question>',
  examples: ['8ball Will it rain tomorrow?', '8ball Should I learn JavaScript?'],
  cooldown: 3,
  permissions: [],
  
  data: new SlashCommandBuilder()
    .setName('8ball')
    .setDescription('Ask the magic 8-ball a question')
    .addStringOption(option =>
      option.setName('question')
        .setDescription('The question to ask the magic 8-ball')
        .setRequired(true)
    ),

  async execute(messageOrInteraction, args, client) {
    try {
      const isSlash = messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.();
      
      // Get the question
      let question;
      
      if (isSlash) {
        question = messageOrInteraction.options.getString('question');
      } else {
        question = args.join(' ');
        
        if (!question) {
          const embed = embeds.error('No Question', 'Please provide a question for the magic 8-ball!');
          return messageOrInteraction.reply({ embeds: [embed] });
        }
      }

      // Magic 8-ball responses
      const responses = [
        // Positive responses
        'It is certain.',
        'It is decidedly so.',
        'Without a doubt.',
        'Yes definitely.',
        'You may rely on it.',
        'As I see it, yes.',
        'Most likely.',
        'Outlook good.',
        'Yes.',
        'Signs point to yes.',
        
        // Neutral/uncertain responses
        'Reply hazy, try again.',
        'Ask again later.',
        'Better not tell you now.',
        'Cannot predict now.',
        'Concentrate and ask again.',
        
        // Negative responses
        'Don\'t count on it.',
        'My reply is no.',
        'My sources say no.',
        'Outlook not so good.',
        'Very doubtful.',
        
        // Fun/creative responses
        'The stars say yes.',
        'The universe is uncertain.',
        'Magic is in the air... and it says maybe.',
        'The crystal ball is cloudy.',
        'Even I don\'t know that one!',
        'That\'s a tough one... yes!',
        'That\'s a tough one... no!',
        'The answer lies within you.',
        'Absolutely!',
        'Not in a million years.',
        'Perhaps...',
        'The odds are in your favor.',
        'I wouldn\'t bet on it.',
        'Definitely maybe.',
        'The magic 8-ball has spoken: Yes!',
        'The magic 8-ball has spoken: No!',
        'Ask your heart, not me.',
        'The answer is written in the stars.',
        'Time will tell.',
        'Only you can decide that.'
      ];

      // Get random response
      const response = responses[Math.floor(Math.random() * responses.length)];
      
      // Determine response color based on type
      let color = client.config.bot.embedColors.primary;
      
      if (response.toLowerCase().includes('yes') || 
          response.toLowerCase().includes('certain') || 
          response.toLowerCase().includes('definitely') ||
          response.toLowerCase().includes('absolutely')) {
        color = client.config.bot.embedColors.success;
      } else if (response.toLowerCase().includes('no') || 
                 response.toLowerCase().includes('doubt') || 
                 response.toLowerCase().includes('not')) {
        color = client.config.bot.embedColors.error;
      } else if (response.toLowerCase().includes('maybe') || 
                 response.toLowerCase().includes('uncertain') || 
                 response.toLowerCase().includes('hazy') ||
                 response.toLowerCase().includes('later')) {
        color = client.config.bot.embedColors.warning;
      }

      const embed = embeds.createBaseEmbed(color)
        .setTitle('🎱 Magic 8-Ball')
        .addFields(
          { name: 'Question', value: question, inline: false },
          { name: 'Answer', value: `*${response}*`, inline: false }
        )
        .setThumbnail('https://cdn.discordapp.com/attachments/123456789/123456789/8ball.png'); // You can add an actual 8-ball image URL

      if (isSlash) {
        return messageOrInteraction.reply({ embeds: [embed] });
      } else {
        return messageOrInteraction.reply({ embeds: [embed] });
      }

    } catch (error) {
      client.logger.error('Error in 8ball command:', error);
      
      const embed = embeds.error('Error', 'The magic 8-ball is having technical difficulties!');
      
      if (messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.()) {
        if (messageOrInteraction.replied || messageOrInteraction.deferred) {
          return messageOrInteraction.followUp({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        }
      } else {
        return messageOrInteraction.reply({ embeds: [embed] });
      }
    }
  }
};
