const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Poll = sequelize.define('Poll', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    guildId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'guilds',
        key: 'id'
      }
    },
    channelId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    messageId: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    creatorId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    question: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    options: {
      type: DataTypes.JSON,
      allowNull: false
    },
    votes: {
      type: DataTypes.JSON,
      defaultValue: {},
      allowNull: false
    },
    allowMultiple: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    anonymous: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    endsAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    ended: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    }
  }, {
    tableName: 'polls',
    timestamps: true,
    indexes: [
      {
        fields: ['guildId']
      },
      {
        fields: ['messageId']
      },
      {
        fields: ['endsAt']
      },
      {
        fields: ['ended']
      },
      {
        fields: ['active']
      }
    ]
  });

  Poll.associate = (models) => {
    Poll.belongsTo(models.Guild, {
      foreignKey: 'guildId',
      as: 'guild'
    });
  };

  return Poll;
};
