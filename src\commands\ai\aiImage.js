/**
 * AI Image Analysis Command for NexusBot
 * Uses Hugging Face vision models to analyze images
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { SlashCommandBuilder, EmbedBuilder, AttachmentBuilder } = require('discord.js');
const { HfInference } = require('@huggingface/inference');
const axios = require('axios');
const config = require('../../config/config');
const logger = require('../../utils/logger');

module.exports = {
  name: 'aiimage',
  description: 'Analyze images using AI',
  category: 'ai',
  cooldown: 10,
  
  data: new SlashCommandBuilder()
    .setName('aiimage')
    .setDescription('Analyze an image using AI')
    .addAttachmentOption(option =>
      option.setName('image')
        .setDescription('Image to analyze')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('analysis_type')
        .setDescription('Type of analysis to perform')
        .addChoices(
          { name: 'Describe Image', value: 'describe' },
          { name: 'Detect Objects', value: 'objects' },
          { name: 'Content Safety', value: 'safety' },
          { name: 'OCR (Read Text)', value: 'ocr' },
          { name: 'Generate Caption', value: 'caption' }
        )
        .setRequired(false))
    .addStringOption(option =>
      option.setName('question')
        .setDescription('Ask a specific question about the image')
        .setRequired(false)
        .setMaxLength(200)),

  async execute(interaction) {
    if (!config.ai.enabled || !config.apis.huggingface.apiKey) {
      return interaction.reply({
        content: '❌ AI image analysis features are not enabled or configured.',
        ephemeral: true
      });
    }

    const timer = logger.startTimer('ai_image_analysis');
    
    try {
      await interaction.deferReply();

      const imageAttachment = interaction.options.getAttachment('image');
      const analysisType = interaction.options.getString('analysis_type') || 'describe';
      const question = interaction.options.getString('question');

      // Validate image
      if (!this.isValidImage(imageAttachment)) {
        return interaction.editReply({
          content: '❌ Please provide a valid image file (PNG, JPG, JPEG, GIF, WebP).'
        });
      }

      // Check file size (limit to 10MB)
      if (imageAttachment.size > 10 * 1024 * 1024) {
        return interaction.editReply({
          content: '❌ Image file is too large. Please use an image smaller than 10MB.'
        });
      }

      // Download and process image
      const imageBuffer = await this.downloadImage(imageAttachment.url);
      
      // Initialize Hugging Face Inference
      const hf = new HfInference(config.apis.huggingface.apiKey);

      // Perform analysis based on type
      let analysisResult;
      switch (analysisType) {
        case 'describe':
          analysisResult = await this.describeImage(hf, imageBuffer);
          break;
        case 'objects':
          analysisResult = await this.detectObjects(hf, imageBuffer);
          break;
        case 'safety':
          analysisResult = await this.analyzeSafety(hf, imageBuffer);
          break;
        case 'ocr':
          analysisResult = await this.performOCR(hf, imageBuffer);
          break;
        case 'caption':
          analysisResult = await this.generateCaption(hf, imageBuffer);
          break;
        default:
          analysisResult = await this.describeImage(hf, imageBuffer);
      }

      // Handle custom questions
      if (question && analysisType === 'describe') {
        analysisResult = await this.answerQuestion(hf, imageBuffer, question);
      }

      // Create response embed
      const responseEmbed = new EmbedBuilder()
        .setColor(0x00D4FF)
        .setTitle(`🤖 AI Image Analysis: ${this.getAnalysisTypeName(analysisType)}`)
        .setImage(imageAttachment.url)
        .setTimestamp();

      if (question) {
        responseEmbed.addFields({
          name: '❓ Your Question',
          value: question,
          inline: false
        });
      }

      responseEmbed.addFields({
        name: '🔍 Analysis Result',
        value: analysisResult.result || 'No analysis result available.',
        inline: false
      });

      if (analysisResult.confidence) {
        responseEmbed.addFields({
          name: '📊 Confidence',
          value: `${(analysisResult.confidence * 100).toFixed(1)}%`,
          inline: true
        });
      }

      if (analysisResult.model) {
        responseEmbed.setFooter({
          text: `Model: ${analysisResult.model}`
        });
      }

      await interaction.editReply({ embeds: [responseEmbed] });

      // Log the analysis
      logger.info('AI image analysis completed', {
        user: interaction.user.id,
        guild: interaction.guild?.id,
        analysisType,
        imageSize: imageAttachment.size,
        hasQuestion: !!question,
        model: analysisResult.model
      });

      timer.end({ success: true, analysisType, model: analysisResult.model });

    } catch (error) {
      logger.errorWithContext(error, {
        command: 'aiimage',
        guild: interaction.guild?.id,
        user: interaction.user?.id
      });

      const errorEmbed = new EmbedBuilder()
        .setColor(0xFF0000)
        .setTitle('🤖 AI Image Analysis Error')
        .setDescription('Sorry, I encountered an error while analyzing your image. Please try again.')
        .setTimestamp();

      await interaction.editReply({ embeds: [errorEmbed] });
      timer.end({ success: false, error: error.message });
    }
  },

  async downloadImage(url) {
    try {
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
        timeout: 30000,
        maxContentLength: 10 * 1024 * 1024 // 10MB limit
      });
      return Buffer.from(response.data);
    } catch (error) {
      throw new Error('Failed to download image: ' + error.message);
    }
  },

  async describeImage(hf, imageBuffer) {
    try {
      const result = await hf.imageToText({
        model: 'Salesforce/blip-image-captioning-large',
        data: imageBuffer
      });

      return {
        result: result.generated_text || 'Unable to generate description.',
        model: 'Salesforce/blip-image-captioning-large',
        confidence: 0.8
      };
    } catch (error) {
      logger.warn('Image description failed:', error.message);
      return {
        result: 'Unable to analyze image. The image might be too complex or the AI service is temporarily unavailable.',
        model: 'fallback',
        confidence: 0.1
      };
    }
  },

  async detectObjects(hf, imageBuffer) {
    try {
      const result = await hf.objectDetection({
        model: 'facebook/detr-resnet-50',
        data: imageBuffer
      });

      if (result && result.length > 0) {
        const objects = result
          .filter(obj => obj.score > 0.5)
          .map(obj => `${obj.label} (${(obj.score * 100).toFixed(1)}%)`)
          .slice(0, 10); // Limit to top 10 objects

        return {
          result: objects.length > 0 
            ? `Detected objects:\n• ${objects.join('\n• ')}`
            : 'No objects detected with high confidence.',
          model: 'facebook/detr-resnet-50',
          confidence: result[0]?.score || 0.5
        };
      }

      return {
        result: 'No objects detected in the image.',
        model: 'facebook/detr-resnet-50',
        confidence: 0.3
      };
    } catch (error) {
      logger.warn('Object detection failed:', error.message);
      return {
        result: 'Unable to detect objects in the image.',
        model: 'fallback',
        confidence: 0.1
      };
    }
  },

  async analyzeSafety(hf, imageBuffer) {
    try {
      // Use image classification for NSFW detection
      const result = await hf.imageClassification({
        model: 'Falconsai/nsfw_image_detection',
        data: imageBuffer
      });

      if (result && result.length > 0) {
        const nsfwResult = result.find(r => r.label.toLowerCase().includes('nsfw'));
        const safeResult = result.find(r => r.label.toLowerCase().includes('sfw') || r.label.toLowerCase().includes('safe'));

        const isNSFW = nsfwResult && nsfwResult.score > 0.7;
        const confidence = Math.max(nsfwResult?.score || 0, safeResult?.score || 0);

        return {
          result: isNSFW 
            ? `⚠️ **Warning**: This image may contain inappropriate content (${(nsfwResult.score * 100).toFixed(1)}% confidence)`
            : `✅ This image appears to be safe for work (${((safeResult?.score || 0.8) * 100).toFixed(1)}% confidence)`,
          model: 'Falconsai/nsfw_image_detection',
          confidence: confidence
        };
      }

      return {
        result: '✅ Image appears to be safe (no concerning content detected).',
        model: 'Falconsai/nsfw_image_detection',
        confidence: 0.6
      };
    } catch (error) {
      logger.warn('Safety analysis failed:', error.message);
      return {
        result: 'Unable to analyze image safety. Please review manually if needed.',
        model: 'fallback',
        confidence: 0.1
      };
    }
  },

  async performOCR(hf, imageBuffer) {
    try {
      // Note: OCR might not be available in all HF models, this is a placeholder
      return {
        result: 'OCR functionality is not currently available. Please try using an external OCR service.',
        model: 'placeholder',
        confidence: 0.1
      };
    } catch (error) {
      return {
        result: 'OCR analysis failed.',
        model: 'fallback',
        confidence: 0.1
      };
    }
  },

  async generateCaption(hf, imageBuffer) {
    // Same as describe but with different model if available
    return this.describeImage(hf, imageBuffer);
  },

  async answerQuestion(hf, imageBuffer, question) {
    try {
      // First get image description
      const description = await this.describeImage(hf, imageBuffer);
      
      // Then use the description to answer the question
      const answer = `Based on the image analysis: ${description.result}\n\nRegarding your question "${question}": I can see the image contains the described elements, but I cannot provide specific answers to questions about images yet. This feature is coming soon!`;

      return {
        result: answer,
        model: 'combined-analysis',
        confidence: 0.6
      };
    } catch (error) {
      return {
        result: 'Unable to answer question about the image.',
        model: 'fallback',
        confidence: 0.1
      };
    }
  },

  isValidImage(attachment) {
    if (!attachment) return false;
    
    const validTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp'];
    const validExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];
    
    return validTypes.includes(attachment.contentType) || 
           validExtensions.some(ext => attachment.name.toLowerCase().endsWith(ext));
  },

  getAnalysisTypeName(type) {
    const names = {
      describe: 'Image Description',
      objects: 'Object Detection',
      safety: 'Content Safety Check',
      ocr: 'Text Recognition (OCR)',
      caption: 'Caption Generation'
    };
    return names[type] || 'Image Analysis';
  }
};
