/**
 * Advanced Cache Manager for NexusBot
 * Provides multi-layer caching with Redis and in-memory fallback
 * <AUTHOR> Team
 * @version 2.0.0
 */

const NodeCache = require('node-cache');
const Redis = require('ioredis');
const logger = require('./logger');
const config = require('../config/config');

class CacheManager {
  constructor() {
    this.memoryCache = new NodeCache({
      stdTTL: 600, // 10 minutes default TTL
      checkperiod: 120, // Check for expired keys every 2 minutes
      useClones: false,
      deleteOnExpire: true,
      enableLegacyCallbacks: false,
      maxKeys: 10000
    });
    
    this.redisClient = null;
    this.isRedisAvailable = false;
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0
    };
  }

  /**
   * Initialize cache manager with Redis connection
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Try to connect to Redis if configured
      if (config.redis && config.redis.enabled) {
        this.redisClient = new Redis({
          host: config.redis.host || 'localhost',
          port: config.redis.port || 6379,
          password: config.redis.password,
          db: config.redis.db || 0,
          retryDelayOnFailover: 100,
          enableReadyCheck: true,
          maxRetriesPerRequest: 3,
          lazyConnect: true
        });

        await this.redisClient.connect();
        this.isRedisAvailable = true;
        logger.info('✅ Redis cache connected successfully');
      } else {
        logger.info('📝 Using in-memory cache only (Redis not configured)');
      }

      // Set up event listeners
      this.setupEventListeners();
      
    } catch (error) {
      logger.warn('⚠️ Redis connection failed, falling back to memory cache:', error.message);
      this.isRedisAvailable = false;
    }
  }

  /**
   * Set up event listeners for cache monitoring
   */
  setupEventListeners() {
    if (this.redisClient) {
      this.redisClient.on('error', (error) => {
        logger.error('Redis error:', error);
        this.stats.errors++;
        this.isRedisAvailable = false;
      });

      this.redisClient.on('connect', () => {
        logger.info('Redis reconnected');
        this.isRedisAvailable = true;
      });
    }

    // Memory cache events
    this.memoryCache.on('expired', (key, value) => {
      logger.debug(`Cache key expired: ${key}`);
    });

    this.memoryCache.on('set', (key, value) => {
      this.stats.sets++;
    });

    this.memoryCache.on('del', (key, value) => {
      this.stats.deletes++;
    });
  }

  /**
   * Get value from cache (Redis first, then memory)
   * @param {string} key - Cache key
   * @returns {Promise<any>} Cached value or null
   */
  async get(key) {
    try {
      // Try Redis first if available
      if (this.isRedisAvailable && this.redisClient) {
        const redisValue = await this.redisClient.get(key);
        if (redisValue !== null) {
          this.stats.hits++;
          return JSON.parse(redisValue);
        }
      }

      // Fallback to memory cache
      const memoryValue = this.memoryCache.get(key);
      if (memoryValue !== undefined) {
        this.stats.hits++;
        return memoryValue;
      }

      this.stats.misses++;
      return null;
    } catch (error) {
      logger.error('Cache get error:', error);
      this.stats.errors++;
      return null;
    }
  }

  /**
   * Set value in cache (both Redis and memory)
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<boolean>} Success status
   */
  async set(key, value, ttl = 600) {
    try {
      // Set in Redis if available
      if (this.isRedisAvailable && this.redisClient) {
        await this.redisClient.setex(key, ttl, JSON.stringify(value));
      }

      // Always set in memory cache as fallback
      this.memoryCache.set(key, value, ttl);
      this.stats.sets++;
      return true;
    } catch (error) {
      logger.error('Cache set error:', error);
      this.stats.errors++;
      return false;
    }
  }

  /**
   * Delete value from cache
   * @param {string} key - Cache key
   * @returns {Promise<boolean>} Success status
   */
  async delete(key) {
    try {
      // Delete from Redis if available
      if (this.isRedisAvailable && this.redisClient) {
        await this.redisClient.del(key);
      }

      // Delete from memory cache
      this.memoryCache.del(key);
      this.stats.deletes++;
      return true;
    } catch (error) {
      logger.error('Cache delete error:', error);
      this.stats.errors++;
      return false;
    }
  }

  /**
   * Check if key exists in cache
   * @param {string} key - Cache key
   * @returns {Promise<boolean>} Existence status
   */
  async has(key) {
    try {
      // Check Redis first if available
      if (this.isRedisAvailable && this.redisClient) {
        const exists = await this.redisClient.exists(key);
        if (exists) return true;
      }

      // Check memory cache
      return this.memoryCache.has(key);
    } catch (error) {
      logger.error('Cache has error:', error);
      return false;
    }
  }

  /**
   * Clear all cache data
   * @returns {Promise<void>}
   */
  async clear() {
    try {
      // Clear Redis if available
      if (this.isRedisAvailable && this.redisClient) {
        await this.redisClient.flushdb();
      }

      // Clear memory cache
      this.memoryCache.flushAll();
      logger.info('Cache cleared successfully');
    } catch (error) {
      logger.error('Cache clear error:', error);
    }
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getStats() {
    const memoryStats = this.memoryCache.getStats();
    return {
      ...this.stats,
      memoryKeys: memoryStats.keys,
      memoryHits: memoryStats.hits,
      memoryMisses: memoryStats.misses,
      redisAvailable: this.isRedisAvailable,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0
    };
  }

  /**
   * Get or set pattern - get from cache or execute function and cache result
   * @param {string} key - Cache key
   * @param {Function} fn - Function to execute if cache miss
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<any>} Cached or computed value
   */
  async getOrSet(key, fn, ttl = 600) {
    const cached = await this.get(key);
    if (cached !== null) {
      return cached;
    }

    try {
      const result = await fn();
      await this.set(key, result, ttl);
      return result;
    } catch (error) {
      logger.error('Cache getOrSet error:', error);
      throw error;
    }
  }

  /**
   * Increment a numeric value in cache
   * @param {string} key - Cache key
   * @param {number} amount - Amount to increment
   * @returns {Promise<number>} New value
   */
  async increment(key, amount = 1) {
    try {
      if (this.isRedisAvailable && this.redisClient) {
        return await this.redisClient.incrby(key, amount);
      }

      const current = await this.get(key) || 0;
      const newValue = current + amount;
      await this.set(key, newValue);
      return newValue;
    } catch (error) {
      logger.error('Cache increment error:', error);
      return 0;
    }
  }

  /**
   * Set expiration for a key
   * @param {string} key - Cache key
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<boolean>} Success status
   */
  async expire(key, ttl) {
    try {
      if (this.isRedisAvailable && this.redisClient) {
        await this.redisClient.expire(key, ttl);
      }

      this.memoryCache.ttl(key, ttl);
      return true;
    } catch (error) {
      logger.error('Cache expire error:', error);
      return false;
    }
  }

  /**
   * Close cache connections
   * @returns {Promise<void>}
   */
  async close() {
    try {
      if (this.redisClient) {
        await this.redisClient.quit();
      }
      this.memoryCache.close();
      logger.info('Cache manager closed successfully');
    } catch (error) {
      logger.error('Error closing cache manager:', error);
    }
  }
}

module.exports = new CacheManager();
