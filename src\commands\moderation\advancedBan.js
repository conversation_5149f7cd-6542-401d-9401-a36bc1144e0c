/**
 * Advanced Ban Command for NexusBot
 * Enhanced ban command with multiple options and logging
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder } = require('discord.js');
const logger = require('../../utils/logger');

module.exports = {
  name: 'advancedban',
  description: 'Advanced ban command with multiple options',
  category: 'moderation',
  permissions: [PermissionFlagsBits.BanMembers],
  cooldown: 5,
  
  data: new SlashCommandBuilder()
    .setName('advancedban')
    .setDescription('Ban a user with advanced options')
    .addUserOption(option =>
      option.setName('user')
        .setDescription('The user to ban')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('reason')
        .setDescription('Reason for the ban')
        .setRequired(false))
    .addIntegerOption(option =>
      option.setName('delete_days')
        .setDescription('Number of days of messages to delete (0-7)')
        .setMinValue(0)
        .setMaxValue(7)
        .setRequired(false))
    .addStringOption(option =>
      option.setName('duration')
        .setDescription('Temporary ban duration (e.g., 1h, 1d, 1w)')
        .setRequired(false))
    .addBooleanOption(option =>
      option.setName('silent')
        .setDescription('Whether to send a DM to the user')
        .setRequired(false))
    .addStringOption(option =>
      option.setName('appeal_url')
        .setDescription('URL for ban appeals')
        .setRequired(false)),

  async execute(interaction) {
    const timer = logger.startTimer('advancedban_command');
    
    try {
      const target = interaction.options.getUser('user');
      const reason = interaction.options.getString('reason') || 'No reason provided';
      const deleteDays = interaction.options.getInteger('delete_days') || 0;
      const duration = interaction.options.getString('duration');
      const silent = interaction.options.getBoolean('silent') || false;
      const appealUrl = interaction.options.getString('appeal_url');

      // Check if user can be banned
      const member = await interaction.guild.members.fetch(target.id).catch(() => null);
      
      if (member) {
        // Check hierarchy
        if (member.roles.highest.position >= interaction.member.roles.highest.position) {
          return interaction.reply({
            content: '❌ You cannot ban this user due to role hierarchy.',
            ephemeral: true
          });
        }

        // Check if user is bannable
        if (!member.bannable) {
          return interaction.reply({
            content: '❌ I cannot ban this user. Check my permissions and role hierarchy.',
            ephemeral: true
          });
        }
      }

      // Parse duration if provided
      let tempBan = false;
      let unbanTime = null;
      if (duration) {
        const durationMs = this.parseDuration(duration);
        if (durationMs) {
          tempBan = true;
          unbanTime = new Date(Date.now() + durationMs);
        }
      }

      // Create ban embed for logging
      const banEmbed = new EmbedBuilder()
        .setColor(0xFF0000)
        .setTitle('🔨 User Banned')
        .setThumbnail(target.displayAvatarURL())
        .addFields(
          { name: 'User', value: `${target.tag} (${target.id})`, inline: true },
          { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
          { name: 'Reason', value: reason, inline: false }
        )
        .setTimestamp();

      if (tempBan) {
        banEmbed.addFields({
          name: 'Duration',
          value: `Until <t:${Math.floor(unbanTime.getTime() / 1000)}:F>`,
          inline: true
        });
      }

      if (deleteDays > 0) {
        banEmbed.addFields({
          name: 'Messages Deleted',
          value: `${deleteDays} day(s) of messages`,
          inline: true
        });
      }

      // Send DM to user if not silent
      if (!silent && member) {
        try {
          const dmEmbed = new EmbedBuilder()
            .setColor(0xFF0000)
            .setTitle(`You have been banned from ${interaction.guild.name}`)
            .addFields(
              { name: 'Reason', value: reason, inline: false },
              { name: 'Moderator', value: interaction.user.tag, inline: true }
            )
            .setTimestamp();

          if (tempBan) {
            dmEmbed.addFields({
              name: 'Duration',
              value: `Until <t:${Math.floor(unbanTime.getTime() / 1000)}:F>`,
              inline: true
            });
          }

          if (appealUrl) {
            dmEmbed.addFields({
              name: 'Appeal',
              value: `You can appeal this ban at: ${appealUrl}`,
              inline: false
            });
          }

          await target.send({ embeds: [dmEmbed] });
        } catch (error) {
          logger.warn('Could not send DM to banned user:', error.message);
        }
      }

      // Execute the ban
      await interaction.guild.bans.create(target.id, {
        reason: `${reason} | Moderator: ${interaction.user.tag}`,
        deleteMessageDays: deleteDays
      });

      // Store temporary ban info if needed
      if (tempBan) {
        const dbManager = require('../../database');
        await dbManager.createTempBan({
          guildId: interaction.guild.id,
          userId: target.id,
          moderatorId: interaction.user.id,
          reason: reason,
          unbanTime: unbanTime,
          appealUrl: appealUrl
        });
      }

      // Log the moderation action
      const dbManager = require('../../database');
      await dbManager.logModerationAction({
        guildId: interaction.guild.id,
        moderatorId: interaction.user.id,
        targetId: target.id,
        action: tempBan ? 'tempban' : 'ban',
        reason: reason,
        duration: tempBan ? duration : null,
        metadata: {
          deleteDays: deleteDays,
          silent: silent,
          appealUrl: appealUrl
        }
      });

      // Send confirmation
      await interaction.reply({
        embeds: [banEmbed],
        ephemeral: false
      });

      // Send to moderation log channel
      const guildSettings = await dbManager.getOrCreateGuild(interaction.guild.id);
      if (guildSettings.settings?.modLogChannel) {
        const logChannel = interaction.guild.channels.cache.get(guildSettings.settings.modLogChannel);
        if (logChannel) {
          await logChannel.send({ embeds: [banEmbed] });
        }
      }

      // Log to system
      logger.moderation('ban', interaction.user.id, target.id, reason, interaction.guild.id);

      timer.end({ success: true, tempBan, deleteDays });

    } catch (error) {
      logger.errorWithContext(error, {
        command: 'advancedban',
        guild: interaction.guild?.id,
        user: interaction.user?.id
      });

      const errorEmbed = new EmbedBuilder()
        .setColor(0xFF0000)
        .setTitle('❌ Error')
        .setDescription('An error occurred while executing the ban command.')
        .setTimestamp();

      await interaction.reply({
        embeds: [errorEmbed],
        ephemeral: true
      });

      timer.end({ success: false, error: error.message });
    }
  },

  // Helper method to parse duration strings
  parseDuration(durationStr) {
    const regex = /^(\d+)([smhdw])$/i;
    const match = durationStr.match(regex);
    
    if (!match) return null;
    
    const value = parseInt(match[1]);
    const unit = match[2].toLowerCase();
    
    const multipliers = {
      s: 1000,
      m: 60 * 1000,
      h: 60 * 60 * 1000,
      d: 24 * 60 * 60 * 1000,
      w: 7 * 24 * 60 * 60 * 1000
    };
    
    return value * multipliers[unit];
  }
};
