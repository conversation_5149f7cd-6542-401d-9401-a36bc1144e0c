/**
 * AI Moderation Command for NexusBot
 * Uses AI to analyze and moderate content
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder } = require('discord.js');
const OpenAI = require('openai');
const config = require('../../config/config');
const logger = require('../../utils/logger');

module.exports = {
  name: 'aimoderate',
  description: 'Use AI to analyze content for moderation',
  category: 'ai',
  permissions: [PermissionFlagsBits.ManageMessages],
  cooldown: 10,
  
  data: new SlashCommandBuilder()
    .setName('aimoderate')
    .setDescription('Analyze content using AI moderation')
    .addStringOption(option =>
      option.setName('content')
        .setDescription('Content to analyze')
        .setRequired(true)
        .setMaxLength(2000))
    .addStringOption(option =>
      option.setName('action')
        .setDescription('Action to take if content violates rules')
        .addChoices(
          { name: 'Analyze Only', value: 'analyze' },
          { name: 'Delete Message', value: 'delete' },
          { name: 'Warn User', value: 'warn' },
          { name: 'Timeout User', value: 'timeout' }
        )
        .setRequired(false))
    .addUserOption(option =>
      option.setName('user')
        .setDescription('User who posted the content (for actions)')
        .setRequired(false)),

  async execute(interaction) {
    if (!config.ai.enabled || !config.apis.openai.apiKey) {
      return interaction.reply({
        content: '❌ AI features are not enabled or configured.',
        ephemeral: true
      });
    }

    const timer = logger.startTimer('ai_moderation');
    
    try {
      await interaction.deferReply({ ephemeral: true });

      const content = interaction.options.getString('content');
      const action = interaction.options.getString('action') || 'analyze';
      const targetUser = interaction.options.getUser('user');

      // Initialize OpenAI
      const openai = new OpenAI({
        apiKey: config.apis.openai.apiKey
      });

      // Analyze content with AI
      const analysisResult = await this.analyzeContent(openai, content);

      // Create analysis embed
      const analysisEmbed = new EmbedBuilder()
        .setColor(analysisResult.violates ? 0xFF0000 : 0x00FF00)
        .setTitle('🤖 AI Content Analysis')
        .addFields(
          { name: 'Content', value: `\`\`\`${content.substring(0, 1000)}\`\`\``, inline: false },
          { name: 'Violates Rules', value: analysisResult.violates ? '❌ Yes' : '✅ No', inline: true },
          { name: 'Confidence', value: `${(analysisResult.confidence * 100).toFixed(1)}%`, inline: true },
          { name: 'Category', value: analysisResult.category || 'None', inline: true }
        )
        .setTimestamp();

      if (analysisResult.reasoning) {
        analysisEmbed.addFields({
          name: 'AI Reasoning',
          value: analysisResult.reasoning.substring(0, 1024),
          inline: false
        });
      }

      // Take action if content violates rules and action is specified
      let actionTaken = false;
      if (analysisResult.violates && action !== 'analyze' && targetUser) {
        actionTaken = await this.takeAction(interaction, action, targetUser, analysisResult);
        
        if (actionTaken) {
          analysisEmbed.addFields({
            name: 'Action Taken',
            value: `${this.getActionDescription(action)} applied to ${targetUser.tag}`,
            inline: false
          });
        }
      }

      // Log the AI moderation
      logger.automod('ai_analysis', targetUser?.id || 'unknown', interaction.guild.id, 'ai_moderation', {
        content: content.substring(0, 100),
        violates: analysisResult.violates,
        confidence: analysisResult.confidence,
        category: analysisResult.category,
        actionTaken: actionTaken ? action : 'none'
      });

      await interaction.editReply({ embeds: [analysisEmbed] });

      timer.end({ 
        success: true, 
        violates: analysisResult.violates, 
        confidence: analysisResult.confidence,
        actionTaken 
      });

    } catch (error) {
      logger.errorWithContext(error, {
        command: 'aimoderate',
        guild: interaction.guild?.id,
        user: interaction.user?.id
      });

      const errorEmbed = new EmbedBuilder()
        .setColor(0xFF0000)
        .setTitle('❌ AI Analysis Error')
        .setDescription('An error occurred while analyzing the content.')
        .setTimestamp();

      await interaction.editReply({ embeds: [errorEmbed] });
      timer.end({ success: false, error: error.message });
    }
  },

  async analyzeContent(openai, content) {
    try {
      const prompt = `
Analyze the following content for potential rule violations in a Discord server. 
Consider toxicity, harassment, spam, inappropriate content, hate speech, and other common Discord rule violations.

Content to analyze: "${content}"

Respond with a JSON object containing:
- violates: boolean (true if content violates rules)
- confidence: number (0-1, confidence in the assessment)
- category: string (type of violation if any: "toxicity", "harassment", "spam", "inappropriate", "hate_speech", "other", or null)
- reasoning: string (brief explanation of the decision)

Be strict but fair in your assessment. Consider context and intent.
`;

      const response = await openai.chat.completions.create({
        model: config.apis.openai.model,
        messages: [
          {
            role: 'system',
            content: 'You are an AI content moderator for Discord servers. Analyze content objectively and provide structured responses.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: config.apis.openai.maxTokens,
        temperature: 0.3
      });

      const result = JSON.parse(response.choices[0].message.content);
      
      // Validate and sanitize the response
      return {
        violates: Boolean(result.violates),
        confidence: Math.max(0, Math.min(1, Number(result.confidence) || 0)),
        category: result.category || null,
        reasoning: String(result.reasoning || '').substring(0, 500)
      };

    } catch (error) {
      logger.error('AI analysis failed:', error);
      
      // Fallback to basic keyword detection
      return this.fallbackAnalysis(content);
    }
  },

  fallbackAnalysis(content) {
    const toxicKeywords = [
      'hate', 'kill', 'die', 'stupid', 'idiot', 'retard', 'gay', 'fag',
      'nazi', 'hitler', 'terrorist', 'bomb', 'shoot', 'murder'
    ];

    const lowerContent = content.toLowerCase();
    const foundKeywords = toxicKeywords.filter(keyword => 
      lowerContent.includes(keyword)
    );

    return {
      violates: foundKeywords.length > 0,
      confidence: foundKeywords.length > 0 ? 0.7 : 0.3,
      category: foundKeywords.length > 0 ? 'toxicity' : null,
      reasoning: foundKeywords.length > 0 
        ? `Detected potentially toxic keywords: ${foundKeywords.join(', ')}`
        : 'No obvious rule violations detected'
    };
  },

  async takeAction(interaction, action, targetUser, analysisResult) {
    try {
      const member = await interaction.guild.members.fetch(targetUser.id);
      if (!member) return false;

      const reason = `AI Moderation: ${analysisResult.category} (${(analysisResult.confidence * 100).toFixed(1)}% confidence)`;

      switch (action) {
        case 'delete':
          // This would typically delete the original message
          // For this command, we'll just log it
          logger.moderation('ai_delete', interaction.user.id, targetUser.id, reason, interaction.guild.id);
          return true;

        case 'warn':
          const dbManager = require('../../database');
          await dbManager.addWarning(
            targetUser.id,
            interaction.guild.id,
            interaction.user.id,
            reason
          );
          
          // Send DM to user
          try {
            const warnEmbed = new EmbedBuilder()
              .setColor(0xFFFF00)
              .setTitle('⚠️ Warning')
              .setDescription(`You have been warned in ${interaction.guild.name}`)
              .addFields({ name: 'Reason', value: reason })
              .setTimestamp();

            await targetUser.send({ embeds: [warnEmbed] });
          } catch (error) {
            logger.warn('Could not send warning DM:', error.message);
          }

          logger.moderation('ai_warn', interaction.user.id, targetUser.id, reason, interaction.guild.id);
          return true;

        case 'timeout':
          const timeoutDuration = 5 * 60 * 1000; // 5 minutes
          await member.timeout(timeoutDuration, reason);
          
          logger.moderation('ai_timeout', interaction.user.id, targetUser.id, reason, interaction.guild.id);
          return true;

        default:
          return false;
      }
    } catch (error) {
      logger.error('Failed to take moderation action:', error);
      return false;
    }
  },

  getActionDescription(action) {
    const descriptions = {
      delete: 'Message deleted',
      warn: 'Warning issued',
      timeout: '5-minute timeout'
    };
    return descriptions[action] || 'Unknown action';
  }
};
