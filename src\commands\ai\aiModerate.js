/**
 * AI Moderation Command for NexusBot
 * Uses Hugging Face AI models to analyze and moderate content
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder } = require('discord.js');
const { HfInference } = require('@huggingface/inference');
const config = require('../../config/config');
const logger = require('../../utils/logger');

module.exports = {
  name: 'aimoderate',
  description: 'Use AI to analyze content for moderation',
  category: 'ai',
  permissions: [PermissionFlagsBits.ManageMessages],
  cooldown: 10,
  
  data: new SlashCommandBuilder()
    .setName('aimoderate')
    .setDescription('Analyze content using AI moderation')
    .addStringOption(option =>
      option.setName('content')
        .setDescription('Content to analyze')
        .setRequired(true)
        .setMaxLength(2000))
    .addStringOption(option =>
      option.setName('action')
        .setDescription('Action to take if content violates rules')
        .addChoices(
          { name: 'Analyze Only', value: 'analyze' },
          { name: 'Delete Message', value: 'delete' },
          { name: 'Warn User', value: 'warn' },
          { name: 'Timeout User', value: 'timeout' }
        )
        .setRequired(false))
    .addUserOption(option =>
      option.setName('user')
        .setDescription('User who posted the content (for actions)')
        .setRequired(false)),

  async execute(interaction) {
    if (!config.ai.enabled || !config.apis.huggingface.apiKey) {
      return interaction.reply({
        content: '❌ AI features are not enabled or configured.',
        ephemeral: true
      });
    }

    const timer = logger.startTimer('ai_moderation');

    try {
      await interaction.deferReply({ ephemeral: true });

      const content = interaction.options.getString('content');
      const action = interaction.options.getString('action') || 'analyze';
      const targetUser = interaction.options.getUser('user');

      // Initialize Hugging Face Inference
      const hf = new HfInference(config.apis.huggingface.apiKey);

      // Analyze content with AI
      const analysisResult = await this.analyzeContent(hf, content);

      // Create analysis embed
      const analysisEmbed = new EmbedBuilder()
        .setColor(analysisResult.violates ? 0xFF0000 : 0x00FF00)
        .setTitle('🤖 AI Content Analysis')
        .addFields(
          { name: 'Content', value: `\`\`\`${content.substring(0, 1000)}\`\`\``, inline: false },
          { name: 'Violates Rules', value: analysisResult.violates ? '❌ Yes' : '✅ No', inline: true },
          { name: 'Confidence', value: `${(analysisResult.confidence * 100).toFixed(1)}%`, inline: true },
          { name: 'Category', value: analysisResult.category || 'None', inline: true }
        )
        .setTimestamp();

      if (analysisResult.reasoning) {
        analysisEmbed.addFields({
          name: 'AI Reasoning',
          value: analysisResult.reasoning.substring(0, 1024),
          inline: false
        });
      }

      // Take action if content violates rules and action is specified
      let actionTaken = false;
      if (analysisResult.violates && action !== 'analyze' && targetUser) {
        actionTaken = await this.takeAction(interaction, action, targetUser, analysisResult);
        
        if (actionTaken) {
          analysisEmbed.addFields({
            name: 'Action Taken',
            value: `${this.getActionDescription(action)} applied to ${targetUser.tag}`,
            inline: false
          });
        }
      }

      // Log the AI moderation
      logger.automod('ai_analysis', targetUser?.id || 'unknown', interaction.guild.id, 'ai_moderation', {
        content: content.substring(0, 100),
        violates: analysisResult.violates,
        confidence: analysisResult.confidence,
        category: analysisResult.category,
        actionTaken: actionTaken ? action : 'none'
      });

      await interaction.editReply({ embeds: [analysisEmbed] });

      timer.end({ 
        success: true, 
        violates: analysisResult.violates, 
        confidence: analysisResult.confidence,
        actionTaken 
      });

    } catch (error) {
      logger.errorWithContext(error, {
        command: 'aimoderate',
        guild: interaction.guild?.id,
        user: interaction.user?.id
      });

      const errorEmbed = new EmbedBuilder()
        .setColor(0xFF0000)
        .setTitle('❌ AI Analysis Error')
        .setDescription('An error occurred while analyzing the content.')
        .setTimestamp();

      await interaction.editReply({ embeds: [errorEmbed] });
      timer.end({ success: false, error: error.message });
    }
  },

  async analyzeContent(hf, content) {
    try {
      // Use multiple Hugging Face models for comprehensive analysis
      const analyses = await Promise.allSettled([
        this.analyzeToxicity(hf, content),
        this.analyzeSentiment(hf, content),
        this.analyzeClassification(hf, content)
      ]);

      // Combine results from different models
      const toxicityResult = analyses[0].status === 'fulfilled' ? analyses[0].value : null;
      const sentimentResult = analyses[1].status === 'fulfilled' ? analyses[1].value : null;
      const classificationResult = analyses[2].status === 'fulfilled' ? analyses[2].value : null;

      // Determine overall violation status
      let violates = false;
      let confidence = 0;
      let category = null;
      let reasoning = '';

      // Check toxicity
      if (toxicityResult && toxicityResult.score > 0.7) {
        violates = true;
        confidence = Math.max(confidence, toxicityResult.score);
        category = 'toxicity';
        reasoning += `High toxicity detected (${(toxicityResult.score * 100).toFixed(1)}%). `;
      }

      // Check sentiment (very negative might indicate harassment)
      if (sentimentResult && sentimentResult.label === 'NEGATIVE' && sentimentResult.score > 0.8) {
        if (!violates) {
          violates = true;
          confidence = sentimentResult.score * 0.6; // Lower confidence for sentiment alone
          category = 'harassment';
        }
        reasoning += `Very negative sentiment detected (${(sentimentResult.score * 100).toFixed(1)}%). `;
      }

      // Check classification results
      if (classificationResult) {
        const harmfulLabels = ['hate speech', 'harassment', 'toxic', 'inappropriate'];
        const harmfulMatch = classificationResult.find(result =>
          harmfulLabels.some(label => result.label.toLowerCase().includes(label)) && result.score > 0.6
        );

        if (harmfulMatch) {
          violates = true;
          confidence = Math.max(confidence, harmfulMatch.score);
          category = category || 'inappropriate';
          reasoning += `Classified as ${harmfulMatch.label} (${(harmfulMatch.score * 100).toFixed(1)}%). `;
        }
      }

      if (!reasoning) {
        reasoning = 'Content appears to be appropriate based on AI analysis.';
      }

      return {
        violates,
        confidence: Math.min(confidence, 1),
        category,
        reasoning: reasoning.trim()
      };

    } catch (error) {
      logger.error('Hugging Face AI analysis failed:', error);

      // Fallback to basic keyword detection
      return this.fallbackAnalysis(content);
    }
  },

  async analyzeToxicity(hf, content) {
    try {
      const result = await hf.textClassification({
        model: config.apis.huggingface.models.toxicity,
        inputs: content
      });

      // Find toxic classification
      const toxicResult = result.find(r => r.label.toLowerCase().includes('toxic'));
      return toxicResult || { label: 'non-toxic', score: 0 };
    } catch (error) {
      logger.warn('Toxicity analysis failed:', error.message);
      return null;
    }
  },

  async analyzeSentiment(hf, content) {
    try {
      const result = await hf.textClassification({
        model: config.apis.huggingface.models.sentiment,
        inputs: content
      });

      return result[0] || null;
    } catch (error) {
      logger.warn('Sentiment analysis failed:', error.message);
      return null;
    }
  },

  async analyzeClassification(hf, content) {
    try {
      // Use zero-shot classification to check for various harmful content types
      const candidateLabels = [
        'hate speech',
        'harassment',
        'toxic content',
        'inappropriate content',
        'spam',
        'normal conversation',
        'friendly message'
      ];

      const result = await hf.zeroShotClassification({
        model: config.apis.huggingface.models.classification,
        inputs: content,
        parameters: { candidate_labels: candidateLabels }
      });

      return result.labels.map((label, index) => ({
        label,
        score: result.scores[index]
      }));
    } catch (error) {
      logger.warn('Classification analysis failed:', error.message);
      return null;
    }
  },

  fallbackAnalysis(content) {
    const toxicKeywords = [
      'hate', 'kill', 'die', 'stupid', 'idiot', 'retard', 'gay', 'fag',
      'nazi', 'hitler', 'terrorist', 'bomb', 'shoot', 'murder'
    ];

    const lowerContent = content.toLowerCase();
    const foundKeywords = toxicKeywords.filter(keyword => 
      lowerContent.includes(keyword)
    );

    return {
      violates: foundKeywords.length > 0,
      confidence: foundKeywords.length > 0 ? 0.7 : 0.3,
      category: foundKeywords.length > 0 ? 'toxicity' : null,
      reasoning: foundKeywords.length > 0 
        ? `Detected potentially toxic keywords: ${foundKeywords.join(', ')}`
        : 'No obvious rule violations detected'
    };
  },

  async takeAction(interaction, action, targetUser, analysisResult) {
    try {
      const member = await interaction.guild.members.fetch(targetUser.id);
      if (!member) return false;

      const reason = `AI Moderation: ${analysisResult.category} (${(analysisResult.confidence * 100).toFixed(1)}% confidence)`;

      switch (action) {
        case 'delete':
          // This would typically delete the original message
          // For this command, we'll just log it
          logger.moderation('ai_delete', interaction.user.id, targetUser.id, reason, interaction.guild.id);
          return true;

        case 'warn':
          const dbManager = require('../../database');
          await dbManager.addWarning(
            targetUser.id,
            interaction.guild.id,
            interaction.user.id,
            reason
          );
          
          // Send DM to user
          try {
            const warnEmbed = new EmbedBuilder()
              .setColor(0xFFFF00)
              .setTitle('⚠️ Warning')
              .setDescription(`You have been warned in ${interaction.guild.name}`)
              .addFields({ name: 'Reason', value: reason })
              .setTimestamp();

            await targetUser.send({ embeds: [warnEmbed] });
          } catch (error) {
            logger.warn('Could not send warning DM:', error.message);
          }

          logger.moderation('ai_warn', interaction.user.id, targetUser.id, reason, interaction.guild.id);
          return true;

        case 'timeout':
          const timeoutDuration = 5 * 60 * 1000; // 5 minutes
          await member.timeout(timeoutDuration, reason);
          
          logger.moderation('ai_timeout', interaction.user.id, targetUser.id, reason, interaction.guild.id);
          return true;

        default:
          return false;
      }
    } catch (error) {
      logger.error('Failed to take moderation action:', error);
      return false;
    }
  },

  getActionDescription(action) {
    const descriptions = {
      delete: 'Message deleted',
      warn: 'Warning issued',
      timeout: '5-minute timeout'
    };
    return descriptions[action] || 'Unknown action';
  }
};
