/**
 * Advanced Rate Limiter for NexusBot
 * Provides flexible rate limiting with multiple strategies
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { RateLimiterMemory, RateLimiterRedis } = require('rate-limiter-flexible');
const logger = require('./logger');
const config = require('../config/config');

class RateLimiter {
  constructor() {
    this.limiters = new Map();
    this.isRedisAvailable = false;
    this.redisClient = null;
  }

  /**
   * Initialize rate limiter with Redis support
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if Redis is available for distributed rate limiting
      if (config.redis && config.redis.enabled) {
        const Redis = require('ioredis');
        this.redisClient = new Redis({
          host: config.redis.host || 'localhost',
          port: config.redis.port || 6379,
          password: config.redis.password,
          db: config.redis.db || 1, // Use different DB for rate limiting
          enableReadyCheck: true,
          lazyConnect: true
        });

        await this.redisClient.connect();
        this.isRedisAvailable = true;
        logger.info('✅ Redis rate limiter connected');
      }

      // Initialize default rate limiters
      this.setupDefaultLimiters();
      
    } catch (error) {
      logger.warn('⚠️ Redis rate limiter failed, using memory-based limiting:', error.message);
      this.isRedisAvailable = false;
      this.setupDefaultLimiters();
    }
  }

  /**
   * Set up default rate limiters for common use cases
   */
  setupDefaultLimiters() {
    const LimiterClass = this.isRedisAvailable ? RateLimiterRedis : RateLimiterMemory;
    const baseOptions = this.isRedisAvailable ? { storeClient: this.redisClient } : {};

    // Command rate limiter - general commands
    this.limiters.set('command', new LimiterClass({
      ...baseOptions,
      keyPrefix: 'rl_cmd',
      points: 10, // Number of requests
      duration: 60, // Per 60 seconds
      blockDuration: 60, // Block for 60 seconds if exceeded
    }));

    // Moderation command limiter - stricter for mod commands
    this.limiters.set('moderation', new LimiterClass({
      ...baseOptions,
      keyPrefix: 'rl_mod',
      points: 5, // Number of requests
      duration: 60, // Per 60 seconds
      blockDuration: 300, // Block for 5 minutes if exceeded
    }));

    // Music command limiter
    this.limiters.set('music', new LimiterClass({
      ...baseOptions,
      keyPrefix: 'rl_music',
      points: 15, // Number of requests
      duration: 60, // Per 60 seconds
      blockDuration: 30, // Block for 30 seconds if exceeded
    }));

    // Economy command limiter
    this.limiters.set('economy', new LimiterClass({
      ...baseOptions,
      keyPrefix: 'rl_econ',
      points: 20, // Number of requests
      duration: 60, // Per 60 seconds
      blockDuration: 60, // Block for 60 seconds if exceeded
    }));

    // API request limiter - for external API calls
    this.limiters.set('api', new LimiterClass({
      ...baseOptions,
      keyPrefix: 'rl_api',
      points: 100, // Number of requests
      duration: 3600, // Per hour
      blockDuration: 3600, // Block for 1 hour if exceeded
    }));

    // Message spam limiter
    this.limiters.set('message', new LimiterClass({
      ...baseOptions,
      keyPrefix: 'rl_msg',
      points: 30, // Number of messages
      duration: 60, // Per 60 seconds
      blockDuration: 300, // Block for 5 minutes if exceeded
    }));

    // Login attempt limiter
    this.limiters.set('login', new LimiterClass({
      ...baseOptions,
      keyPrefix: 'rl_login',
      points: 5, // Number of attempts
      duration: 900, // Per 15 minutes
      blockDuration: 3600, // Block for 1 hour if exceeded
    }));

    logger.info('✅ Rate limiters initialized');
  }

  /**
   * Create a custom rate limiter
   * @param {string} name - Limiter name
   * @param {Object} options - Rate limiter options
   * @returns {boolean} Success status
   */
  createLimiter(name, options) {
    try {
      const LimiterClass = this.isRedisAvailable ? RateLimiterRedis : RateLimiterMemory;
      const baseOptions = this.isRedisAvailable ? { storeClient: this.redisClient } : {};

      this.limiters.set(name, new LimiterClass({
        ...baseOptions,
        keyPrefix: `rl_${name}`,
        ...options
      }));

      logger.info(`Custom rate limiter '${name}' created`);
      return true;
    } catch (error) {
      logger.error(`Failed to create rate limiter '${name}':`, error);
      return false;
    }
  }

  /**
   * Check if action is allowed for a key
   * @param {string} limiterName - Name of the rate limiter
   * @param {string} key - Unique identifier (user ID, IP, etc.)
   * @param {number} points - Points to consume (default: 1)
   * @returns {Promise<Object>} Rate limit result
   */
  async checkLimit(limiterName, key, points = 1) {
    try {
      const limiter = this.limiters.get(limiterName);
      if (!limiter) {
        logger.warn(`Rate limiter '${limiterName}' not found`);
        return { allowed: true, remaining: Infinity };
      }

      const result = await limiter.consume(key, points);
      return {
        allowed: true,
        remaining: result.remainingPoints,
        resetTime: new Date(Date.now() + result.msBeforeNext),
        totalHits: result.totalHits
      };
    } catch (rejRes) {
      // Rate limit exceeded
      return {
        allowed: false,
        remaining: 0,
        resetTime: new Date(Date.now() + rejRes.msBeforeNext),
        totalHits: rejRes.totalHits,
        retryAfter: Math.round(rejRes.msBeforeNext / 1000)
      };
    }
  }

  /**
   * Get current status for a key without consuming points
   * @param {string} limiterName - Name of the rate limiter
   * @param {string} key - Unique identifier
   * @returns {Promise<Object>} Current status
   */
  async getStatus(limiterName, key) {
    try {
      const limiter = this.limiters.get(limiterName);
      if (!limiter) {
        return { remaining: Infinity, resetTime: null };
      }

      const result = await limiter.get(key);
      if (!result) {
        return { remaining: limiter.points, resetTime: null };
      }

      return {
        remaining: result.remainingPoints,
        resetTime: new Date(Date.now() + result.msBeforeNext),
        totalHits: result.totalHits
      };
    } catch (error) {
      logger.error('Error getting rate limit status:', error);
      return { remaining: 0, resetTime: null };
    }
  }

  /**
   * Reset rate limit for a key
   * @param {string} limiterName - Name of the rate limiter
   * @param {string} key - Unique identifier
   * @returns {Promise<boolean>} Success status
   */
  async resetLimit(limiterName, key) {
    try {
      const limiter = this.limiters.get(limiterName);
      if (!limiter) {
        return false;
      }

      await limiter.delete(key);
      return true;
    } catch (error) {
      logger.error('Error resetting rate limit:', error);
      return false;
    }
  }

  /**
   * Block a key for a specific duration
   * @param {string} limiterName - Name of the rate limiter
   * @param {string} key - Unique identifier
   * @param {number} duration - Block duration in seconds
   * @returns {Promise<boolean>} Success status
   */
  async blockKey(limiterName, key, duration) {
    try {
      const limiter = this.limiters.get(limiterName);
      if (!limiter) {
        return false;
      }

      await limiter.block(key, duration);
      return true;
    } catch (error) {
      logger.error('Error blocking key:', error);
      return false;
    }
  }

  /**
   * Get rate limiter statistics
   * @returns {Object} Statistics for all limiters
   */
  getStats() {
    const stats = {
      limiters: Array.from(this.limiters.keys()),
      redisAvailable: this.isRedisAvailable,
      totalLimiters: this.limiters.size
    };

    return stats;
  }

  /**
   * Middleware for Express.js rate limiting
   * @param {string} limiterName - Name of the rate limiter
   * @param {Function} keyGenerator - Function to generate key from request
   * @returns {Function} Express middleware
   */
  expressMiddleware(limiterName, keyGenerator = (req) => req.ip) {
    return async (req, res, next) => {
      try {
        const key = keyGenerator(req);
        const result = await this.checkLimit(limiterName, key);

        if (!result.allowed) {
          return res.status(429).json({
            error: 'Too Many Requests',
            retryAfter: result.retryAfter,
            resetTime: result.resetTime
          });
        }

        // Add rate limit headers
        res.set({
          'X-RateLimit-Limit': this.limiters.get(limiterName)?.points || 'Unknown',
          'X-RateLimit-Remaining': result.remaining,
          'X-RateLimit-Reset': result.resetTime?.toISOString() || 'Unknown'
        });

        next();
      } catch (error) {
        logger.error('Rate limiter middleware error:', error);
        next(); // Allow request to proceed on error
      }
    };
  }

  /**
   * Close rate limiter connections
   * @returns {Promise<void>}
   */
  async close() {
    try {
      if (this.redisClient) {
        await this.redisClient.quit();
      }
      logger.info('Rate limiter closed successfully');
    } catch (error) {
      logger.error('Error closing rate limiter:', error);
    }
  }
}

module.exports = new RateLimiter();
