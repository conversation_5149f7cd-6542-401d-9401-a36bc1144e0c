# NexusBot - Comprehensive Discord Bot

A feature-rich Discord bot built with Discord.js v14, featuring moderation, entertainment, utility, music, economy, and leveling systems. Uses Supabase as the primary database with automatic fallback to SQLite/PostgreSQL.

## 🌟 Features

### 🛡️ Moderation System
- Kick, ban, timeout, warn, and clear commands
- Automatic moderation logging
- Warning system with persistent storage
- Permission checks and role hierarchy validation
- Cooldown system to prevent spam

### 🎉 Community Engagement
- Poll system with reaction-based voting
- Announcement system with embed formatting
- Giveaway system with automatic winner selection
- Event scheduling integration

### 🎮 Entertainment & Games
- Magic 8-ball with randomized responses
- Dice rolling system (supports multiple dice and custom sides)
- Coin flip with animated responses
- Trivia system with multiple categories

### 🔧 Utility Tools
- Weather API integration
- Timezone conversion utilities
- Dictionary API for word definitions
- Translation service integration

### 🎵 Music System
- Voice channel integration for music playback
- YouTube and Spotify URL support
- Queue management with add, remove, and shuffle
- Audio controls (play, pause, skip, stop, volume)

### 💰 Economy & Leveling
- Virtual currency system with daily rewards
- XP/leveling system based on message activity
- Shop system with purchasable roles and items
- Leaderboards for both economy and levels

### ⚙️ Configuration System
- Per-server settings storage
- Command enable/disable functionality
- Custom prefix support
- Moderation channel configuration

### 📊 Analytics Dashboard
- Server statistics and activity metrics
- User activity tracking
- Bot performance metrics

## 🚀 Quick Start

### Prerequisites
- Node.js 18.0.0 or higher
- npm 8.0.0 or higher
- Discord Bot Token
- Supabase Account (recommended) or PostgreSQL/SQLite

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/nexusbot.git
   cd nexusbot
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and fill in your configuration:
   ```env
   # Discord Bot Configuration
   DISCORD_TOKEN=your_discord_bot_token_here
   CLIENT_ID=your_discord_application_id_here
   
   # Supabase Configuration (Primary Database)
   SUPABASE_URL=https://your-project-ref.supabase.co
   SUPABASE_ANON_KEY=your_supabase_anon_key_here
   SUPABASE_SERVICE_KEY=your_supabase_service_role_key_here
   
   # API Keys (Optional)
   OPENWEATHER_API_KEY=your_openweather_api_key_here
   YOUTUBE_API_KEY=your_youtube_api_key_here
   ```

4. **Start the bot**
   ```bash
   # Development mode with auto-restart
   npm run dev
   
   # Production mode
   npm start
   ```

## 🗄️ Database Setup

### Supabase (Recommended)
NexusBot automatically creates the required tables in your Supabase project. The database schema includes:

- **guilds** - Server configurations and settings
- **users** - User profiles and preferences
- **economy** - Virtual currency and transactions
- **levels** - XP and leveling data
- **warnings** - Moderation warnings
- **moderation_logs** - Moderation action history
- **giveaways** - Giveaway management
- **polls** - Poll system data

### Local Database (Fallback)
If Supabase is not configured, the bot will automatically fall back to SQLite for development or PostgreSQL for production.

## 🎨 Visual Design

NexusBot uses a consistent color palette across all embeds:
- **Primary**: Electric Blue (#00D4FF)
- **Secondary**: Deep Purple (#6B46C1)
- **Accent**: Neon Green (#00FF88)
- **Background**: Dark Gray (#1A1A1A)

## 📝 Commands

### Utility Commands
- `!help [command]` - Show help information
- `!ping` - Check bot latency and uptime

### Moderation Commands
- `!kick <user> [reason]` - Kick a member from the server
- `!ban <user> [reason]` - Ban a member from the server
- `!warn <user> <reason>` - Warn a member
- `!warnings <user>` - View warnings for a user
- `!clear <amount>` - Clear messages from a channel

### Entertainment Commands
- `!8ball <question>` - Ask the magic 8-ball
- `!dice [XdY]` - Roll dice (e.g., 2d6, 1d20)
- `!coinflip` - Flip a coin

### Economy Commands
- `!balance [user]` - Check balance
- `!daily` - Claim daily reward
- `!pay <user> <amount>` - Transfer money
- `!leaderboard` - View economy leaderboard

### Music Commands
- `!play <song>` - Play a song
- `!queue` - View the music queue
- `!skip` - Skip current song
- `!stop` - Stop music and clear queue

## 🔧 Configuration

### Server Settings
Use the configuration commands to customize NexusBot for your server:

```bash
!prefix <new_prefix>          # Change command prefix
!modchannel <channel>         # Set moderation log channel
!welcome <channel> [message]  # Configure welcome system
!autorole <role>              # Set automatic role for new members
```

### Command Management
```bash
!disable <command>    # Disable a command
!enable <command>     # Enable a command
!commands             # List all available commands
```

## 🚀 Deployment

### Railway (Recommended)
1. Fork this repository
2. Connect your GitHub account to Railway
3. Deploy from your forked repository
4. Add environment variables in Railway dashboard

### Heroku
1. Create a new Heroku app
2. Connect your GitHub repository
3. Add environment variables in Heroku dashboard
4. Deploy from GitHub

### VPS/Self-Hosted
1. Clone the repository on your server
2. Install Node.js and npm
3. Configure environment variables
4. Use PM2 for process management:
   ```bash
   npm install -g pm2
   pm2 start src/index.js --name "nexusbot"
   pm2 startup
   pm2 save
   ```

## 🔒 Security

- Never share your bot token or service keys
- Use environment variables for all sensitive data
- Regularly rotate API keys and tokens
- Enable 2FA on your Discord and Supabase accounts

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- Join our [Discord Server](https://discord.gg/your-support-server)
- Create an [Issue](https://github.com/your-username/nexusbot/issues)
- Check the [Documentation](https://your-bot-website.com/docs)

## 🙏 Acknowledgments

- [Discord.js](https://discord.js.org/) - The Discord API library
- [Supabase](https://supabase.com/) - The database platform
- [Node.js](https://nodejs.org/) - The runtime environment

---

Made with ❤️ by the NexusBot Team
