const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ModerationLog = sequelize.define('ModerationLog', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    guildId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'guilds',
        key: 'id'
      }
    },
    action: {
      type: DataTypes.ENUM('kick', 'ban', 'unban', 'timeout', 'warn', 'clear', 'mute', 'unmute'),
      allowNull: false
    },
    targetId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    targetTag: {
      type: DataTypes.STRING,
      allowNull: false
    },
    moderatorId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    moderatorTag: {
      type: DataTypes.STRING,
      allowNull: false
    },
    reason: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    duration: {
      type: DataTypes.STRING,
      allowNull: true
    },
    channelId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    messageId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    additionalData: {
      type: DataTypes.JSON,
      defaultValue: {},
      allowNull: false
    }
  }, {
    tableName: 'moderation_logs',
    timestamps: true,
    indexes: [
      {
        fields: ['guildId']
      },
      {
        fields: ['action']
      },
      {
        fields: ['targetId']
      },
      {
        fields: ['moderatorId']
      },
      {
        fields: ['createdAt']
      }
    ]
  });

  ModerationLog.associate = (models) => {
    ModerationLog.belongsTo(models.Guild, {
      foreignKey: 'guildId',
      as: 'guild'
    });
  };

  return ModerationLog;
};
