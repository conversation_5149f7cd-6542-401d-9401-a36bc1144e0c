<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NexusBot - Comprehensive Discord Bot Dashboard</title>
    <meta name="description" content="NexusBot - A powerful Discord bot with moderation, music, economy, and entertainment features">
    <meta name="keywords" content="discord bot, moderation, music bot, economy bot, entertainment">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="NexusBot - Discord Bot Dashboard">
    <meta property="og:description" content="Comprehensive Discord bot with moderation, music, economy, and entertainment features">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://wat40.github.io/">
    <meta property="og:image" content="https://wat40.github.io/assets/images/nexusbot-preview.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-robot"></i>
                <span>NexusBot</span>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="#home" class="nav-link active">Home</a>
                <a href="#features" class="nav-link">Features</a>
                <a href="#commands" class="nav-link">Commands</a>
                <a href="#setup" class="nav-link">Setup</a>
                <a href="#stats" class="nav-link">Stats</a>
            </div>
            <div class="nav-actions">
                <a href="#invite" class="btn btn-primary">
                    <i class="fab fa-discord"></i>
                    Invite Bot
                </a>
                <div class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-particles"></div>
        </div>
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        Meet <span class="gradient-text">NexusBot</span>
                        <br>Your Ultimate Discord Companion
                    </h1>
                    <p class="hero-description">
                        A comprehensive Discord bot featuring advanced moderation, music streaming, 
                        economy system, entertainment commands, and powerful server management tools.
                    </p>
                    <div class="hero-stats">
                        <div class="stat">
                            <div class="stat-number" data-count="50">0</div>
                            <div class="stat-label">Commands</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number" data-count="1000">0</div>
                            <div class="stat-label">Servers</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number" data-count="10000">0</div>
                            <div class="stat-label">Users</div>
                        </div>
                    </div>
                    <div class="hero-actions">
                        <a href="#invite" class="btn btn-primary btn-large">
                            <i class="fab fa-discord"></i>
                            Add to Discord
                        </a>
                        <a href="#features" class="btn btn-secondary btn-large">
                            <i class="fas fa-play"></i>
                            View Features
                        </a>
                    </div>
                </div>
                <div class="hero-image">
                    <div class="bot-preview">
                        <div class="bot-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="status-indicator online"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Powerful Features</h2>
                <p class="section-description">
                    Everything you need to manage and entertain your Discord community
                </p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">Advanced Moderation</h3>
                    <p class="feature-description">
                        Comprehensive moderation tools including kick, ban, timeout, warnings, 
                        and automatic moderation with customizable rules.
                    </p>
                    <ul class="feature-list">
                        <li>Kick & Ban members</li>
                        <li>Warning system</li>
                        <li>Auto-moderation</li>
                        <li>Moderation logs</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="feature-title">Music System</h3>
                    <p class="feature-description">
                        High-quality music streaming from YouTube and Spotify with 
                        queue management and audio controls.
                    </p>
                    <ul class="feature-list">
                        <li>YouTube & Spotify support</li>
                        <li>Queue management</li>
                        <li>Volume controls</li>
                        <li>Playlist support</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h3 class="feature-title">Economy & Leveling</h3>
                    <p class="feature-description">
                        Engaging economy system with virtual currency, daily rewards, 
                        XP leveling, and interactive shop features.
                    </p>
                    <ul class="feature-list">
                        <li>Virtual currency</li>
                        <li>Daily rewards</li>
                        <li>XP & leveling</li>
                        <li>Shop system</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <h3 class="feature-title">Entertainment</h3>
                    <p class="feature-description">
                        Fun commands and games including magic 8-ball, dice rolling, 
                        trivia, polls, and giveaway management.
                    </p>
                    <ul class="feature-list">
                        <li>Magic 8-ball</li>
                        <li>Dice & games</li>
                        <li>Polls & giveaways</li>
                        <li>Trivia system</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3 class="feature-title">Utility Tools</h3>
                    <p class="feature-description">
                        Helpful utilities including weather information, timezone conversion, 
                        translation services, and server management.
                    </p>
                    <ul class="feature-list">
                        <li>Weather information</li>
                        <li>Translation services</li>
                        <li>Server statistics</li>
                        <li>Custom commands</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3 class="feature-title">Configuration</h3>
                    <p class="feature-description">
                        Extensive customization options with per-server settings, 
                        custom prefixes, and role-based permissions.
                    </p>
                    <ul class="feature-list">
                        <li>Custom prefixes</li>
                        <li>Role permissions</li>
                        <li>Channel settings</li>
                        <li>Command toggles</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Commands Preview -->
    <section id="commands" class="commands-preview">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Command Categories</h2>
                <p class="section-description">
                    Explore our comprehensive command library
                </p>
            </div>
            <div class="command-categories">
                <div class="category-tab active" data-category="moderation">
                    <i class="fas fa-shield-alt"></i>
                    Moderation
                </div>
                <div class="category-tab" data-category="music">
                    <i class="fas fa-music"></i>
                    Music
                </div>
                <div class="category-tab" data-category="economy">
                    <i class="fas fa-coins"></i>
                    Economy
                </div>
                <div class="category-tab" data-category="entertainment">
                    <i class="fas fa-gamepad"></i>
                    Entertainment
                </div>
                <div class="category-tab" data-category="utility">
                    <i class="fas fa-tools"></i>
                    Utility
                </div>
            </div>
            <div class="command-content">
                <div class="command-list active" id="moderation">
                    <div class="command-item">
                        <div class="command-name">-kick</div>
                        <div class="command-description">Kick a member from the server</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">-ban</div>
                        <div class="command-description">Ban a member from the server</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">-warn</div>
                        <div class="command-description">Warn a member</div>
                    </div>
                    <div class="command-item">
                        <div class="command-name">-clear</div>
                        <div class="command-description">Clear messages from a channel</div>
                    </div>
                </div>
                <!-- More command categories will be added via JavaScript -->
            </div>
            <div class="commands-cta">
                <a href="commands.html" class="btn btn-primary">
                    <i class="fas fa-list"></i>
                    View All Commands
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-robot"></i>
                        <span>NexusBot</span>
                    </div>
                    <p class="footer-description">
                        The ultimate Discord bot for community management and entertainment.
                    </p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="#invite">Invite Bot</a></li>
                        <li><a href="commands.html">Commands</a></li>
                        <li><a href="setup.html">Setup Guide</a></li>
                        <li><a href="#support">Support</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul class="footer-links">
                        <li><a href="#discord">Discord Server</a></li>
                        <li><a href="#github">GitHub</a></li>
                        <li><a href="#docs">Documentation</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 NexusBot. Made with ❤️ for the Discord community.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
</body>
</html>
