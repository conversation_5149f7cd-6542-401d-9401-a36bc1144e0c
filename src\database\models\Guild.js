const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Guild = sequelize.define('Guild', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    prefix: {
      type: DataTypes.STRING,
      defaultValue: '!',
      allowNull: false
    },
    moderationChannel: {
      type: DataTypes.STRING,
      allowNull: true
    },
    welcomeChannel: {
      type: DataTypes.STRING,
      allowNull: true
    },
    welcomeMessage: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    autoRole: {
      type: DataTypes.STRING,
      allowNull: true
    },
    muteRole: {
      type: DataTypes.STRING,
      allowNull: true
    },
    disabledCommands: {
      type: DataTypes.JSON,
      defaultValue: [],
      allowNull: false
    },
    autoModeration: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    },
    spamThreshold: {
      type: DataTypes.INTEGER,
      defaultValue: 5,
      allowNull: false
    },
    maxWarnings: {
      type: DataTypes.INTEGER,
      defaultValue: 3,
      allowNull: false
    },
    levelingEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    },
    economyEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    },
    musicEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    },
    settings: {
      type: DataTypes.JSON,
      defaultValue: {},
      allowNull: false
    }
  }, {
    tableName: 'guilds',
    timestamps: true,
    indexes: [
      {
        fields: ['id']
      }
    ]
  });

  Guild.associate = (models) => {
    Guild.hasMany(models.Warning, {
      foreignKey: 'guildId',
      as: 'warnings'
    });
    Guild.hasMany(models.Economy, {
      foreignKey: 'guildId',
      as: 'economyData'
    });
    Guild.hasMany(models.Level, {
      foreignKey: 'guildId',
      as: 'levelData'
    });
    Guild.hasMany(models.ModerationLog, {
      foreignKey: 'guildId',
      as: 'moderationLogs'
    });
    Guild.hasMany(models.Giveaway, {
      foreignKey: 'guildId',
      as: 'giveaways'
    });
    Guild.hasMany(models.Poll, {
      foreignKey: 'guildId',
      as: 'polls'
    });
  };

  return Guild;
};
