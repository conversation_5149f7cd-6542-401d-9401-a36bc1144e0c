const fs = require('fs').promises;
const path = require('path');
const logger = require('./logger');

class EventHandler {
  constructor() {
    this.events = new Map();
  }

  // Load all events from the events directory
  async loadEvents(client) {
    try {
      const eventsPath = path.join(__dirname, '../events');
      
      // Check if events directory exists
      try {
        await fs.access(eventsPath);
      } catch {
        logger.warn('Events directory not found, creating it...');
        await fs.mkdir(eventsPath, { recursive: true });
        return;
      }

      const eventFiles = (await fs.readdir(eventsPath))
        .filter(file => file.endsWith('.js'));

      for (const file of eventFiles) {
        const filePath = path.join(eventsPath, file);
        
        try {
          // Clear require cache for hot reloading in development
          delete require.cache[require.resolve(filePath)];
          
          const event = require(filePath);
          
          // Validate event structure
          if (!this.validateEvent(event)) {
            logger.warn(`Invalid event structure in ${filePath}`);
            continue;
          }

          // Register event
          if (event.once) {
            client.once(event.name, (...args) => event.execute(...args, client));
          } else {
            client.on(event.name, (...args) => event.execute(...args, client));
          }

          this.events.set(event.name, event);
          logger.debug(`Loaded event: ${event.name}`);
          
        } catch (error) {
          logger.error(`Error loading event ${file}:`, error);
        }
      }

      logger.info(`Loaded ${this.events.size} events`);
    } catch (error) {
      logger.error('Error loading events:', error);
      throw error;
    }
  }

  // Validate event structure
  validateEvent(event) {
    return event.name && 
           typeof event.execute === 'function' &&
           typeof event.once === 'boolean';
  }

  // Reload a specific event
  async reloadEvent(eventName) {
    try {
      const eventsPath = path.join(__dirname, '../events');
      const eventFile = `${eventName}.js`;
      const filePath = path.join(eventsPath, eventFile);

      // Check if file exists
      try {
        await fs.access(filePath);
      } catch {
        throw new Error(`Event file ${eventFile} not found`);
      }

      // Clear require cache
      delete require.cache[require.resolve(filePath)];
      
      // Reload event
      const event = require(filePath);
      
      if (!this.validateEvent(event)) {
        throw new Error(`Invalid event structure in ${eventFile}`);
      }

      this.events.set(event.name, event);
      logger.info(`Reloaded event: ${event.name}`);
      
      return true;
    } catch (error) {
      logger.error(`Error reloading event ${eventName}:`, error);
      throw error;
    }
  }

  // Get all loaded events
  getEvents() {
    return Array.from(this.events.values());
  }

  // Get specific event
  getEvent(eventName) {
    return this.events.get(eventName);
  }

  // Remove event
  removeEvent(eventName) {
    if (this.events.has(eventName)) {
      this.events.delete(eventName);
      logger.info(`Removed event: ${eventName}`);
      return true;
    }
    return false;
  }
}

module.exports = new EventHandler();
