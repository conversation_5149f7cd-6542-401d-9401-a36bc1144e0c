const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    username: {
      type: DataTypes.STRING,
      allowNull: false
    },
    discriminator: {
      type: DataTypes.STRING,
      allowNull: true
    },
    avatar: {
      type: DataTypes.STRING,
      allowNull: true
    },
    isBot: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    totalMessages: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false
    },
    totalCommands: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false
    },
    lastSeen: {
      type: DataTypes.DATE,
      allowNull: true
    },
    preferences: {
      type: DataTypes.JSON,
      defaultValue: {},
      allowNull: false
    },
    blacklisted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    blacklistReason: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'users',
    timestamps: true,
    indexes: [
      {
        fields: ['id']
      },
      {
        fields: ['username']
      },
      {
        fields: ['blacklisted']
      }
    ]
  });

  User.associate = (models) => {
    User.hasMany(models.Warning, {
      foreignKey: 'userId',
      as: 'warnings'
    });
    User.hasMany(models.Economy, {
      foreignKey: 'userId',
      as: 'economyData'
    });
    User.hasMany(models.Level, {
      foreignKey: 'userId',
      as: 'levelData'
    });
    User.hasMany(models.ModerationLog, {
      foreignKey: 'targetId',
      as: 'moderationActions'
    });
    User.hasMany(models.ModerationLog, {
      foreignKey: 'moderatorId',
      as: 'moderationPerformed'
    });
  };

  return User;
};
