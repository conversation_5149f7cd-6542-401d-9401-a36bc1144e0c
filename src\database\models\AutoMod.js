/**
 * Auto Moderation Model for NexusBot
 * Handles automated moderation settings and logs
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AutoMod = sequelize.define('AutoMod', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    guildId: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      index: true
    },
    isEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    antiSpam: {
      type: DataTypes.JSON,
      defaultValue: {
        enabled: true,
        maxMessages: 5,
        timeWindow: 10000, // 10 seconds
        action: 'timeout', // timeout, kick, ban
        duration: 300000, // 5 minutes
        deleteMessages: true,
        exemptRoles: [],
        exemptChannels: []
      }
    },
    antiRaid: {
      type: DataTypes.JSON,
      defaultValue: {
        enabled: true,
        maxJoins: 10,
        timeWindow: 60000, // 1 minute
        action: 'lockdown', // lockdown, kick_new
        duration: 600000, // 10 minutes
        alertChannel: null,
        exemptRoles: []
      }
    },
    linkFilter: {
      type: DataTypes.JSON,
      defaultValue: {
        enabled: false,
        allowedDomains: [],
        blockedDomains: [],
        action: 'delete', // delete, timeout, warn
        exemptRoles: [],
        exemptChannels: []
      }
    },
    wordFilter: {
      type: DataTypes.JSON,
      defaultValue: {
        enabled: false,
        blockedWords: [],
        action: 'delete', // delete, timeout, warn
        severity: 'medium', // low, medium, high
        exemptRoles: [],
        exemptChannels: []
      }
    },
    inviteFilter: {
      type: DataTypes.JSON,
      defaultValue: {
        enabled: false,
        allowOwnServer: true,
        allowedServers: [],
        action: 'delete',
        exemptRoles: [],
        exemptChannels: []
      }
    },
    mentionSpam: {
      type: DataTypes.JSON,
      defaultValue: {
        enabled: true,
        maxMentions: 5,
        action: 'timeout',
        duration: 300000,
        exemptRoles: []
      }
    },
    capsFilter: {
      type: DataTypes.JSON,
      defaultValue: {
        enabled: false,
        maxPercentage: 70,
        minLength: 10,
        action: 'delete',
        exemptRoles: [],
        exemptChannels: []
      }
    },
    duplicateMessages: {
      type: DataTypes.JSON,
      defaultValue: {
        enabled: true,
        maxDuplicates: 3,
        timeWindow: 30000,
        action: 'timeout',
        duration: 300000,
        exemptRoles: []
      }
    },
    logChannel: {
      type: DataTypes.STRING,
      allowNull: true
    },
    alertChannel: {
      type: DataTypes.STRING,
      allowNull: true
    },
    moderatorRoles: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    trustedRoles: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    settings: {
      type: DataTypes.JSON,
      defaultValue: {
        deleteAfterAction: true,
        dmUser: false,
        logActions: true,
        escalationEnabled: true,
        maxViolationsPerHour: 5
      }
    }
  }, {
    tableName: 'automod_settings',
    hooks: {
      beforeSave: (automod) => {
        // Validate settings
        if (automod.antiSpam && automod.antiSpam.maxMessages < 1) {
          automod.antiSpam.maxMessages = 1;
        }
        if (automod.antiRaid && automod.antiRaid.maxJoins < 1) {
          automod.antiRaid.maxJoins = 1;
        }
      }
    }
  });

  // Instance methods
  AutoMod.prototype.isUserExempt = function(member, feature) {
    const featureConfig = this[feature];
    if (!featureConfig || !featureConfig.enabled) return true;

    // Check if user has trusted role
    if (this.trustedRoles.some(roleId => member.roles.cache.has(roleId))) {
      return true;
    }

    // Check if user has moderator role
    if (this.moderatorRoles.some(roleId => member.roles.cache.has(roleId))) {
      return true;
    }

    // Check feature-specific exempt roles
    if (featureConfig.exemptRoles && featureConfig.exemptRoles.some(roleId => 
      member.roles.cache.has(roleId)
    )) {
      return true;
    }

    return false;
  };

  AutoMod.prototype.isChannelExempt = function(channelId, feature) {
    const featureConfig = this[feature];
    if (!featureConfig || !featureConfig.enabled) return true;

    return featureConfig.exemptChannels && 
           featureConfig.exemptChannels.includes(channelId);
  };

  AutoMod.prototype.shouldTakeAction = function(feature, violationCount = 1) {
    const featureConfig = this[feature];
    if (!featureConfig || !featureConfig.enabled) return false;

    // Check escalation settings
    if (this.settings.escalationEnabled) {
      const maxViolations = this.settings.maxViolationsPerHour || 5;
      return violationCount >= maxViolations;
    }

    return true;
  };

  // Class methods
  AutoMod.findByGuild = async function(guildId) {
    let automod = await this.findOne({ where: { guildId } });
    
    if (!automod) {
      automod = await this.create({ guildId });
    }
    
    return automod;
  };

  AutoMod.updateSettings = async function(guildId, settings) {
    const [automod] = await this.findOrCreate({
      where: { guildId },
      defaults: { guildId }
    });

    return await automod.update(settings);
  };

  return AutoMod;
};

// Auto Moderation Log Model
module.exports.AutoModLog = (sequelize) => {
  const AutoModLog = sequelize.define('AutoModLog', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    guildId: {
      type: DataTypes.STRING,
      allowNull: false,
      index: true
    },
    userId: {
      type: DataTypes.STRING,
      allowNull: false,
      index: true
    },
    channelId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    messageId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    feature: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isIn: [['antiSpam', 'antiRaid', 'linkFilter', 'wordFilter', 'inviteFilter', 'mentionSpam', 'capsFilter', 'duplicateMessages']]
      }
    },
    action: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isIn: [['delete', 'timeout', 'kick', 'ban', 'warn', 'lockdown']]
      }
    },
    reason: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    metadata: {
      type: DataTypes.JSON,
      defaultValue: {}
    }
  }, {
    tableName: 'automod_logs',
    indexes: [
      {
        fields: ['guildId', 'userId']
      },
      {
        fields: ['guildId', 'feature']
      },
      {
        fields: ['createdAt']
      }
    ]
  });

  // Class methods
  AutoModLog.logAction = async function(data) {
    return await this.create(data);
  };

  AutoModLog.getUserViolations = async function(guildId, userId, timeframe = 3600000) {
    const since = new Date(Date.now() - timeframe);
    
    return await this.count({
      where: {
        guildId,
        userId,
        createdAt: {
          [sequelize.Sequelize.Op.gte]: since
        }
      }
    });
  };

  AutoModLog.getGuildStats = async function(guildId, timeframe = 86400000) {
    const since = new Date(Date.now() - timeframe);
    
    return await this.findAll({
      where: {
        guildId,
        createdAt: {
          [sequelize.Sequelize.Op.gte]: since
        }
      },
      attributes: [
        'feature',
        'action',
        [sequelize.fn('COUNT', '*'), 'count']
      ],
      group: ['feature', 'action']
    });
  };

  return AutoModLog;
};
