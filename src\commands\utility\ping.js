const { SlashCommandBuilder } = require('discord.js');
const embeds = require('../../utils/embeds');

module.exports = {
  name: 'ping',
  description: 'Shows bot latency and API response time',
  aliases: ['latency', 'pong'],
  usage: 'ping',
  examples: ['ping'],
  cooldown: 3,
  permissions: [],
  
  data: new SlashCommandBuilder()
    .setName('ping')
    .setDescription('Shows bot latency and API response time'),

  async execute(messageOrInteraction, args, client) {
    try {
      const isSlash = messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.();
      
      // Calculate latency
      const sent = Date.now();
      
      const initialEmbed = embeds.info('🏓 Pong!', 'Calculating latency...');
      
      let response;
      if (isSlash) {
        response = await messageOrInteraction.reply({ embeds: [initialEmbed], fetchReply: true });
      } else {
        response = await messageOrInteraction.reply({ embeds: [initialEmbed] });
      }
      
      const timeDiff = Date.now() - sent;
      const apiLatency = Math.round(client.ws.ping);
      
      // Determine latency quality
      let latencyQuality = '🟢 Excellent';
      if (timeDiff > 200 || apiLatency > 200) {
        latencyQuality = '🟡 Good';
      }
      if (timeDiff > 500 || apiLatency > 500) {
        latencyQuality = '🟠 Fair';
      }
      if (timeDiff > 1000 || apiLatency > 1000) {
        latencyQuality = '🔴 Poor';
      }

      const pingEmbed = embeds.info('🏓 Pong!', `${latencyQuality}`)
        .addFields(
          { name: 'Bot Latency', value: `${timeDiff}ms`, inline: true },
          { name: 'API Latency', value: `${apiLatency}ms`, inline: true },
          { name: 'Uptime', value: this.formatUptime(client.uptime), inline: true }
        );

      if (isSlash) {
        return messageOrInteraction.editReply({ embeds: [pingEmbed] });
      } else {
        return response.edit({ embeds: [pingEmbed] });
      }
    } catch (error) {
      client.logger.error('Error in ping command:', error);
      
      const embed = embeds.error('Error', 'An error occurred while checking latency.');
      
      if (messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.()) {
        if (messageOrInteraction.replied || messageOrInteraction.deferred) {
          return messageOrInteraction.followUp({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        }
      } else {
        return messageOrInteraction.reply({ embeds: [embed] });
      }
    }
  },

  formatUptime(uptime) {
    const seconds = Math.floor((uptime / 1000) % 60);
    const minutes = Math.floor((uptime / (1000 * 60)) % 60);
    const hours = Math.floor((uptime / (1000 * 60 * 60)) % 24);
    const days = Math.floor(uptime / (1000 * 60 * 60 * 24));

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m ${seconds}s`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }
};
