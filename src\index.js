const { Client, GatewayIntentBits, Partials, Collection } = require('discord.js');
const config = require('./config/config');
const logger = require('./utils/logger');
const database = require('./database/database');
const commandHandler = require('./utils/commandHandler');
const eventHandler = require('./utils/eventHandler');

// Create Discord client with necessary intents
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.GuildMessageReactions,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildPresences,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.DirectMessages
  ],
  partials: [
    Partials.Message,
    Partials.Channel,
    Partials.Reaction,
    Partials.User,
    Partials.GuildMember
  ]
});

// Initialize collections for commands and cooldowns
client.commands = new Collection();
client.slashCommands = new Collection();
client.cooldowns = new Collection();
client.musicQueues = new Collection();
client.config = config;
client.logger = logger;

// Initialize database
async function initializeDatabase() {
  try {
    await database.authenticate();
    await database.sync({ alter: true });
    logger.info('Database connected and synchronized successfully');
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    process.exit(1);
  }
}

// Initialize bot
async function initializeBot() {
  try {
    // Initialize database
    await initializeDatabase();

    // Load commands and events
    await commandHandler.loadCommands(client);
    await eventHandler.loadEvents(client);

    // Login to Discord
    await client.login(config.discord.token);
    
    logger.info('NexusBot initialization completed successfully');
  } catch (error) {
    logger.error('Failed to initialize bot:', error);
    process.exit(1);
  }
}

// Global error handlers
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down gracefully...');
  
  try {
    // Disconnect from voice channels
    client.voice?.adapters?.forEach(adapter => {
      adapter.destroy();
    });

    // Close database connection
    await database.close();
    
    // Destroy Discord client
    client.destroy();
    
    logger.info('Bot shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down gracefully...');
  
  try {
    await database.close();
    client.destroy();
    logger.info('Bot shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

// Start the bot
initializeBot();
