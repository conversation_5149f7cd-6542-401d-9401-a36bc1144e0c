const { Client, GatewayIntentBits, Partials, Collection, ActivityType } = require('discord.js');
const config = require('./config/config');
const logger = require('./utils/logger');
const dbManager = require('./database');
const commandHandler = require('./utils/commandHandler');
const eventHandler = require('./utils/eventHandler');
const cacheManager = require('./utils/cacheManager');
const rateLimiter = require('./utils/rateLimiter');
const healthMonitor = require('./utils/healthMonitor');
const backupManager = require('./utils/backupManager');
const chalk = require('chalk');
const figlet = require('figlet');

/**
 * Enhanced Discord client with comprehensive intents and advanced features
 * @type {Client}
 */
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.GuildMessageReactions,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildPresences,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.DirectMessages,
    GatewayIntentBits.GuildModeration,
    GatewayIntentBits.GuildEmojisAndStickers,
    GatewayIntentBits.GuildWebhooks,
    GatewayIntentBits.GuildInvites,
    GatewayIntentBits.GuildScheduledEvents
  ],
  partials: [
    Partials.Message,
    Partials.Channel,
    Partials.Reaction,
    Partials.User,
    Partials.GuildMember,
    Partials.ThreadMember,
    Partials.GuildScheduledEvent
  ],
  allowedMentions: {
    parse: ['users', 'roles'],
    repliedUser: false
  },
  presence: {
    activities: [{
      name: 'Starting up...',
      type: ActivityType.Playing
    }],
    status: 'dnd'
  }
});

// Initialize enhanced collections and managers
client.commands = new Collection();
client.slashCommands = new Collection();
client.cooldowns = new Collection();
client.musicQueues = new Collection();
client.guildSettings = new Collection();
client.userProfiles = new Collection();
client.activeGames = new Collection();
client.reminders = new Collection();
client.autoMod = new Collection();
client.raidProtection = new Collection();
client.config = config;
client.cache = cacheManager;
client.rateLimiter = rateLimiter;
client.health = healthMonitor;
client.backup = backupManager;

/**
 * Display startup banner
 */
function displayStartupBanner() {
  console.log(chalk.cyan(figlet.textSync('NexusBot', {
    font: 'ANSI Shadow',
    horizontalLayout: 'default',
    verticalLayout: 'default'
  })));
  console.log(chalk.yellow('━'.repeat(80)));
  console.log(chalk.green('🤖 NexusBot v2.0.0 - Advanced Discord Bot'));
  console.log(chalk.blue('🚀 Starting initialization process...'));
  console.log(chalk.yellow('━'.repeat(80)));
}

/**
 * Initialize database with enhanced error handling and migration support
 * @returns {Promise<void>}
 */
async function initializeDatabase() {
  try {
    logger.info('🔄 Initializing database connection...');

    // Initialize the database manager (will choose Supabase or Sequelize)
    await dbManager.initialize();

    // Test connection and initialize tables
    const healthCheck = await dbManager.healthCheck();
    if (healthCheck.status === 'healthy') {
      logger.info(`✅ Database connected successfully (${dbManager.getDatabaseType()})`);

      // Run any pending migrations
      await dbManager.runMigrations();
      logger.info('✅ Database migrations completed');

      // Initialize cache with database data
      await cacheManager.initialize();
      logger.info('✅ Cache manager initialized');

    } else {
      throw new Error(`Database health check failed: ${healthCheck.error}`);
    }
  } catch (error) {
    logger.error('❌ Failed to connect to database:', error);
    process.exit(1);
  }
}

/**
 * Initialize all bot systems with comprehensive error handling
 * @returns {Promise<void>}
 */
async function initializeBot() {
  try {
    // Display startup banner
    displayStartupBanner();

    // Initialize core systems
    logger.info('🔄 Initializing core systems...');

    // Initialize database
    await initializeDatabase();

    // Initialize rate limiter
    await rateLimiter.initialize();
    logger.info('✅ Rate limiter initialized');

    // Initialize health monitor
    await healthMonitor.initialize(client);
    logger.info('✅ Health monitor initialized');

    // Initialize backup manager
    await backupManager.initialize();
    logger.info('✅ Backup manager initialized');

    // Load commands and events
    logger.info('🔄 Loading commands and events...');
    await commandHandler.loadCommands(client);
    await eventHandler.loadEvents(client);
    logger.info('✅ Commands and events loaded');

    // Login to Discord
    logger.info('🔄 Connecting to Discord...');
    await client.login(config.discord.token);

    logger.info('🎉 NexusBot initialization completed successfully');
  } catch (error) {
    logger.error('❌ Failed to initialize bot:', error);
    process.exit(1);
  }
}

// Global error handlers
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down gracefully...');
  
  try {
    // Disconnect from voice channels
    client.voice?.adapters?.forEach(adapter => {
      adapter.destroy();
    });

    // Close database connection
    await dbManager.close();
    
    // Destroy Discord client
    client.destroy();
    
    logger.info('Bot shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down gracefully...');
  
  try {
    await dbManager.close();
    client.destroy();
    logger.info('Bot shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

// Start the bot
initializeBot();
