const { sequelize } = require('../database');

// Import all models
const Guild = require('./Guild');
const User = require('./User');
const Warning = require('./Warning');
const Economy = require('./Economy');
const Level = require('./Level');
const ModerationLog = require('./ModerationLog');
const Giveaway = require('./Giveaway');
const Poll = require('./Poll');
const Reminder = require('./Reminder');
const CustomCommand = require('./CustomCommand');
const AutoMod = require('./AutoMod');

// Initialize models
const models = {
  Guild: Guild(sequelize),
  User: User(sequelize),
  Warning: Warning(sequelize),
  Economy: Economy(sequelize),
  Level: Level(sequelize),
  ModerationLog: ModerationLog(sequelize),
  Giveaway: Giveaway(sequelize),
  Poll: Poll(sequelize),
  Reminder: Reminder(sequelize),
  CustomCommand: CustomCommand(sequelize),
  AutoMod: AutoMod(sequelize),
  AutoModLog: AutoMod.AutoModLog(sequelize)
};

// Define associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

// Export models
module.exports = models;
