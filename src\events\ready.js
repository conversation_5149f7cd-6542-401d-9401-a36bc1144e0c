const { ActivityType } = require('discord.js');
const logger = require('../utils/logger');

module.exports = {
  name: 'ready',
  once: true,
  async execute(client) {
    try {
      logger.info(`<PERSON><PERSON> is ready! Logged in as ${client.user.tag}`);
      logger.info(`Serving ${client.guilds.cache.size} guilds with ${client.users.cache.size} users`);

      // Set bot activity
      const activities = [
        { name: 'your commands', type: ActivityType.Listening },
        { name: 'the server', type: ActivityType.Watching },
        { name: `${client.guilds.cache.size} servers`, type: ActivityType.Watching },
        { name: 'music and having fun!', type: ActivityType.Playing },
        { name: 'with Discord.js', type: ActivityType.Playing }
      ];

      let activityIndex = 0;
      
      // Set initial activity
      client.user.setActivity(activities[activityIndex]);

      // Rotate activities every 30 seconds
      setInterval(() => {
        activityIndex = (activityIndex + 1) % activities.length;
        client.user.setActivity(activities[activityIndex]);
      }, 30000);

      // Log guild information
      client.guilds.cache.forEach(guild => {
        logger.debug(`Connected to guild: ${guild.name} (${guild.id}) with ${guild.memberCount} members`);
      });

      // Set bot status
      client.user.setStatus('online');

      logger.info('NexusBot is fully operational!');
    } catch (error) {
      logger.error('Error in ready event:', error);
    }
  }
};
