const models = require('./models');
const logger = require('../utils/logger');
const config = require('../config/config');
const supabaseUtils = require('./supabaseUtils');

class DatabaseUtils {
  // Guild utilities
  async getOrCreateGuild(guildId, guildData = {}) {
    try {
      const [guild, created] = await models.Guild.findOrCreate({
        where: { id: guildId },
        defaults: {
          id: guildId,
          name: guildData.name || 'Unknown Guild',
          ...guildData
        }
      });

      if (created) {
        logger.info(`Created new guild record: ${guild.name} (${guildId})`);
      }

      return guild;
    } catch (error) {
      logger.error('Error getting or creating guild:', error);
      throw error;
    }
  }

  async updateGuildSettings(guildId, settings) {
    try {
      const guild = await this.getOrCreateGuild(guildId);
      await guild.update(settings);
      return guild;
    } catch (error) {
      logger.error('Error updating guild settings:', error);
      throw error;
    }
  }

  // User utilities
  async getOrCreateUser(userId, userData = {}) {
    try {
      const [user, created] = await models.User.findOrCreate({
        where: { id: userId },
        defaults: {
          id: userId,
          username: userData.username || 'Unknown User',
          discriminator: userData.discriminator || '0000',
          avatar: userData.avatar || null,
          ...userData
        }
      });

      if (created) {
        logger.debug(`Created new user record: ${user.username} (${userId})`);
      }

      return user;
    } catch (error) {
      logger.error('Error getting or creating user:', error);
      throw error;
    }
  }

  // Economy utilities
  async getOrCreateEconomy(userId, guildId) {
    try {
      const [economy, created] = await models.Economy.findOrCreate({
        where: { userId, guildId },
        defaults: {
          userId,
          guildId,
          balance: config.economy.startingBalance
        }
      });

      if (created) {
        logger.debug(`Created new economy record for user ${userId} in guild ${guildId}`);
      }

      return economy;
    } catch (error) {
      logger.error('Error getting or creating economy:', error);
      throw error;
    }
  }

  async updateBalance(userId, guildId, amount, operation = 'add') {
    try {
      const economy = await this.getOrCreateEconomy(userId, guildId);
      
      let newBalance;
      if (operation === 'add') {
        newBalance = parseInt(economy.balance) + amount;
        economy.totalEarned = parseInt(economy.totalEarned) + Math.max(0, amount);
      } else if (operation === 'subtract') {
        newBalance = parseInt(economy.balance) - amount;
        economy.totalSpent = parseInt(economy.totalSpent) + Math.max(0, amount);
      } else if (operation === 'set') {
        newBalance = amount;
      }

      economy.balance = Math.max(0, newBalance);
      await economy.save();

      return economy;
    } catch (error) {
      logger.error('Error updating balance:', error);
      throw error;
    }
  }

  // Level utilities
  async getOrCreateLevel(userId, guildId) {
    try {
      const [level, created] = await models.Level.findOrCreate({
        where: { userId, guildId },
        defaults: {
          userId,
          guildId,
          xp: 0,
          level: 1,
          totalXp: 0
        }
      });

      if (created) {
        logger.debug(`Created new level record for user ${userId} in guild ${guildId}`);
      }

      return level;
    } catch (error) {
      logger.error('Error getting or creating level:', error);
      throw error;
    }
  }

  async addXp(userId, guildId, xpAmount) {
    try {
      const levelData = await this.getOrCreateLevel(userId, guildId);
      
      const oldLevel = levelData.level;
      levelData.xp = parseInt(levelData.xp) + xpAmount;
      levelData.totalXp = parseInt(levelData.totalXp) + xpAmount;
      levelData.messageCount = parseInt(levelData.messageCount) + 1;
      levelData.lastXpGain = new Date();

      // Calculate new level
      const newLevel = this.calculateLevel(levelData.totalXp);
      levelData.level = newLevel;

      await levelData.save();

      // Return level up information
      return {
        levelData,
        leveledUp: newLevel > oldLevel,
        oldLevel,
        newLevel
      };
    } catch (error) {
      logger.error('Error adding XP:', error);
      throw error;
    }
  }

  calculateLevel(totalXp) {
    // Level formula: level = floor(sqrt(totalXp / 100))
    return Math.floor(Math.sqrt(totalXp / 100)) + 1;
  }

  calculateXpForLevel(level) {
    // XP needed for a specific level
    return Math.pow(level - 1, 2) * 100;
  }

  // Warning utilities
  async addWarning(userId, guildId, moderatorId, reason, expiresAt = null) {
    try {
      const warning = await models.Warning.create({
        userId,
        guildId,
        moderatorId,
        reason,
        expiresAt
      });

      logger.moderation('warning_added', moderatorId, userId, reason, guildId);
      return warning;
    } catch (error) {
      logger.error('Error adding warning:', error);
      throw error;
    }
  }

  async getActiveWarnings(userId, guildId) {
    try {
      return await models.Warning.findAll({
        where: {
          userId,
          guildId,
          active: true
        },
        order: [['createdAt', 'DESC']]
      });
    } catch (error) {
      logger.error('Error getting active warnings:', error);
      throw error;
    }
  }

  // Moderation log utilities
  async logModerationAction(data) {
    try {
      const log = await models.ModerationLog.create(data);
      logger.moderation(data.action, data.moderatorId, data.targetId, data.reason, data.guildId);
      return log;
    } catch (error) {
      logger.error('Error logging moderation action:', error);
      throw error;
    }
  }

  // Leaderboard utilities
  async getEconomyLeaderboard(guildId, limit = 10) {
    try {
      return await models.Economy.findAll({
        where: { guildId },
        order: [['balance', 'DESC']],
        limit,
        include: [{
          model: models.User,
          as: 'user',
          attributes: ['username', 'discriminator', 'avatar']
        }]
      });
    } catch (error) {
      logger.error('Error getting economy leaderboard:', error);
      throw error;
    }
  }

  async getLevelLeaderboard(guildId, limit = 10) {
    try {
      return await models.Level.findAll({
        where: { guildId },
        order: [['totalXp', 'DESC']],
        limit,
        include: [{
          model: models.User,
          as: 'user',
          attributes: ['username', 'discriminator', 'avatar']
        }]
      });
    } catch (error) {
      logger.error('Error getting level leaderboard:', error);
      throw error;
    }
  }

  // Cleanup utilities
  async cleanupExpiredWarnings() {
    try {
      const expiredCount = await models.Warning.update(
        { active: false },
        {
          where: {
            active: true,
            expiresAt: {
              [models.sequelize.Op.lt]: new Date()
            }
          }
        }
      );

      if (expiredCount[0] > 0) {
        logger.info(`Cleaned up ${expiredCount[0]} expired warnings`);
      }

      return expiredCount[0];
    } catch (error) {
      logger.error('Error cleaning up expired warnings:', error);
      throw error;
    }
  }

  async cleanupOldLogs(daysOld = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const deletedCount = await models.ModerationLog.destroy({
        where: {
          createdAt: {
            [models.sequelize.Op.lt]: cutoffDate
          }
        }
      });

      if (deletedCount > 0) {
        logger.info(`Cleaned up ${deletedCount} old moderation logs`);
      }

      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up old logs:', error);
      throw error;
    }
  }
}

module.exports = new DatabaseUtils();
