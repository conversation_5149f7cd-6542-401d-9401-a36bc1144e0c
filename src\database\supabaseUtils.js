const { SupabaseUtils } = require('./supabase');
const logger = require('../utils/logger');
const config = require('../config/config');

class SupabaseDatabaseUtils {
  constructor() {
    this.supabase = SupabaseUtils.client;
  }

  // Guild utilities
  async getOrCreateGuild(guildId, guildData = {}) {
    try {
      // First try to get existing guild
      const { data: existingGuild, error: fetchError } = await this.supabase
        .from('guilds')
        .select('*')
        .eq('id', guildId)
        .single();

      if (existingGuild && !fetchError) {
        return existingGuild;
      }

      // Create new guild if it doesn't exist
      const { data: newGuild, error: createError } = await this.supabase
        .from('guilds')
        .insert({
          id: guildId,
          name: guildData.name || 'Unknown Guild',
          ...guildData
        })
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      logger.info(`Created new guild record: ${newGuild.name} (${guildId})`);
      return newGuild;
    } catch (error) {
      logger.error('Error getting or creating guild:', error);
      throw error;
    }
  }

  async updateGuildSettings(guildId, settings) {
    try {
      const { data, error } = await this.supabase
        .from('guilds')
        .update({ ...settings, updated_at: new Date().toISOString() })
        .eq('id', guildId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Error updating guild settings:', error);
      throw error;
    }
  }

  // User utilities
  async getOrCreateUser(userId, userData = {}) {
    try {
      // First try to get existing user
      const { data: existingUser, error: fetchError } = await this.supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (existingUser && !fetchError) {
        return existingUser;
      }

      // Create new user if it doesn't exist
      const { data: newUser, error: createError } = await this.supabase
        .from('users')
        .insert({
          id: userId,
          username: userData.username || 'Unknown User',
          discriminator: userData.discriminator || '0000',
          avatar: userData.avatar || null,
          ...userData
        })
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      logger.debug(`Created new user record: ${newUser.username} (${userId})`);
      return newUser;
    } catch (error) {
      logger.error('Error getting or creating user:', error);
      throw error;
    }
  }

  // Economy utilities
  async getOrCreateEconomy(userId, guildId) {
    try {
      // Ensure user and guild exist
      await this.getOrCreateUser(userId);
      await this.getOrCreateGuild(guildId);

      // First try to get existing economy record
      const { data: existingEconomy, error: fetchError } = await this.supabase
        .from('economy')
        .select('*')
        .eq('user_id', userId)
        .eq('guild_id', guildId)
        .single();

      if (existingEconomy && !fetchError) {
        return existingEconomy;
      }

      // Create new economy record if it doesn't exist
      const { data: newEconomy, error: createError } = await this.supabase
        .from('economy')
        .insert({
          user_id: userId,
          guild_id: guildId,
          balance: config.economy.startingBalance
        })
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      logger.debug(`Created new economy record for user ${userId} in guild ${guildId}`);
      return newEconomy;
    } catch (error) {
      logger.error('Error getting or creating economy:', error);
      throw error;
    }
  }

  async updateBalance(userId, guildId, amount, operation = 'add') {
    try {
      const economy = await this.getOrCreateEconomy(userId, guildId);
      
      let newBalance;
      let updateData = { updated_at: new Date().toISOString() };

      if (operation === 'add') {
        newBalance = parseInt(economy.balance) + amount;
        updateData.total_earned = parseInt(economy.total_earned) + Math.max(0, amount);
      } else if (operation === 'subtract') {
        newBalance = parseInt(economy.balance) - amount;
        updateData.total_spent = parseInt(economy.total_spent) + Math.max(0, amount);
      } else if (operation === 'set') {
        newBalance = amount;
      }

      updateData.balance = Math.max(0, newBalance);

      const { data, error } = await this.supabase
        .from('economy')
        .update(updateData)
        .eq('user_id', userId)
        .eq('guild_id', guildId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Error updating balance:', error);
      throw error;
    }
  }

  // Level utilities
  async getOrCreateLevel(userId, guildId) {
    try {
      // Ensure user and guild exist
      await this.getOrCreateUser(userId);
      await this.getOrCreateGuild(guildId);

      // First try to get existing level record
      const { data: existingLevel, error: fetchError } = await this.supabase
        .from('levels')
        .select('*')
        .eq('user_id', userId)
        .eq('guild_id', guildId)
        .single();

      if (existingLevel && !fetchError) {
        return existingLevel;
      }

      // Create new level record if it doesn't exist
      const { data: newLevel, error: createError } = await this.supabase
        .from('levels')
        .insert({
          user_id: userId,
          guild_id: guildId,
          xp: 0,
          level: 1,
          total_xp: 0
        })
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      logger.debug(`Created new level record for user ${userId} in guild ${guildId}`);
      return newLevel;
    } catch (error) {
      logger.error('Error getting or creating level:', error);
      throw error;
    }
  }

  async addXp(userId, guildId, xpAmount) {
    try {
      const levelData = await this.getOrCreateLevel(userId, guildId);
      
      const oldLevel = levelData.level;
      const newXp = parseInt(levelData.xp) + xpAmount;
      const newTotalXp = parseInt(levelData.total_xp) + xpAmount;
      const newMessageCount = parseInt(levelData.message_count) + 1;

      // Calculate new level
      const newLevel = this.calculateLevel(newTotalXp);

      const { data, error } = await this.supabase
        .from('levels')
        .update({
          xp: newXp,
          total_xp: newTotalXp,
          level: newLevel,
          message_count: newMessageCount,
          last_xp_gain: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('guild_id', guildId)
        .select()
        .single();

      if (error) throw error;

      // Return level up information
      return {
        levelData: data,
        leveledUp: newLevel > oldLevel,
        oldLevel,
        newLevel
      };
    } catch (error) {
      logger.error('Error adding XP:', error);
      throw error;
    }
  }

  calculateLevel(totalXp) {
    // Level formula: level = floor(sqrt(totalXp / 100))
    return Math.floor(Math.sqrt(totalXp / 100)) + 1;
  }

  calculateXpForLevel(level) {
    // XP needed for a specific level
    return Math.pow(level - 1, 2) * 100;
  }

  // Warning utilities
  async addWarning(userId, guildId, moderatorId, reason, expiresAt = null) {
    try {
      // Ensure user and guild exist
      await this.getOrCreateUser(userId);
      await this.getOrCreateGuild(guildId);

      const { data, error } = await this.supabase
        .from('warnings')
        .insert({
          user_id: userId,
          guild_id: guildId,
          moderator_id: moderatorId,
          reason,
          expires_at: expiresAt
        })
        .select()
        .single();

      if (error) throw error;

      logger.moderation('warning_added', moderatorId, userId, reason, guildId);
      return data;
    } catch (error) {
      logger.error('Error adding warning:', error);
      throw error;
    }
  }

  async getActiveWarnings(userId, guildId) {
    try {
      const { data, error } = await this.supabase
        .from('warnings')
        .select('*')
        .eq('user_id', userId)
        .eq('guild_id', guildId)
        .eq('active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      logger.error('Error getting active warnings:', error);
      throw error;
    }
  }

  // Moderation log utilities
  async logModerationAction(data) {
    try {
      const { data: log, error } = await this.supabase
        .from('moderation_logs')
        .insert(data)
        .select()
        .single();

      if (error) throw error;

      logger.moderation(data.action, data.moderator_id, data.target_id, data.reason, data.guild_id);
      return log;
    } catch (error) {
      logger.error('Error logging moderation action:', error);
      throw error;
    }
  }

  // Leaderboard utilities
  async getEconomyLeaderboard(guildId, limit = 10) {
    try {
      const { data, error } = await this.supabase
        .from('economy')
        .select(`
          *,
          users (
            username,
            discriminator,
            avatar
          )
        `)
        .eq('guild_id', guildId)
        .order('balance', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      logger.error('Error getting economy leaderboard:', error);
      throw error;
    }
  }

  async getLevelLeaderboard(guildId, limit = 10) {
    try {
      const { data, error } = await this.supabase
        .from('levels')
        .select(`
          *,
          users (
            username,
            discriminator,
            avatar
          )
        `)
        .eq('guild_id', guildId)
        .order('total_xp', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      logger.error('Error getting level leaderboard:', error);
      throw error;
    }
  }

  // Cleanup utilities
  async cleanupExpiredWarnings() {
    try {
      const { data, error } = await this.supabase
        .from('warnings')
        .update({ active: false, updated_at: new Date().toISOString() })
        .eq('active', true)
        .lt('expires_at', new Date().toISOString())
        .select();

      if (error) throw error;

      const expiredCount = data?.length || 0;
      if (expiredCount > 0) {
        logger.info(`Cleaned up ${expiredCount} expired warnings`);
      }

      return expiredCount;
    } catch (error) {
      logger.error('Error cleaning up expired warnings:', error);
      throw error;
    }
  }

  async cleanupOldLogs(daysOld = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const { data, error } = await this.supabase
        .from('moderation_logs')
        .delete()
        .lt('created_at', cutoffDate.toISOString())
        .select();

      if (error) throw error;

      const deletedCount = data?.length || 0;
      if (deletedCount > 0) {
        logger.info(`Cleaned up ${deletedCount} old moderation logs`);
      }

      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up old logs:', error);
      throw error;
    }
  }
}

module.exports = new SupabaseDatabaseUtils();
