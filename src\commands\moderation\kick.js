const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const embeds = require('../../utils/embeds');
const dbUtils = require('../../database/utils');

module.exports = {
  name: 'kick',
  description: 'Kick a member from the server',
  aliases: [],
  usage: 'kick <user> [reason]',
  examples: ['kick @user Spamming', 'kick 123456789012345678 Breaking rules'],
  cooldown: 5,
  permissions: [PermissionFlagsBits.KickMembers],
  
  data: new SlashCommandBuilder()
    .setName('kick')
    .setDescription('Kick a member from the server')
    .addUserOption(option =>
      option.setName('user')
        .setDescription('The user to kick')
        .setRequired(true)
    )
    .addStringOption(option =>
      option.setName('reason')
        .setDescription('Reason for the kick')
        .setRequired(false)
    )
    .setDefaultMemberPermissions(PermissionFlagsBits.KickMembers),

  async execute(messageOrInteraction, args, client) {
    try {
      const isSlash = messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.();
      
      // Get target user and reason
      let targetUser, reason;
      
      if (isSlash) {
        targetUser = messageOrInteraction.options.getUser('user');
        reason = messageOrInteraction.options.getString('reason') || 'No reason provided';
      } else {
        // Parse mentions or user ID from message
        const userMention = messageOrInteraction.mentions.users.first();
        const userId = args[0]?.replace(/[<@!>]/g, '');
        
        if (userMention) {
          targetUser = userMention;
        } else if (userId && /^\d+$/.test(userId)) {
          try {
            targetUser = await client.users.fetch(userId);
          } catch {
            const embed = embeds.error('User Not Found', 'Could not find the specified user.');
            return messageOrInteraction.reply({ embeds: [embed] });
          }
        } else {
          const embed = embeds.error('Invalid User', 'Please mention a user or provide a valid user ID.');
          return messageOrInteraction.reply({ embeds: [embed] });
        }
        
        reason = args.slice(1).join(' ') || 'No reason provided';
      }

      // Get guild member
      const guild = messageOrInteraction.guild;
      const member = await guild.members.fetch(targetUser.id).catch(() => null);
      
      if (!member) {
        const embed = embeds.error('Member Not Found', 'This user is not a member of this server.');
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed] });
        }
      }

      // Permission checks
      const moderator = messageOrInteraction.member;
      const botMember = guild.members.me;

      // Check if target is the bot
      if (targetUser.id === client.user.id) {
        const embed = embeds.error('Cannot Kick Bot', 'I cannot kick myself!');
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed] });
        }
      }

      // Check if target is the moderator
      if (targetUser.id === moderator.id) {
        const embed = embeds.error('Cannot Kick Self', 'You cannot kick yourself!');
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed] });
        }
      }

      // Check role hierarchy
      if (member.roles.highest.position >= moderator.roles.highest.position && moderator.id !== guild.ownerId) {
        const embed = embeds.error('Insufficient Permissions', 'You cannot kick this user due to role hierarchy.');
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed] });
        }
      }

      // Check if bot can kick the member
      if (member.roles.highest.position >= botMember.roles.highest.position) {
        const embed = embeds.error('Cannot Kick User', 'I cannot kick this user due to role hierarchy.');
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed] });
        }
      }

      // Check if member is kickable
      if (!member.kickable) {
        const embed = embeds.error('Cannot Kick User', 'I do not have permission to kick this user.');
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed] });
        }
      }

      try {
        // Send DM to user before kicking
        try {
          const dmEmbed = embeds.warning(
            'You have been kicked',
            `You have been kicked from **${guild.name}**`,
            [
              { name: 'Reason', value: reason, inline: false },
              { name: 'Moderator', value: `${moderator.user.tag}`, inline: true }
            ]
          );
          
          await targetUser.send({ embeds: [dmEmbed] });
        } catch {
          // User has DMs disabled or blocked the bot
        }

        // Kick the member
        await member.kick(reason);

        // Log the action
        await dbUtils.logModerationAction({
          guildId: guild.id,
          action: 'kick',
          targetId: targetUser.id,
          targetTag: targetUser.tag,
          moderatorId: moderator.id,
          moderatorTag: moderator.user.tag,
          reason: reason,
          channelId: messageOrInteraction.channel.id
        });

        // Send confirmation
        const successEmbed = embeds.moderation('Kick', targetUser, moderator.user, reason);
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [successEmbed] });
        } else {
          return messageOrInteraction.reply({ embeds: [successEmbed] });
        }

      } catch (error) {
        client.logger.error('Error kicking member:', error);
        
        const embed = embeds.error('Kick Failed', 'An error occurred while trying to kick the user.');
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed] });
        }
      }

    } catch (error) {
      client.logger.error('Error in kick command:', error);
      
      const embed = embeds.error('Error', 'An error occurred while processing the kick command.');
      
      if (messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.()) {
        if (messageOrInteraction.replied || messageOrInteraction.deferred) {
          return messageOrInteraction.followUp({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        }
      } else {
        return messageOrInteraction.reply({ embeds: [embed] });
      }
    }
  }
};
