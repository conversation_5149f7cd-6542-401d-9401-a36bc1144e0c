const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Level = sequelize.define('Level', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    guildId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'guilds',
        key: 'id'
      }
    },
    xp: {
      type: DataTypes.BIGINT,
      defaultValue: 0,
      allowNull: false
    },
    level: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      allowNull: false
    },
    totalXp: {
      type: DataTypes.BIGINT,
      defaultValue: 0,
      allowNull: false
    },
    messageCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false
    },
    voiceTime: {
      type: DataTypes.BIGINT,
      defaultValue: 0,
      allowNull: false
    },
    lastXpGain: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    tableName: 'levels',
    timestamps: true,
    indexes: [
      {
        fields: ['userId', 'guildId'],
        unique: true
      },
      {
        fields: ['level']
      },
      {
        fields: ['totalXp']
      },
      {
        fields: ['messageCount']
      }
    ]
  });

  Level.associate = (models) => {
    Level.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    Level.belongsTo(models.Guild, {
      foreignKey: 'guildId',
      as: 'guild'
    });
  };

  return Level;
};
