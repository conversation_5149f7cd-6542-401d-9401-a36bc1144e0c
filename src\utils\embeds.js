const { EmbedBuilder } = require('discord.js');
const config = require('../config/config');

class EmbedTemplates {
  constructor() {
    this.colors = config.bot.embedColors;
    this.emojis = config.bot.emojis;
  }

  // Base embed with common properties
  createBaseEmbed(color = this.colors.primary) {
    return new EmbedBuilder()
      .setColor(color)
      .setTimestamp()
      .setFooter({
        text: 'NexusBot • Powered by Discord.js',
        iconURL: 'https://cdn.discordapp.com/app-icons/your-bot-id/icon.png'
      });
  }

  // Success embed
  success(title, description, fields = []) {
    const embed = this.createBaseEmbed(this.colors.success)
      .setTitle(`${this.emojis.success} ${title}`)
      .setDescription(description);

    if (fields.length > 0) {
      embed.addFields(fields);
    }

    return embed;
  }

  // Error embed
  error(title, description, fields = []) {
    const embed = this.createBaseEmbed(this.colors.error)
      .setTitle(`${this.emojis.error} ${title}`)
      .setDescription(description);

    if (fields.length > 0) {
      embed.addFields(fields);
    }

    return embed;
  }

  // Warning embed
  warning(title, description, fields = []) {
    const embed = this.createBaseEmbed(this.colors.warning)
      .setTitle(`${this.emojis.warning} ${title}`)
      .setDescription(description);

    if (fields.length > 0) {
      embed.addFields(fields);
    }

    return embed;
  }

  // Info embed
  info(title, description, fields = []) {
    const embed = this.createBaseEmbed(this.colors.info)
      .setTitle(`${this.emojis.info} ${title}`)
      .setDescription(description);

    if (fields.length > 0) {
      embed.addFields(fields);
    }

    return embed;
  }

  // Moderation embed
  moderation(action, target, moderator, reason, duration = null) {
    const embed = this.createBaseEmbed(this.colors.warning)
      .setTitle(`🔨 Moderation Action: ${action}`)
      .addFields(
        { name: 'Target', value: `${target}`, inline: true },
        { name: 'Moderator', value: `${moderator}`, inline: true },
        { name: 'Reason', value: reason || 'No reason provided', inline: false }
      );

    if (duration) {
      embed.addFields({ name: 'Duration', value: duration, inline: true });
    }

    return embed;
  }

  // Economy embed
  economy(title, user, amount, balance, action = null) {
    const embed = this.createBaseEmbed(this.colors.accent)
      .setTitle(`${this.emojis.money} ${title}`)
      .addFields(
        { name: 'User', value: `${user}`, inline: true },
        { name: 'Amount', value: `${config.economy.currency.symbol} ${amount.toLocaleString()}`, inline: true },
        { name: 'New Balance', value: `${config.economy.currency.symbol} ${balance.toLocaleString()}`, inline: true }
      );

    if (action) {
      embed.setDescription(`Action: ${action}`);
    }

    return embed;
  }

  // Music embed
  music(title, description, thumbnail = null, fields = []) {
    const embed = this.createBaseEmbed(this.colors.secondary)
      .setTitle(`${this.emojis.music} ${title}`)
      .setDescription(description);

    if (thumbnail) {
      embed.setThumbnail(thumbnail);
    }

    if (fields.length > 0) {
      embed.addFields(fields);
    }

    return embed;
  }

  // Level up embed
  levelUp(user, newLevel, xpNeeded) {
    return this.createBaseEmbed(this.colors.accent)
      .setTitle(`${this.emojis.level} Level Up!`)
      .setDescription(`Congratulations ${user}! You've reached level **${newLevel}**!`)
      .addFields(
        { name: 'New Level', value: `${newLevel}`, inline: true },
        { name: 'XP to Next Level', value: `${xpNeeded.toLocaleString()}`, inline: true }
      )
      .setThumbnail(user.displayAvatarURL({ dynamic: true }));
  }

  // Poll embed
  poll(question, options, duration, creator) {
    const embed = this.createBaseEmbed(this.colors.primary)
      .setTitle('📊 Poll')
      .setDescription(`**${question}**`)
      .addFields(
        { name: 'Duration', value: duration, inline: true },
        { name: 'Created by', value: `${creator}`, inline: true }
      );

    // Add options with emoji reactions
    const optionText = options.map((option, index) => {
      const emoji = ['1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣', '6️⃣', '7️⃣', '8️⃣', '9️⃣', '🔟'][index];
      return `${emoji} ${option}`;
    }).join('\n');

    embed.addFields({ name: 'Options', value: optionText, inline: false });

    return embed;
  }

  // Giveaway embed
  giveaway(prize, duration, requirements, host) {
    return this.createBaseEmbed(this.colors.accent)
      .setTitle('🎉 GIVEAWAY 🎉')
      .setDescription(`**Prize:** ${prize}`)
      .addFields(
        { name: 'Duration', value: duration, inline: true },
        { name: 'Host', value: `${host}`, inline: true },
        { name: 'Requirements', value: requirements || 'None', inline: false },
        { name: 'How to Enter', value: 'React with 🎉 to enter!', inline: false }
      );
  }

  // Server stats embed
  serverStats(guild, stats) {
    return this.createBaseEmbed(this.colors.primary)
      .setTitle(`📊 ${guild.name} Statistics`)
      .setThumbnail(guild.iconURL({ dynamic: true }))
      .addFields(
        { name: 'Total Members', value: `${stats.totalMembers}`, inline: true },
        { name: 'Online Members', value: `${stats.onlineMembers}`, inline: true },
        { name: 'Bots', value: `${stats.bots}`, inline: true },
        { name: 'Text Channels', value: `${stats.textChannels}`, inline: true },
        { name: 'Voice Channels', value: `${stats.voiceChannels}`, inline: true },
        { name: 'Roles', value: `${stats.roles}`, inline: true },
        { name: 'Server Created', value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:F>`, inline: false }
      );
  }

  // Help embed
  help(commandName, command) {
    const embed = this.createBaseEmbed(this.colors.info)
      .setTitle(`📖 Help: ${commandName}`)
      .setDescription(command.description);

    if (command.usage) {
      embed.addFields({ name: 'Usage', value: `\`${command.usage}\``, inline: false });
    }

    if (command.examples && command.examples.length > 0) {
      embed.addFields({ 
        name: 'Examples', 
        value: command.examples.map(ex => `\`${ex}\``).join('\n'), 
        inline: false 
      });
    }

    if (command.aliases && command.aliases.length > 0) {
      embed.addFields({ 
        name: 'Aliases', 
        value: command.aliases.map(alias => `\`${alias}\``).join(', '), 
        inline: true 
      });
    }

    if (command.permissions && command.permissions.length > 0) {
      embed.addFields({ 
        name: 'Required Permissions', 
        value: command.permissions.join(', '), 
        inline: true 
      });
    }

    if (command.cooldown) {
      embed.addFields({ 
        name: 'Cooldown', 
        value: `${command.cooldown} seconds`, 
        inline: true 
      });
    }

    return embed;
  }

  // Command list embed
  commandList(categories, prefix) {
    const embed = this.createBaseEmbed(this.colors.primary)
      .setTitle('📚 Command List')
      .setDescription(`Use \`${prefix}help <command>\` for detailed information about a specific command.`);

    for (const [category, commands] of Object.entries(categories)) {
      if (commands.length > 0) {
        const commandNames = commands.map(cmd => `\`${cmd.name}\``).join(', ');
        embed.addFields({ 
          name: `${category.charAt(0).toUpperCase() + category.slice(1)} (${commands.length})`, 
          value: commandNames, 
          inline: false 
        });
      }
    }

    return embed;
  }
}

module.exports = new EmbedTemplates();
