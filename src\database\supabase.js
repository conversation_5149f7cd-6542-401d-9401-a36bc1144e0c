const { createClient } = require('@supabase/supabase-js');
const config = require('../config/config');
const logger = require('../utils/logger');

// Create Supabase client
let supabase = null;

function initializeSupabase() {
  if (!config.supabase.url || !config.supabase.serviceKey) {
    logger.warn('Supabase configuration missing, falling back to local database');
    return null;
  }

  try {
    // Use service key for server-side operations
    supabase = createClient(config.supabase.url, config.supabase.serviceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    logger.info('Supabase client initialized successfully');
    return supabase;
  } catch (error) {
    logger.error('Failed to initialize Supabase client:', error);
    return null;
  }
}

// Database utility functions for Supabase
class SupabaseUtils {
  constructor() {
    this.client = initializeSupabase();
  }

  // Test connection
  async testConnection() {
    if (!this.client) return false;
    
    try {
      const { data, error } = await this.client
        .from('guilds')
        .select('count')
        .limit(1);
      
      if (error && error.code !== 'PGRST116') { // PGRST116 = table doesn't exist
        throw error;
      }
      
      logger.info('Supabase connection test successful');
      return true;
    } catch (error) {
      logger.error('Supabase connection test failed:', error);
      return false;
    }
  }

  // Initialize database tables
  async initializeTables() {
    if (!this.client) return false;

    try {
      // Create tables using SQL
      const { error } = await this.client.rpc('create_nexusbot_tables');
      
      if (error) {
        // If the function doesn't exist, create tables manually
        await this.createTablesManually();
      }
      
      logger.info('Supabase tables initialized successfully');
      return true;
    } catch (error) {
      logger.error('Failed to initialize Supabase tables:', error);
      return false;
    }
  }

  // Create tables manually
  async createTablesManually() {
    const tables = [
      // Guilds table
      `
      CREATE TABLE IF NOT EXISTS guilds (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        prefix TEXT DEFAULT '!' NOT NULL,
        moderation_channel TEXT,
        welcome_channel TEXT,
        welcome_message TEXT,
        auto_role TEXT,
        mute_role TEXT,
        disabled_commands JSONB DEFAULT '[]'::jsonb,
        auto_moderation BOOLEAN DEFAULT true,
        spam_threshold INTEGER DEFAULT 5,
        max_warnings INTEGER DEFAULT 3,
        leveling_enabled BOOLEAN DEFAULT true,
        economy_enabled BOOLEAN DEFAULT true,
        music_enabled BOOLEAN DEFAULT true,
        settings JSONB DEFAULT '{}'::jsonb,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,
      
      // Users table
      `
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT NOT NULL,
        discriminator TEXT,
        avatar TEXT,
        is_bot BOOLEAN DEFAULT false,
        total_messages INTEGER DEFAULT 0,
        total_commands INTEGER DEFAULT 0,
        last_seen TIMESTAMP WITH TIME ZONE,
        preferences JSONB DEFAULT '{}'::jsonb,
        blacklisted BOOLEAN DEFAULT false,
        blacklist_reason TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,
      
      // Economy table
      `
      CREATE TABLE IF NOT EXISTS economy (
        id SERIAL PRIMARY KEY,
        user_id TEXT NOT NULL REFERENCES users(id),
        guild_id TEXT NOT NULL REFERENCES guilds(id),
        balance BIGINT DEFAULT 1000,
        bank BIGINT DEFAULT 0,
        last_daily TIMESTAMP WITH TIME ZONE,
        last_weekly TIMESTAMP WITH TIME ZONE,
        last_work TIMESTAMP WITH TIME ZONE,
        total_earned BIGINT DEFAULT 0,
        total_spent BIGINT DEFAULT 0,
        inventory JSONB DEFAULT '{}'::jsonb,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, guild_id)
      );
      `,
      
      // Levels table
      `
      CREATE TABLE IF NOT EXISTS levels (
        id SERIAL PRIMARY KEY,
        user_id TEXT NOT NULL REFERENCES users(id),
        guild_id TEXT NOT NULL REFERENCES guilds(id),
        xp BIGINT DEFAULT 0,
        level INTEGER DEFAULT 1,
        total_xp BIGINT DEFAULT 0,
        message_count INTEGER DEFAULT 0,
        voice_time BIGINT DEFAULT 0,
        last_xp_gain TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, guild_id)
      );
      `,
      
      // Warnings table
      `
      CREATE TABLE IF NOT EXISTS warnings (
        id SERIAL PRIMARY KEY,
        user_id TEXT NOT NULL REFERENCES users(id),
        guild_id TEXT NOT NULL REFERENCES guilds(id),
        moderator_id TEXT NOT NULL,
        reason TEXT NOT NULL,
        active BOOLEAN DEFAULT true,
        expires_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,
      
      // Moderation logs table
      `
      CREATE TABLE IF NOT EXISTS moderation_logs (
        id SERIAL PRIMARY KEY,
        guild_id TEXT NOT NULL REFERENCES guilds(id),
        action TEXT NOT NULL CHECK (action IN ('kick', 'ban', 'unban', 'timeout', 'warn', 'clear', 'mute', 'unmute')),
        target_id TEXT NOT NULL,
        target_tag TEXT NOT NULL,
        moderator_id TEXT NOT NULL,
        moderator_tag TEXT NOT NULL,
        reason TEXT,
        duration TEXT,
        channel_id TEXT,
        message_id TEXT,
        additional_data JSONB DEFAULT '{}'::jsonb,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,
      
      // Giveaways table
      `
      CREATE TABLE IF NOT EXISTS giveaways (
        id SERIAL PRIMARY KEY,
        guild_id TEXT NOT NULL REFERENCES guilds(id),
        channel_id TEXT NOT NULL,
        message_id TEXT NOT NULL UNIQUE,
        host_id TEXT NOT NULL,
        prize TEXT NOT NULL,
        winner_count INTEGER DEFAULT 1,
        requirements JSONB DEFAULT '{}'::jsonb,
        participants JSONB DEFAULT '[]'::jsonb,
        winners JSONB DEFAULT '[]'::jsonb,
        ends_at TIMESTAMP WITH TIME ZONE NOT NULL,
        ended BOOLEAN DEFAULT false,
        active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,
      
      // Polls table
      `
      CREATE TABLE IF NOT EXISTS polls (
        id SERIAL PRIMARY KEY,
        guild_id TEXT NOT NULL REFERENCES guilds(id),
        channel_id TEXT NOT NULL,
        message_id TEXT NOT NULL UNIQUE,
        creator_id TEXT NOT NULL,
        question TEXT NOT NULL,
        options JSONB NOT NULL,
        votes JSONB DEFAULT '{}'::jsonb,
        allow_multiple BOOLEAN DEFAULT false,
        anonymous BOOLEAN DEFAULT false,
        ends_at TIMESTAMP WITH TIME ZONE,
        ended BOOLEAN DEFAULT false,
        active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `
    ];

    for (const tableSQL of tables) {
      const { error } = await this.client.rpc('exec_sql', { sql: tableSQL });
      if (error) {
        logger.error('Error creating table:', error);
        throw error;
      }
    }

    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_economy_user_guild ON economy(user_id, guild_id);',
      'CREATE INDEX IF NOT EXISTS idx_levels_user_guild ON levels(user_id, guild_id);',
      'CREATE INDEX IF NOT EXISTS idx_warnings_user_guild ON warnings(user_id, guild_id);',
      'CREATE INDEX IF NOT EXISTS idx_warnings_active ON warnings(active);',
      'CREATE INDEX IF NOT EXISTS idx_moderation_logs_guild ON moderation_logs(guild_id);',
      'CREATE INDEX IF NOT EXISTS idx_giveaways_guild ON giveaways(guild_id);',
      'CREATE INDEX IF NOT EXISTS idx_polls_guild ON polls(guild_id);'
    ];

    for (const indexSQL of indexes) {
      const { error } = await this.client.rpc('exec_sql', { sql: indexSQL });
      if (error) {
        logger.warn('Error creating index:', error);
      }
    }
  }

  // Health check
  async healthCheck() {
    if (!this.client) {
      return {
        status: 'unhealthy',
        error: 'Supabase client not initialized',
        timestamp: new Date().toISOString()
      };
    }

    try {
      const { data, error } = await this.client
        .from('guilds')
        .select('count')
        .limit(1);

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return {
        status: 'healthy',
        database: 'supabase',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = {
  supabase,
  SupabaseUtils: new SupabaseUtils(),
  initializeSupabase
};
