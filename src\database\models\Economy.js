const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Economy = sequelize.define('Economy', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    guildId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'guilds',
        key: 'id'
      }
    },
    balance: {
      type: DataTypes.BIGINT,
      defaultValue: 1000,
      allowNull: false
    },
    bank: {
      type: DataTypes.BIGINT,
      defaultValue: 0,
      allowNull: false
    },
    lastDaily: {
      type: DataTypes.DATE,
      allowNull: true
    },
    lastWeekly: {
      type: DataTypes.DATE,
      allowNull: true
    },
    lastWork: {
      type: DataTypes.DATE,
      allowNull: true
    },
    totalEarned: {
      type: DataTypes.BIGINT,
      defaultValue: 0,
      allowNull: false
    },
    totalSpent: {
      type: DataTypes.BIGINT,
      defaultValue: 0,
      allowNull: false
    },
    inventory: {
      type: DataTypes.JSON,
      defaultValue: {},
      allowNull: false
    }
  }, {
    tableName: 'economy',
    timestamps: true,
    indexes: [
      {
        fields: ['userId', 'guildId'],
        unique: true
      },
      {
        fields: ['balance']
      },
      {
        fields: ['totalEarned']
      }
    ]
  });

  Economy.associate = (models) => {
    Economy.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    Economy.belongsTo(models.Guild, {
      foreignKey: 'guildId',
      as: 'guild'
    });
  };

  return Economy;
};
