{"level":"info","message":"Supabase client initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:36:09"}
{"level":"info","message":"Using Supabase as primary database","service":"nexusbot","timestamp":"2025-07-19 14:36:09"}
{"level":"info","message":"Database manager initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:36:09"}
{"level":"info","message":"Database connected successfully (supabase)","service":"nexusbot","timestamp":"2025-07-19 14:36:10"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error loading command play.js: Cannot find module '@discordjs/voice'\nRequire stack:\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\commands\\music\\play.js\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\index.js","requireStack":["C:\\Users\\<USER>\\Downloads\\discord bot\\src\\commands\\music\\play.js","C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js","C:\\Users\\<USER>\\Downloads\\discord bot\\src\\index.js"],"service":"nexusbot","stack":"Error: Cannot find module '@discordjs/voice'\nRequire stack:\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\commands\\music\\play.js\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\commands\\music\\play.js:2:112)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","timestamp":"2025-07-19 14:36:10"}
{"code":50035,"level":"error","message":"Error registering slash commands: Invalid Form Body\nguild_id[NUMBER_TYPE_COERCE]: Value \"your_test_guild_id_here\" is not snowflake.","method":"PUT","rawError":{"code":50035,"errors":{"guild_id":{"_errors":[{"code":"NUMBER_TYPE_COERCE","message":"Value \"your_test_guild_id_here\" is not snowflake."}]}},"message":"Invalid Form Body"},"requestBody":{"json":[{"description":"Ask the magic 8-ball a question","name":"8ball","options":[{"description":"The question to ask the magic 8-ball","name":"question","required":true,"type":3}],"type":1},{"description":"Roll dice with customizable sides and quantity","name":"dice","options":[{"description":"Dice notation (e.g., 2d6, 1d20, 3d10)","name":"dice","required":false,"type":3}],"type":1},{"default_member_permissions":"2","description":"Kick a member from the server","name":"kick","options":[{"description":"The user to kick","name":"user","required":true,"type":6},{"description":"Reason for the kick","name":"reason","required":false,"type":3}],"type":1},{"description":"Show the current music queue","name":"queue","options":[],"type":1},{"description":"Skip the current song","name":"skip","options":[],"type":1},{"description":"Stop music and clear the queue","name":"stop","options":[],"type":1},{"description":"Shows help information for commands","name":"help","options":[{"description":"Specific command to get help for","name":"command","required":false,"type":3}],"type":1},{"description":"Shows bot latency and API response time","name":"ping","options":[],"type":1}]},"service":"nexusbot","stack":"DiscordAPIError[50035]: Invalid Form Body\nguild_id[NUMBER_TYPE_COERCE]: Value \"your_test_guild_id_here\" is not snowflake.\n    at handleErrors (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SequentialHandler.runRequest (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\@discordjs\\rest\\dist\\index.js:1149:23)\n    at async SequentialHandler.queueRequest (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\@discordjs\\rest\\dist\\index.js:980:14)\n    at async _REST.request (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async CommandHandler.registerSlashCommands (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js:98:9)\n    at async CommandHandler.loadCommands (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js:81:9)\n    at async initializeBot (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\index.js:64:5)","status":400,"timestamp":"2025-07-19 14:36:10","url":"https://discord.com/api/v10/applications/1396212968743505930/guilds/your_test_guild_id_here/commands"}
{"level":"info","message":"Supabase client initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:36:33"}
{"level":"info","message":"Using Supabase as primary database","service":"nexusbot","timestamp":"2025-07-19 14:36:33"}
{"level":"info","message":"Database manager initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:36:33"}
{"level":"info","message":"Database connected successfully (supabase)","service":"nexusbot","timestamp":"2025-07-19 14:36:34"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error loading command play.js: Cannot find module '@discordjs/voice'\nRequire stack:\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\commands\\music\\play.js\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\index.js","requireStack":["C:\\Users\\<USER>\\Downloads\\discord bot\\src\\commands\\music\\play.js","C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js","C:\\Users\\<USER>\\Downloads\\discord bot\\src\\index.js"],"service":"nexusbot","stack":"Error: Cannot find module '@discordjs/voice'\nRequire stack:\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\commands\\music\\play.js\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js\n- C:\\Users\\<USER>\\Downloads\\discord bot\\src\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\commands\\music\\play.js:2:112)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","timestamp":"2025-07-19 14:36:34"}
{"code":50035,"level":"error","message":"Error registering slash commands: Invalid Form Body\nguild_id[NUMBER_TYPE_COERCE]: Value \"your_test_guild_id_here\" is not snowflake.","method":"PUT","rawError":{"code":50035,"errors":{"guild_id":{"_errors":[{"code":"NUMBER_TYPE_COERCE","message":"Value \"your_test_guild_id_here\" is not snowflake."}]}},"message":"Invalid Form Body"},"requestBody":{"json":[{"description":"Ask the magic 8-ball a question","name":"8ball","options":[{"description":"The question to ask the magic 8-ball","name":"question","required":true,"type":3}],"type":1},{"description":"Roll dice with customizable sides and quantity","name":"dice","options":[{"description":"Dice notation (e.g., 2d6, 1d20, 3d10)","name":"dice","required":false,"type":3}],"type":1},{"default_member_permissions":"2","description":"Kick a member from the server","name":"kick","options":[{"description":"The user to kick","name":"user","required":true,"type":6},{"description":"Reason for the kick","name":"reason","required":false,"type":3}],"type":1},{"description":"Show the current music queue","name":"queue","options":[],"type":1},{"description":"Skip the current song","name":"skip","options":[],"type":1},{"description":"Stop music and clear the queue","name":"stop","options":[],"type":1},{"description":"Shows help information for commands","name":"help","options":[{"description":"Specific command to get help for","name":"command","required":false,"type":3}],"type":1},{"description":"Shows bot latency and API response time","name":"ping","options":[],"type":1}]},"service":"nexusbot","stack":"DiscordAPIError[50035]: Invalid Form Body\nguild_id[NUMBER_TYPE_COERCE]: Value \"your_test_guild_id_here\" is not snowflake.\n    at handleErrors (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SequentialHandler.runRequest (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\@discordjs\\rest\\dist\\index.js:1149:23)\n    at async SequentialHandler.queueRequest (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\@discordjs\\rest\\dist\\index.js:980:14)\n    at async _REST.request (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async CommandHandler.registerSlashCommands (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js:98:9)\n    at async CommandHandler.loadCommands (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js:81:9)\n    at async initializeBot (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\index.js:64:5)","status":400,"timestamp":"2025-07-19 14:36:34","url":"https://discord.com/api/v10/applications/1396212968743505930/guilds/your_test_guild_id_here/commands"}
{"level":"info","message":"Supabase client initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:37:39"}
{"level":"info","message":"Using Supabase as primary database","service":"nexusbot","timestamp":"2025-07-19 14:37:39"}
{"level":"info","message":"Database manager initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:37:39"}
{"level":"info","message":"Database connected successfully (supabase)","service":"nexusbot","timestamp":"2025-07-19 14:37:39"}
{"level":"info","message":"Registered 5 guild slash commands for development","service":"nexusbot","timestamp":"2025-07-19 14:37:40"}
{"level":"info","message":"Loaded 13 prefix commands and 5 slash commands","service":"nexusbot","timestamp":"2025-07-19 14:37:40"}
{"level":"info","message":"Loaded 3 events","service":"nexusbot","timestamp":"2025-07-19 14:37:40"}
{"level":"info","message":"Bot is ready! Logged in as NexusBot#3069","service":"nexusbot","timestamp":"2025-07-19 14:37:40"}
{"level":"info","message":"Serving 1 guilds with 2 users","service":"nexusbot","timestamp":"2025-07-19 14:37:40"}
{"level":"info","message":"NexusBot is fully operational!","service":"nexusbot","timestamp":"2025-07-19 14:37:40"}
{"level":"info","message":"NexusBot initialization completed successfully","service":"nexusbot","timestamp":"2025-07-19 14:37:40"}
{"level":"error","message":"Error handling slash command: Cannot read properties of undefined (reading 'logger')","service":"nexusbot","stack":"TypeError: Cannot read properties of undefined (reading 'logger')\n    at Object.execute (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\commands\\utility\\help.js:79:14)\n    at CommandHandler.handleSlashCommand (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js:194:21)\n    at Object.execute (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\events\\interactionCreate.js:11:30)\n    at Client.<anonymous> (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\eventHandler.js:46:54)\n    at Client.emit (node:events:507:28)\n    at InteractionCreateAction.handle (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\discord.js\\src\\client\\actions\\InteractionCreate.js:101:12)\n    at module.exports [as INTERACTION_CREATE] (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\discord.js\\src\\client\\websocket\\handlers\\INTERACTION_CREATE.js:4:36)\n    at WebSocketManager.handlePacket (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:351:31)\n    at WebSocketManager.<anonymous> (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:235:12)\n    at WebSocketManager.emit (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)","timestamp":"2025-07-19 14:37:49"}
{"arguments":[],"command":"help","guild":"1396214129609539696","level":"info","message":"Command executed","service":"nexusbot","timestamp":"2025-07-19 14:38:12","user":"1320058519642177668"}
{"arguments":[],"command":"ping","guild":"1396214129609539696","level":"info","message":"Command executed","service":"nexusbot","timestamp":"2025-07-19 14:38:33","user":"1320058519642177668"}
{"level":"info","message":"Supabase client initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:38:55"}
{"level":"info","message":"Using Supabase as primary database","service":"nexusbot","timestamp":"2025-07-19 14:38:55"}
{"level":"info","message":"Database manager initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:38:55"}
{"level":"info","message":"Database connected successfully (supabase)","service":"nexusbot","timestamp":"2025-07-19 14:38:56"}
{"level":"info","message":"Registered 5 guild slash commands for development","service":"nexusbot","timestamp":"2025-07-19 14:38:56"}
{"level":"info","message":"Loaded 13 prefix commands and 5 slash commands","service":"nexusbot","timestamp":"2025-07-19 14:38:56"}
{"level":"info","message":"Loaded 3 events","service":"nexusbot","timestamp":"2025-07-19 14:38:56"}
{"level":"info","message":"NexusBot initialization completed successfully","service":"nexusbot","timestamp":"2025-07-19 14:38:57"}
{"level":"info","message":"Bot is ready! Logged in as NexusBot#3069","service":"nexusbot","timestamp":"2025-07-19 14:38:57"}
{"level":"info","message":"Serving 1 guilds with 2 users","service":"nexusbot","timestamp":"2025-07-19 14:38:57"}
{"level":"info","message":"NexusBot is fully operational!","service":"nexusbot","timestamp":"2025-07-19 14:38:57"}
{"level":"info","message":"Supabase client initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:39:08"}
{"level":"info","message":"Using Supabase as primary database","service":"nexusbot","timestamp":"2025-07-19 14:39:08"}
{"level":"info","message":"Database manager initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:39:08"}
{"level":"info","message":"Database connected successfully (supabase)","service":"nexusbot","timestamp":"2025-07-19 14:39:08"}
{"level":"info","message":"Registered 5 guild slash commands for development","service":"nexusbot","timestamp":"2025-07-19 14:39:09"}
{"level":"info","message":"Loaded 13 prefix commands and 5 slash commands","service":"nexusbot","timestamp":"2025-07-19 14:39:09"}
{"level":"info","message":"Loaded 3 events","service":"nexusbot","timestamp":"2025-07-19 14:39:09"}
{"level":"info","message":"NexusBot initialization completed successfully","service":"nexusbot","timestamp":"2025-07-19 14:39:09"}
{"level":"info","message":"Bot is ready! Logged in as NexusBot#3069","service":"nexusbot","timestamp":"2025-07-19 14:39:09"}
{"level":"info","message":"Serving 1 guilds with 2 users","service":"nexusbot","timestamp":"2025-07-19 14:39:09"}
{"level":"info","message":"NexusBot is fully operational!","service":"nexusbot","timestamp":"2025-07-19 14:39:09"}
{"level":"info","message":"Supabase client initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:39:26"}
{"level":"info","message":"Using Supabase as primary database","service":"nexusbot","timestamp":"2025-07-19 14:39:26"}
{"level":"info","message":"Database manager initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:39:26"}
{"level":"info","message":"Database connected successfully (supabase)","service":"nexusbot","timestamp":"2025-07-19 14:39:27"}
{"level":"info","message":"Registered 5 guild slash commands for development","service":"nexusbot","timestamp":"2025-07-19 14:39:27"}
{"level":"info","message":"Loaded 13 prefix commands and 5 slash commands","service":"nexusbot","timestamp":"2025-07-19 14:39:27"}
{"level":"info","message":"Loaded 3 events","service":"nexusbot","timestamp":"2025-07-19 14:39:27"}
{"level":"info","message":"NexusBot initialization completed successfully","service":"nexusbot","timestamp":"2025-07-19 14:39:28"}
{"level":"info","message":"Bot is ready! Logged in as NexusBot#3069","service":"nexusbot","timestamp":"2025-07-19 14:39:28"}
{"level":"info","message":"Serving 1 guilds with 2 users","service":"nexusbot","timestamp":"2025-07-19 14:39:28"}
{"level":"info","message":"NexusBot is fully operational!","service":"nexusbot","timestamp":"2025-07-19 14:39:28"}
{"level":"info","message":"Supabase client initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:39:58"}
{"level":"info","message":"Using Supabase as primary database","service":"nexusbot","timestamp":"2025-07-19 14:39:58"}
{"level":"info","message":"Database manager initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:39:58"}
{"level":"info","message":"Database connected successfully (supabase)","service":"nexusbot","timestamp":"2025-07-19 14:39:59"}
{"level":"info","message":"Registered 5 guild slash commands for development","service":"nexusbot","timestamp":"2025-07-19 14:40:00"}
{"level":"info","message":"Loaded 13 prefix commands and 5 slash commands","service":"nexusbot","timestamp":"2025-07-19 14:40:00"}
{"level":"info","message":"Loaded 3 events","service":"nexusbot","timestamp":"2025-07-19 14:40:00"}
{"level":"info","message":"Bot is ready! Logged in as NexusBot#3069","service":"nexusbot","timestamp":"2025-07-19 14:40:01"}
{"level":"info","message":"Serving 1 guilds with 2 users","service":"nexusbot","timestamp":"2025-07-19 14:40:01"}
{"level":"info","message":"NexusBot is fully operational!","service":"nexusbot","timestamp":"2025-07-19 14:40:01"}
{"level":"info","message":"NexusBot initialization completed successfully","service":"nexusbot","timestamp":"2025-07-19 14:40:01"}
{"level":"info","message":"Supabase client initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:40:10"}
{"level":"info","message":"Using Supabase as primary database","service":"nexusbot","timestamp":"2025-07-19 14:40:10"}
{"level":"info","message":"Database manager initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:40:10"}
{"level":"info","message":"Database connected successfully (supabase)","service":"nexusbot","timestamp":"2025-07-19 14:40:10"}
{"level":"info","message":"Registered 5 guild slash commands for development","service":"nexusbot","timestamp":"2025-07-19 14:40:57"}
{"level":"info","message":"Loaded 13 prefix commands and 5 slash commands","service":"nexusbot","timestamp":"2025-07-19 14:40:57"}
{"level":"info","message":"Loaded 3 events","service":"nexusbot","timestamp":"2025-07-19 14:40:57"}
{"level":"info","message":"NexusBot initialization completed successfully","service":"nexusbot","timestamp":"2025-07-19 14:40:57"}
{"level":"info","message":"Bot is ready! Logged in as NexusBot#3069","service":"nexusbot","timestamp":"2025-07-19 14:40:57"}
{"level":"info","message":"Serving 1 guilds with 2 users","service":"nexusbot","timestamp":"2025-07-19 14:40:57"}
{"level":"info","message":"NexusBot is fully operational!","service":"nexusbot","timestamp":"2025-07-19 14:40:57"}
{"level":"error","message":"Error in help command: Cannot read properties of undefined (reading 'commands')","service":"nexusbot","stack":"TypeError: Cannot read properties of undefined (reading 'commands')\n    at Object.execute (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\commands\\utility\\help.js:56:16)\n    at CommandHandler.handleSlashCommand (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\commandHandler.js:194:21)\n    at Object.execute (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\events\\interactionCreate.js:11:30)\n    at Client.<anonymous> (C:\\Users\\<USER>\\Downloads\\discord bot\\src\\utils\\eventHandler.js:46:54)\n    at Client.emit (node:events:507:28)\n    at InteractionCreateAction.handle (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\discord.js\\src\\client\\actions\\InteractionCreate.js:101:12)\n    at module.exports [as INTERACTION_CREATE] (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\discord.js\\src\\client\\websocket\\handlers\\INTERACTION_CREATE.js:4:36)\n    at WebSocketManager.handlePacket (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:351:31)\n    at WebSocketManager.<anonymous> (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\discord.js\\src\\client\\websocket\\WebSocketManager.js:235:12)\n    at WebSocketManager.emit (C:\\Users\\<USER>\\Downloads\\discord bot\\node_modules\\@vladfrangu\\async_event_emitter\\dist\\index.cjs:287:31)","timestamp":"2025-07-19 14:41:04"}
{"arguments":[],"command":"help","guild":"1396214129609539696","level":"info","message":"Command executed","service":"nexusbot","timestamp":"2025-07-19 14:41:04","user":"1320058519642177668"}
{"level":"info","message":"Supabase client initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:41:31"}
{"level":"info","message":"Using Supabase as primary database","service":"nexusbot","timestamp":"2025-07-19 14:41:31"}
{"level":"info","message":"Database manager initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:41:31"}
{"level":"info","message":"Database connected successfully (supabase)","service":"nexusbot","timestamp":"2025-07-19 14:41:32"}
{"level":"info","message":"Registered 5 guild slash commands for development","service":"nexusbot","timestamp":"2025-07-19 14:41:32"}
{"level":"info","message":"Loaded 13 prefix commands and 5 slash commands","service":"nexusbot","timestamp":"2025-07-19 14:41:32"}
{"level":"info","message":"Loaded 3 events","service":"nexusbot","timestamp":"2025-07-19 14:41:32"}
{"level":"info","message":"NexusBot initialization completed successfully","service":"nexusbot","timestamp":"2025-07-19 14:41:33"}
{"level":"info","message":"Bot is ready! Logged in as NexusBot#3069","service":"nexusbot","timestamp":"2025-07-19 14:41:33"}
{"level":"info","message":"Serving 1 guilds with 2 users","service":"nexusbot","timestamp":"2025-07-19 14:41:33"}
{"level":"info","message":"NexusBot is fully operational!","service":"nexusbot","timestamp":"2025-07-19 14:41:33"}
{"level":"info","message":"Supabase client initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:41:51"}
{"level":"info","message":"Using Supabase as primary database","service":"nexusbot","timestamp":"2025-07-19 14:41:51"}
{"level":"info","message":"Database manager initialized successfully","service":"nexusbot","timestamp":"2025-07-19 14:41:51"}
{"level":"info","message":"Database connected successfully (supabase)","service":"nexusbot","timestamp":"2025-07-19 14:41:52"}
