/**
 * Advanced Reminder System for NexusBot
 * Comprehensive reminder management with recurring options
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const logger = require('../../utils/logger');
const config = require('../../config/config');

module.exports = {
  name: 'reminder',
  description: 'Advanced reminder system',
  category: 'utility',
  cooldown: 3,
  
  data: new SlashCommandBuilder()
    .setName('reminder')
    .setDescription('Manage your reminders')
    .addSubcommand(subcommand =>
      subcommand
        .setName('create')
        .setDescription('Create a new reminder')
        .addStringOption(option =>
          option.setName('time')
            .setDescription('When to remind you (e.g., 1h, 30m, 2d, tomorrow 3pm)')
            .setRequired(true))
        .addStringOption(option =>
          option.setName('message')
            .setDescription('What to remind you about')
            .setRequired(true)
            .setMaxLength(2000))
        .addBooleanOption(option =>
          option.setName('recurring')
            .setDescription('Make this a recurring reminder')
            .setRequired(false))
        .addStringOption(option =>
          option.setName('interval')
            .setDescription('Recurring interval (daily, weekly, monthly)')
            .addChoices(
              { name: 'Daily', value: 'daily' },
              { name: 'Weekly', value: 'weekly' },
              { name: 'Monthly', value: 'monthly' }
            )
            .setRequired(false)))
    .addSubcommand(subcommand =>
      subcommand
        .setName('list')
        .setDescription('List your active reminders'))
    .addSubcommand(subcommand =>
      subcommand
        .setName('delete')
        .setDescription('Delete a reminder')
        .addStringOption(option =>
          option.setName('id')
            .setDescription('Reminder ID to delete')
            .setRequired(true)))
    .addSubcommand(subcommand =>
      subcommand
        .setName('edit')
        .setDescription('Edit an existing reminder')
        .addStringOption(option =>
          option.setName('id')
            .setDescription('Reminder ID to edit')
            .setRequired(true))
        .addStringOption(option =>
          option.setName('message')
            .setDescription('New reminder message')
            .setRequired(false))
        .addStringOption(option =>
          option.setName('time')
            .setDescription('New reminder time')
            .setRequired(false))),

  async execute(interaction) {
    if (!config.features.reminders.enabled) {
      return interaction.reply({
        content: '❌ Reminder system is currently disabled.',
        ephemeral: true
      });
    }

    const subcommand = interaction.options.getSubcommand();
    const timer = logger.startTimer(`reminder_${subcommand}`);

    try {
      switch (subcommand) {
        case 'create':
          await this.createReminder(interaction);
          break;
        case 'list':
          await this.listReminders(interaction);
          break;
        case 'delete':
          await this.deleteReminder(interaction);
          break;
        case 'edit':
          await this.editReminder(interaction);
          break;
        default:
          await interaction.reply({
            content: '❌ Unknown subcommand.',
            ephemeral: true
          });
      }

      timer.end({ success: true, subcommand });

    } catch (error) {
      logger.errorWithContext(error, {
        command: 'reminder',
        subcommand,
        guild: interaction.guild?.id,
        user: interaction.user?.id
      });

      const errorEmbed = new EmbedBuilder()
        .setColor(0xFF0000)
        .setTitle('❌ Reminder Error')
        .setDescription('An error occurred while processing your reminder.')
        .setTimestamp();

      await interaction.reply({
        embeds: [errorEmbed],
        ephemeral: true
      });

      timer.end({ success: false, error: error.message });
    }
  },

  async createReminder(interaction) {
    const timeStr = interaction.options.getString('time');
    const message = interaction.options.getString('message');
    const recurring = interaction.options.getBoolean('recurring') || false;
    const interval = interaction.options.getString('interval');

    // Check user's reminder limit
    const dbManager = require('../../database');
    const userReminderCount = await dbManager.getUserReminders(interaction.user.id, interaction.guild.id);
    
    if (userReminderCount.length >= config.features.reminders.maxPerUser) {
      return interaction.reply({
        content: `❌ You have reached the maximum number of reminders (${config.features.reminders.maxPerUser}).`,
        ephemeral: true
      });
    }

    // Parse the time
    const reminderTime = this.parseTime(timeStr);
    if (!reminderTime) {
      return interaction.reply({
        content: '❌ Invalid time format. Examples: `1h`, `30m`, `2d`, `tomorrow 3pm`',
        ephemeral: true
      });
    }

    if (reminderTime <= new Date()) {
      return interaction.reply({
        content: '❌ Reminder time must be in the future.',
        ephemeral: true
      });
    }

    // Validate recurring options
    if (recurring && !interval) {
      return interaction.reply({
        content: '❌ Please specify an interval for recurring reminders.',
        ephemeral: true
      });
    }

    // Create the reminder
    const reminderData = {
      userId: interaction.user.id,
      guildId: interaction.guild.id,
      channelId: interaction.channel.id,
      messageId: null, // Will be set after reply
      content: message,
      reminderTime: reminderTime,
      isRecurring: recurring,
      recurringInterval: interval,
      metadata: {
        originalTimeStr: timeStr,
        createdBy: interaction.user.tag
      }
    };

    const reminder = await dbManager.getOrCreateReminder(
      interaction.user.id,
      interaction.guild.id,
      reminderData
    );

    // Create confirmation embed
    const confirmEmbed = new EmbedBuilder()
      .setColor(0x00FF00)
      .setTitle('⏰ Reminder Created')
      .addFields(
        { name: 'Message', value: message, inline: false },
        { name: 'Time', value: `<t:${Math.floor(reminderTime.getTime() / 1000)}:F>`, inline: true },
        { name: 'Recurring', value: recurring ? `Yes (${interval})` : 'No', inline: true },
        { name: 'ID', value: reminder.id.substring(0, 8), inline: true }
      )
      .setFooter({ text: `Use /reminder delete ${reminder.id.substring(0, 8)} to cancel` })
      .setTimestamp();

    const reply = await interaction.reply({
      embeds: [confirmEmbed],
      ephemeral: false
    });

    // Update reminder with message ID
    reminder.messageId = reply.id;
    await reminder.save();

    // Log the reminder creation
    logger.reminder('create', interaction.user.id, interaction.guild.id, {
      id: reminder.id,
      time: reminderTime.toISOString(),
      recurring: recurring,
      interval: interval
    });
  },

  async listReminders(interaction) {
    const dbManager = require('../../database');
    const reminders = await dbManager.getUserReminders(interaction.user.id, interaction.guild.id);

    if (reminders.length === 0) {
      return interaction.reply({
        content: '📝 You have no active reminders.',
        ephemeral: true
      });
    }

    const embed = new EmbedBuilder()
      .setColor(0x0099FF)
      .setTitle('📝 Your Active Reminders')
      .setDescription(`You have ${reminders.length} active reminder(s)`)
      .setTimestamp();

    // Add reminder fields (limit to 10 for embed limits)
    const displayReminders = reminders.slice(0, 10);
    for (const reminder of displayReminders) {
      const timeLeft = reminder.reminderTime.getTime() - Date.now();
      const timeLeftStr = timeLeft > 0 ? this.formatTimeLeft(timeLeft) : 'Overdue';
      
      embed.addFields({
        name: `ID: ${reminder.id.substring(0, 8)} ${reminder.isRecurring ? '🔄' : ''}`,
        value: `**Message:** ${reminder.content.substring(0, 100)}${reminder.content.length > 100 ? '...' : ''}\n**Time:** <t:${Math.floor(reminder.reminderTime.getTime() / 1000)}:R> (${timeLeftStr})`,
        inline: false
      });
    }

    if (reminders.length > 10) {
      embed.setFooter({ text: `Showing 10 of ${reminders.length} reminders` });
    }

    // Add action buttons
    const row = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('reminder_refresh')
          .setLabel('Refresh')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🔄'),
        new ButtonBuilder()
          .setCustomId('reminder_delete_all')
          .setLabel('Delete All')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🗑️')
      );

    await interaction.reply({
      embeds: [embed],
      components: [row],
      ephemeral: true
    });
  },

  async deleteReminder(interaction) {
    const reminderId = interaction.options.getString('id');
    
    const dbManager = require('../../database');
    const reminders = await dbManager.getUserReminders(interaction.user.id, interaction.guild.id);
    
    const reminder = reminders.find(r => r.id.startsWith(reminderId));
    if (!reminder) {
      return interaction.reply({
        content: '❌ Reminder not found. Use `/reminder list` to see your reminders.',
        ephemeral: true
      });
    }

    await dbManager.deleteReminder(reminder.id);

    const embed = new EmbedBuilder()
      .setColor(0xFF0000)
      .setTitle('🗑️ Reminder Deleted')
      .setDescription(`Reminder "${reminder.content.substring(0, 100)}" has been deleted.`)
      .setTimestamp();

    await interaction.reply({
      embeds: [embed],
      ephemeral: true
    });

    logger.reminder('delete', interaction.user.id, interaction.guild.id, {
      id: reminder.id,
      content: reminder.content.substring(0, 50)
    });
  },

  async editReminder(interaction) {
    const reminderId = interaction.options.getString('id');
    const newMessage = interaction.options.getString('message');
    const newTime = interaction.options.getString('time');

    if (!newMessage && !newTime) {
      return interaction.reply({
        content: '❌ Please specify either a new message or new time.',
        ephemeral: true
      });
    }

    const dbManager = require('../../database');
    const reminders = await dbManager.getUserReminders(interaction.user.id, interaction.guild.id);
    
    const reminder = reminders.find(r => r.id.startsWith(reminderId));
    if (!reminder) {
      return interaction.reply({
        content: '❌ Reminder not found.',
        ephemeral: true
      });
    }

    // Update fields
    if (newMessage) {
      reminder.content = newMessage;
    }

    if (newTime) {
      const parsedTime = this.parseTime(newTime);
      if (!parsedTime || parsedTime <= new Date()) {
        return interaction.reply({
          content: '❌ Invalid time format or time must be in the future.',
          ephemeral: true
        });
      }
      reminder.reminderTime = parsedTime;
    }

    await reminder.save();

    const embed = new EmbedBuilder()
      .setColor(0x00FF00)
      .setTitle('✏️ Reminder Updated')
      .addFields(
        { name: 'Message', value: reminder.content, inline: false },
        { name: 'Time', value: `<t:${Math.floor(reminder.reminderTime.getTime() / 1000)}:F>`, inline: true }
      )
      .setTimestamp();

    await interaction.reply({
      embeds: [embed],
      ephemeral: true
    });

    logger.reminder('edit', interaction.user.id, interaction.guild.id, {
      id: reminder.id,
      changes: { message: !!newMessage, time: !!newTime }
    });
  },

  // Helper methods
  parseTime(timeStr) {
    // Simple time parsing - can be enhanced with libraries like chrono-node
    const now = new Date();
    
    // Handle relative times like "1h", "30m", "2d"
    const relativeMatch = timeStr.match(/^(\d+)([smhdw])$/i);
    if (relativeMatch) {
      const value = parseInt(relativeMatch[1]);
      const unit = relativeMatch[2].toLowerCase();
      
      const multipliers = {
        s: 1000,
        m: 60 * 1000,
        h: 60 * 60 * 1000,
        d: 24 * 60 * 60 * 1000,
        w: 7 * 24 * 60 * 60 * 1000
      };
      
      return new Date(now.getTime() + value * multipliers[unit]);
    }

    // Handle "tomorrow" and "today"
    if (timeStr.toLowerCase().includes('tomorrow')) {
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(9, 0, 0, 0); // Default to 9 AM
      return tomorrow;
    }

    if (timeStr.toLowerCase().includes('today')) {
      const today = new Date(now);
      today.setHours(now.getHours() + 1, 0, 0, 0); // Default to 1 hour from now
      return today;
    }

    // Try to parse as a date
    const parsed = new Date(timeStr);
    if (!isNaN(parsed.getTime())) {
      return parsed;
    }

    return null;
  },

  formatTimeLeft(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }
};
