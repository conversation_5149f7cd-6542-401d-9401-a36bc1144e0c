const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Giveaway = sequelize.define('Giveaway', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    guildId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'guilds',
        key: 'id'
      }
    },
    channelId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    messageId: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    hostId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    prize: {
      type: DataTypes.STRING,
      allowNull: false
    },
    winnerCount: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      allowNull: false
    },
    requirements: {
      type: DataTypes.JSON,
      defaultValue: {},
      allowNull: false
    },
    participants: {
      type: DataTypes.JSON,
      defaultValue: [],
      allowNull: false
    },
    winners: {
      type: DataTypes.JSON,
      defaultValue: [],
      allowNull: false
    },
    endsAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    ended: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    }
  }, {
    tableName: 'giveaways',
    timestamps: true,
    indexes: [
      {
        fields: ['guildId']
      },
      {
        fields: ['messageId']
      },
      {
        fields: ['endsAt']
      },
      {
        fields: ['ended']
      },
      {
        fields: ['active']
      }
    ]
  });

  Giveaway.associate = (models) => {
    Giveaway.belongsTo(models.Guild, {
      foreignKey: 'guildId',
      as: 'guild'
    });
  };

  return Giveaway;
};
