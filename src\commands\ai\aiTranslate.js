/**
 * AI Translation Command for NexusBot
 * Uses Hugging Face translation models
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { HfInference } = require('@huggingface/inference');
const config = require('../../config/config');
const logger = require('../../utils/logger');

module.exports = {
  name: 'aitranslate',
  description: 'Translate text using AI',
  category: 'ai',
  cooldown: 3,
  
  data: new SlashCommandBuilder()
    .setName('aitranslate')
    .setDescription('Translate text between languages using AI')
    .addStringOption(option =>
      option.setName('text')
        .setDescription('Text to translate')
        .setRequired(true)
        .setMaxLength(1000))
    .addStringOption(option =>
      option.setName('to_language')
        .setDescription('Target language')
        .addChoices(
          { name: 'Spanish', value: 'es' },
          { name: 'French', value: 'fr' },
          { name: 'German', value: 'de' },
          { name: 'Italian', value: 'it' },
          { name: 'Portuguese', value: 'pt' },
          { name: 'Russian', value: 'ru' },
          { name: 'Japanese', value: 'ja' },
          { name: 'Korean', value: 'ko' },
          { name: 'Chinese (Simplified)', value: 'zh' },
          { name: 'Arabic', value: 'ar' },
          { name: 'Hindi', value: 'hi' },
          { name: 'Dutch', value: 'nl' },
          { name: 'Swedish', value: 'sv' },
          { name: 'Norwegian', value: 'no' },
          { name: 'Finnish', value: 'fi' }
        )
        .setRequired(true))
    .addStringOption(option =>
      option.setName('from_language')
        .setDescription('Source language (auto-detect if not specified)')
        .addChoices(
          { name: 'Auto-detect', value: 'auto' },
          { name: 'English', value: 'en' },
          { name: 'Spanish', value: 'es' },
          { name: 'French', value: 'fr' },
          { name: 'German', value: 'de' },
          { name: 'Italian', value: 'it' },
          { name: 'Portuguese', value: 'pt' },
          { name: 'Russian', value: 'ru' },
          { name: 'Japanese', value: 'ja' },
          { name: 'Korean', value: 'ko' },
          { name: 'Chinese (Simplified)', value: 'zh' },
          { name: 'Arabic', value: 'ar' },
          { name: 'Hindi', value: 'hi' }
        )
        .setRequired(false)),

  async execute(interaction) {
    if (!config.ai.enabled || !config.apis.huggingface.apiKey) {
      return interaction.reply({
        content: '❌ AI translation features are not enabled or configured.',
        ephemeral: true
      });
    }

    const timer = logger.startTimer('ai_translation');
    
    try {
      await interaction.deferReply();

      const text = interaction.options.getString('text');
      const toLanguage = interaction.options.getString('to_language');
      const fromLanguage = interaction.options.getString('from_language') || 'auto';

      // Initialize Hugging Face Inference
      const hf = new HfInference(config.apis.huggingface.apiKey);

      // Detect source language if auto-detect is enabled
      let detectedLanguage = fromLanguage;
      if (fromLanguage === 'auto') {
        detectedLanguage = await this.detectLanguage(text);
      }

      // Perform translation
      const translationResult = await this.translateText(hf, text, detectedLanguage, toLanguage);

      // Create response embed
      const responseEmbed = new EmbedBuilder()
        .setColor(0x00D4FF)
        .setTitle('🌐 AI Translation')
        .addFields(
          {
            name: `📝 Original (${this.getLanguageName(detectedLanguage)})`,
            value: `\`\`\`${text}\`\`\``,
            inline: false
          },
          {
            name: `🔄 Translation (${this.getLanguageName(toLanguage)})`,
            value: `\`\`\`${translationResult.translation}\`\`\``,
            inline: false
          }
        )
        .setTimestamp();

      if (translationResult.confidence) {
        responseEmbed.addFields({
          name: '📊 Confidence',
          value: `${(translationResult.confidence * 100).toFixed(1)}%`,
          inline: true
        });
      }

      if (fromLanguage === 'auto' && detectedLanguage !== 'unknown') {
        responseEmbed.addFields({
          name: '🔍 Detected Language',
          value: this.getLanguageName(detectedLanguage),
          inline: true
        });
      }

      responseEmbed.setFooter({
        text: `Model: ${translationResult.model}`
      });

      await interaction.editReply({ embeds: [responseEmbed] });

      // Log the translation
      logger.info('AI translation completed', {
        user: interaction.user.id,
        guild: interaction.guild?.id,
        fromLanguage: detectedLanguage,
        toLanguage,
        textLength: text.length,
        model: translationResult.model
      });

      timer.end({ 
        success: true, 
        fromLanguage: detectedLanguage, 
        toLanguage, 
        model: translationResult.model 
      });

    } catch (error) {
      logger.errorWithContext(error, {
        command: 'aitranslate',
        guild: interaction.guild?.id,
        user: interaction.user?.id
      });

      const errorEmbed = new EmbedBuilder()
        .setColor(0xFF0000)
        .setTitle('🌐 Translation Error')
        .setDescription('Sorry, I encountered an error while translating your text. Please try again.')
        .setTimestamp();

      await interaction.editReply({ embeds: [errorEmbed] });
      timer.end({ success: false, error: error.message });
    }
  },

  async detectLanguage(text) {
    try {
      // Simple language detection based on character patterns
      // This is a basic implementation - you could use a proper language detection model
      
      // Check for common patterns
      if (/[а-яё]/i.test(text)) return 'ru'; // Cyrillic
      if (/[αβγδεζηθικλμνξοπρστυφχψω]/i.test(text)) return 'el'; // Greek
      if (/[ひらがなカタカナ]/i.test(text)) return 'ja'; // Japanese
      if (/[가-힣]/i.test(text)) return 'ko'; // Korean
      if (/[一-龯]/i.test(text)) return 'zh'; // Chinese
      if (/[ا-ي]/i.test(text)) return 'ar'; // Arabic
      if (/[अ-ह]/i.test(text)) return 'hi'; // Hindi
      
      // For Latin-based languages, default to English
      return 'en';
    } catch (error) {
      logger.warn('Language detection failed:', error.message);
      return 'en'; // Default to English
    }
  },

  async translateText(hf, text, fromLang, toLang) {
    try {
      // Try different translation models based on language pair
      const models = this.getTranslationModels(fromLang, toLang);
      
      let translation = null;
      let usedModel = null;
      let confidence = 0.8;

      for (const model of models) {
        try {
          const result = await hf.translation({
            model: model,
            inputs: text
          });

          if (result && result.translation_text) {
            translation = result.translation_text;
            usedModel = model;
            break;
          }
        } catch (modelError) {
          logger.warn(`Translation model ${model} failed:`, modelError.message);
          continue;
        }
      }

      // Fallback to Google Translate API if available
      if (!translation && config.apis.googleTranslate) {
        try {
          const googleTranslate = require('google-translate-api');
          const result = await googleTranslate(text, { from: fromLang, to: toLang });
          translation = result.text;
          usedModel = 'google-translate-api';
          confidence = 0.9;
        } catch (error) {
          logger.warn('Google Translate fallback failed:', error.message);
        }
      }

      if (!translation) {
        throw new Error('All translation methods failed');
      }

      return {
        translation: translation,
        model: usedModel,
        confidence: confidence
      };

    } catch (error) {
      logger.error('Translation failed:', error);
      return {
        translation: 'Translation failed. Please try again or use a different language pair.',
        model: 'fallback',
        confidence: 0.1
      };
    }
  },

  getTranslationModels(fromLang, toLang) {
    // Return appropriate models based on language pair
    const models = [];
    
    // Helsinki-NLP models for common language pairs
    if (fromLang === 'en') {
      models.push(`Helsinki-NLP/opus-mt-en-${toLang}`);
    } else if (toLang === 'en') {
      models.push(`Helsinki-NLP/opus-mt-${fromLang}-en`);
    }
    
    // Add more general models
    models.push('Helsinki-NLP/opus-mt-mul-en'); // Multilingual to English
    models.push('facebook/mbart-large-50-many-to-many-mmt'); // Many-to-many
    
    return models;
  },

  getLanguageName(code) {
    const languages = {
      'auto': 'Auto-detect',
      'en': 'English',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ru': 'Russian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese (Simplified)',
      'ar': 'Arabic',
      'hi': 'Hindi',
      'nl': 'Dutch',
      'sv': 'Swedish',
      'no': 'Norwegian',
      'fi': 'Finnish',
      'el': 'Greek',
      'unknown': 'Unknown'
    };

    return languages[code] || code.toUpperCase();
  }
};
