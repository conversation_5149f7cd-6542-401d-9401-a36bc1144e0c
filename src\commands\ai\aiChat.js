/**
 * AI Chat Command for NexusBot
 * Uses Hugging Face models for conversational AI
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { HfInference } = require('@huggingface/inference');
const config = require('../../config/config');
const logger = require('../../utils/logger');
const cacheManager = require('../../utils/cacheManager');

module.exports = {
  name: 'aichat',
  description: 'Chat with AI using Hugging Face models',
  category: 'ai',
  cooldown: 5,
  
  data: new SlashCommandBuilder()
    .setName('aichat')
    .setDescription('Have a conversation with <PERSON>')
    .addStringOption(option =>
      option.setName('message')
        .setDescription('Your message to the AI')
        .setRequired(true)
        .setMaxLength(500))
    .addStringOption(option =>
      option.setName('personality')
        .setDescription('AI personality to use')
        .addChoices(
          { name: 'Helpful Assistant', value: 'helpful' },
          { name: 'Friendly Companion', value: 'friendly' },
          { name: 'Professional', value: 'professional' },
          { name: 'Creative Writer', value: 'creative' },
          { name: 'Tech Expert', value: 'tech' }
        )
        .setRequired(false))
    .addBooleanOption(option =>
      option.setName('remember_context')
        .setDescription('Remember previous messages in this conversation')
        .setRequired(false)),

  async execute(interaction) {
    if (!config.ai.enabled || !config.apis.huggingface.apiKey) {
      return interaction.reply({
        content: '❌ AI chat features are not enabled or configured.',
        ephemeral: true
      });
    }

    const timer = logger.startTimer('ai_chat');
    
    try {
      await interaction.deferReply();

      const message = interaction.options.getString('message');
      const personality = interaction.options.getString('personality') || 'helpful';
      const rememberContext = interaction.options.getBoolean('remember_context') || false;

      // Initialize Hugging Face Inference
      const hf = new HfInference(config.apis.huggingface.apiKey);

      // Get conversation context if enabled
      let conversationHistory = [];
      if (rememberContext) {
        const contextKey = `ai_chat_context:${interaction.user.id}:${interaction.channel.id}`;
        conversationHistory = await cacheManager.get(contextKey) || [];
      }

      // Generate AI response
      const aiResponse = await this.generateResponse(hf, message, personality, conversationHistory);

      // Create response embed
      const responseEmbed = new EmbedBuilder()
        .setColor(0x00D4FF)
        .setAuthor({
          name: `NexusBot AI (${this.getPersonalityName(personality)})`,
          iconURL: interaction.client.user.displayAvatarURL()
        })
        .setDescription(aiResponse.response)
        .addFields({
          name: 'Your Message',
          value: `> ${message}`,
          inline: false
        })
        .setFooter({
          text: `Model: ${aiResponse.model} • Confidence: ${(aiResponse.confidence * 100).toFixed(1)}%`
        })
        .setTimestamp();

      // Update conversation history
      if (rememberContext) {
        conversationHistory.push(
          { role: 'user', content: message },
          { role: 'assistant', content: aiResponse.response }
        );

        // Keep only last 10 messages to prevent context from getting too long
        if (conversationHistory.length > 20) {
          conversationHistory = conversationHistory.slice(-20);
        }

        const contextKey = `ai_chat_context:${interaction.user.id}:${interaction.channel.id}`;
        await cacheManager.set(contextKey, conversationHistory, 3600); // Cache for 1 hour
      }

      await interaction.editReply({ embeds: [responseEmbed] });

      // Log the AI chat
      logger.info('AI chat interaction', {
        user: interaction.user.id,
        guild: interaction.guild?.id,
        personality,
        messageLength: message.length,
        responseLength: aiResponse.response.length,
        model: aiResponse.model
      });

      timer.end({ success: true, personality, model: aiResponse.model });

    } catch (error) {
      logger.errorWithContext(error, {
        command: 'aichat',
        guild: interaction.guild?.id,
        user: interaction.user?.id
      });

      const errorEmbed = new EmbedBuilder()
        .setColor(0xFF0000)
        .setTitle('🤖 AI Chat Error')
        .setDescription('Sorry, I encountered an error while processing your message. Please try again.')
        .setTimestamp();

      await interaction.editReply({ embeds: [errorEmbed] });
      timer.end({ success: false, error: error.message });
    }
  },

  async generateResponse(hf, message, personality, conversationHistory = []) {
    try {
      // Build the prompt with personality and context
      const systemPrompt = this.getSystemPrompt(personality);
      const fullPrompt = this.buildPrompt(systemPrompt, message, conversationHistory);

      // Try different models in order of preference
      const models = [
        'microsoft/DialoGPT-medium',
        'facebook/blenderbot-400M-distill',
        'microsoft/DialoGPT-small'
      ];

      let response = null;
      let usedModel = null;

      for (const model of models) {
        try {
          const result = await hf.textGeneration({
            model: model,
            inputs: fullPrompt,
            parameters: {
              max_new_tokens: 150,
              temperature: 0.7,
              do_sample: true,
              top_p: 0.9,
              repetition_penalty: 1.1
            }
          });

          if (result && result.generated_text) {
            response = this.cleanResponse(result.generated_text, fullPrompt);
            usedModel = model;
            break;
          }
        } catch (modelError) {
          logger.warn(`Model ${model} failed:`, modelError.message);
          continue;
        }
      }

      if (!response) {
        // Fallback to a simple response
        response = this.getFallbackResponse(personality);
        usedModel = 'fallback';
      }

      return {
        response: response,
        model: usedModel,
        confidence: usedModel === 'fallback' ? 0.3 : 0.8
      };

    } catch (error) {
      logger.error('AI response generation failed:', error);
      return {
        response: this.getFallbackResponse(personality),
        model: 'fallback',
        confidence: 0.1
      };
    }
  },

  getSystemPrompt(personality) {
    const prompts = {
      helpful: "You are a helpful AI assistant. Provide clear, accurate, and useful responses. Be concise but informative.",
      friendly: "You are a friendly and cheerful AI companion. Be warm, encouraging, and conversational in your responses.",
      professional: "You are a professional AI assistant. Maintain a formal, respectful tone while being helpful and efficient.",
      creative: "You are a creative AI writer. Be imaginative, expressive, and engaging in your responses. Use vivid language.",
      tech: "You are a technical AI expert. Focus on providing accurate technical information and solutions. Be precise and detailed."
    };

    return prompts[personality] || prompts.helpful;
  },

  buildPrompt(systemPrompt, message, conversationHistory) {
    let prompt = systemPrompt + "\n\n";

    // Add conversation history
    if (conversationHistory.length > 0) {
      prompt += "Previous conversation:\n";
      conversationHistory.slice(-6).forEach(msg => { // Last 3 exchanges
        prompt += `${msg.role === 'user' ? 'Human' : 'Assistant'}: ${msg.content}\n`;
      });
      prompt += "\n";
    }

    prompt += `Human: ${message}\nAssistant:`;
    return prompt;
  },

  cleanResponse(generatedText, originalPrompt) {
    // Remove the original prompt from the response
    let response = generatedText.replace(originalPrompt, '').trim();
    
    // Remove common artifacts
    response = response.replace(/^(Assistant:|AI:|Bot:)/i, '').trim();
    response = response.replace(/Human:.*$/s, '').trim();
    
    // Limit length
    if (response.length > 1000) {
      response = response.substring(0, 1000) + '...';
    }

    // Ensure we have a response
    if (!response || response.length < 3) {
      return "I'm not sure how to respond to that. Could you try rephrasing your message?";
    }

    return response;
  },

  getFallbackResponse(personality) {
    const responses = {
      helpful: "I'm here to help! Could you please rephrase your question or try asking something else?",
      friendly: "Hey there! I'm having a bit of trouble understanding. Could you try asking in a different way? 😊",
      professional: "I apologize, but I'm unable to process your request at this time. Please try again with a different query.",
      creative: "My creative circuits are sparking with possibilities, but I need a clearer prompt to weave my response!",
      tech: "System processing error. Please reformulate your query with more specific parameters."
    };

    return responses[personality] || responses.helpful;
  },

  getPersonalityName(personality) {
    const names = {
      helpful: 'Helpful Assistant',
      friendly: 'Friendly Companion',
      professional: 'Professional Assistant',
      creative: 'Creative Writer',
      tech: 'Tech Expert'
    };

    return names[personality] || 'AI Assistant';
  }
};
