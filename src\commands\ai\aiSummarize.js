/**
 * AI Summarization Command for NexusBot
 * Uses Hugging Face summarization models
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { HfInference } = require('@huggingface/inference');
const config = require('../../config/config');
const logger = require('../../utils/logger');

module.exports = {
  name: 'aisummarize',
  description: 'Summarize text using AI',
  category: 'ai',
  cooldown: 5,
  
  data: new SlashCommandBuilder()
    .setName('aisummarize')
    .setDescription('Summarize long text using AI')
    .addStringOption(option =>
      option.setName('text')
        .setDescription('Text to summarize')
        .setRequired(true)
        .setMaxLength(4000))
    .addStringOption(option =>
      option.setName('length')
        .setDescription('Summary length')
        .addChoices(
          { name: 'Short (1-2 sentences)', value: 'short' },
          { name: 'Medium (3-5 sentences)', value: 'medium' },
          { name: 'Long (6-10 sentences)', value: 'long' }
        )
        .setRequired(false))
    .addStringOption(option =>
      option.setName('style')
        .setDescription('Summary style')
        .addChoices(
          { name: 'Bullet Points', value: 'bullets' },
          { name: 'Paragraph', value: 'paragraph' },
          { name: 'Key Points', value: 'keypoints' },
          { name: 'Abstract', value: 'abstract' }
        )
        .setRequired(false))
    .addStringOption(option =>
      option.setName('url')
        .setDescription('URL to fetch and summarize content from')
        .setRequired(false)),

  async execute(interaction) {
    if (!config.ai.enabled || !config.apis.huggingface.apiKey) {
      return interaction.reply({
        content: '❌ AI summarization features are not enabled or configured.',
        ephemeral: true
      });
    }

    const timer = logger.startTimer('ai_summarization');
    
    try {
      await interaction.deferReply();

      let text = interaction.options.getString('text');
      const url = interaction.options.getString('url');
      const length = interaction.options.getString('length') || 'medium';
      const style = interaction.options.getString('style') || 'paragraph';

      // If URL is provided, fetch content
      if (url) {
        try {
          const fetchedContent = await this.fetchUrlContent(url);
          text = fetchedContent.substring(0, 4000); // Limit to 4000 chars
        } catch (error) {
          return interaction.editReply({
            content: '❌ Failed to fetch content from the provided URL. Please check the URL and try again.'
          });
        }
      }

      // Validate text length
      if (text.length < 100) {
        return interaction.editReply({
          content: '❌ Text is too short to summarize. Please provide at least 100 characters.'
        });
      }

      // Initialize Hugging Face Inference
      const hf = new HfInference(config.apis.huggingface.apiKey);

      // Generate summary
      const summaryResult = await this.generateSummary(hf, text, length, style);

      // Create response embed
      const responseEmbed = new EmbedBuilder()
        .setColor(0x00D4FF)
        .setTitle('📄 AI Text Summary')
        .addFields(
          {
            name: '📝 Original Text',
            value: text.length > 500 ? `${text.substring(0, 500)}...` : text,
            inline: false
          },
          {
            name: '📋 Summary',
            value: summaryResult.summary,
            inline: false
          }
        )
        .addFields(
          {
            name: '📊 Compression Ratio',
            value: `${((1 - summaryResult.summary.length / text.length) * 100).toFixed(1)}%`,
            inline: true
          },
          {
            name: '📏 Length',
            value: this.getLengthName(length),
            inline: true
          },
          {
            name: '🎨 Style',
            value: this.getStyleName(style),
            inline: true
          }
        )
        .setFooter({
          text: `Model: ${summaryResult.model} • Original: ${text.length} chars • Summary: ${summaryResult.summary.length} chars`
        })
        .setTimestamp();

      if (url) {
        responseEmbed.addFields({
          name: '🔗 Source URL',
          value: url,
          inline: false
        });
      }

      await interaction.editReply({ embeds: [responseEmbed] });

      // Log the summarization
      logger.info('AI summarization completed', {
        user: interaction.user.id,
        guild: interaction.guild?.id,
        originalLength: text.length,
        summaryLength: summaryResult.summary.length,
        compressionRatio: (1 - summaryResult.summary.length / text.length),
        length,
        style,
        hasUrl: !!url,
        model: summaryResult.model
      });

      timer.end({ 
        success: true, 
        length, 
        style, 
        model: summaryResult.model,
        compressionRatio: (1 - summaryResult.summary.length / text.length)
      });

    } catch (error) {
      logger.errorWithContext(error, {
        command: 'aisummarize',
        guild: interaction.guild?.id,
        user: interaction.user?.id
      });

      const errorEmbed = new EmbedBuilder()
        .setColor(0xFF0000)
        .setTitle('📄 Summarization Error')
        .setDescription('Sorry, I encountered an error while summarizing your text. Please try again.')
        .setTimestamp();

      await interaction.editReply({ embeds: [errorEmbed] });
      timer.end({ success: false, error: error.message });
    }
  },

  async fetchUrlContent(url) {
    const axios = require('axios');
    const cheerio = require('cheerio');

    try {
      // Validate URL
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        throw new Error('Invalid URL protocol');
      }

      // Fetch the webpage
      const response = await axios.get(url, {
        timeout: 10000,
        maxContentLength: 1024 * 1024, // 1MB limit
        headers: {
          'User-Agent': 'NexusBot/2.0 (Discord Bot)'
        }
      });

      // Parse HTML and extract text
      const $ = cheerio.load(response.data);
      
      // Remove script and style elements
      $('script, style, nav, header, footer, aside').remove();
      
      // Extract main content
      let content = '';
      const contentSelectors = ['main', 'article', '.content', '#content', '.post', '.entry'];
      
      for (const selector of contentSelectors) {
        const element = $(selector);
        if (element.length > 0) {
          content = element.text();
          break;
        }
      }
      
      // Fallback to body if no main content found
      if (!content) {
        content = $('body').text();
      }
      
      // Clean up the text
      content = content
        .replace(/\s+/g, ' ')
        .replace(/\n+/g, '\n')
        .trim();
      
      if (content.length < 100) {
        throw new Error('Insufficient content extracted from URL');
      }
      
      return content;

    } catch (error) {
      logger.error('URL content fetch failed:', error);
      throw new Error('Failed to fetch content from URL');
    }
  },

  async generateSummary(hf, text, length, style) {
    try {
      // Determine summary parameters based on length
      const lengthParams = this.getLengthParameters(length, text.length);
      
      // Try different summarization models
      const models = [
        'facebook/bart-large-cnn',
        'sshleifer/distilbart-cnn-12-6',
        'google/pegasus-xsum'
      ];

      let summary = null;
      let usedModel = null;

      for (const model of models) {
        try {
          const result = await hf.summarization({
            model: model,
            inputs: text,
            parameters: {
              max_length: lengthParams.maxLength,
              min_length: lengthParams.minLength,
              do_sample: false
            }
          });

          if (result && result.summary_text) {
            summary = result.summary_text;
            usedModel = model;
            break;
          }
        } catch (modelError) {
          logger.warn(`Summarization model ${model} failed:`, modelError.message);
          continue;
        }
      }

      if (!summary) {
        // Fallback to extractive summarization
        summary = this.extractiveSummary(text, lengthParams);
        usedModel = 'extractive-fallback';
      }

      // Format summary based on style
      summary = this.formatSummary(summary, style);

      return {
        summary: summary,
        model: usedModel
      };

    } catch (error) {
      logger.error('Summary generation failed:', error);
      return {
        summary: 'Failed to generate summary. The text might be too complex or the AI service is temporarily unavailable.',
        model: 'fallback'
      };
    }
  },

  getLengthParameters(length, textLength) {
    const baseLength = Math.min(textLength / 4, 500); // Max 500 chars or 1/4 of original
    
    switch (length) {
      case 'short':
        return { minLength: 20, maxLength: Math.max(50, baseLength * 0.5) };
      case 'medium':
        return { minLength: 50, maxLength: Math.max(150, baseLength) };
      case 'long':
        return { minLength: 100, maxLength: Math.max(300, baseLength * 1.5) };
      default:
        return { minLength: 50, maxLength: Math.max(150, baseLength) };
    }
  },

  extractiveSummary(text, lengthParams) {
    // Simple extractive summarization - take first and most important sentences
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);
    
    if (sentences.length <= 3) {
      return sentences.join('. ') + '.';
    }
    
    // Take first sentence and a few from the middle
    const numSentences = Math.min(5, Math.max(2, Math.floor(lengthParams.maxLength / 50)));
    const selectedSentences = [
      sentences[0], // First sentence
      ...sentences.slice(Math.floor(sentences.length / 3), Math.floor(sentences.length / 3) + numSentences - 1)
    ];
    
    return selectedSentences.join('. ') + '.';
  },

  formatSummary(summary, style) {
    switch (style) {
      case 'bullets':
        const sentences = summary.split(/[.!?]+/).filter(s => s.trim().length > 10);
        return sentences.map(s => `• ${s.trim()}`).join('\n');
      
      case 'keypoints':
        const points = summary.split(/[.!?]+/).filter(s => s.trim().length > 10);
        return points.map((p, i) => `**${i + 1}.** ${p.trim()}`).join('\n');
      
      case 'abstract':
        return `**Abstract:** ${summary}`;
      
      case 'paragraph':
      default:
        return summary;
    }
  },

  getLengthName(length) {
    const names = {
      short: 'Short (1-2 sentences)',
      medium: 'Medium (3-5 sentences)',
      long: 'Long (6-10 sentences)'
    };
    return names[length] || 'Medium';
  },

  getStyleName(style) {
    const names = {
      bullets: 'Bullet Points',
      paragraph: 'Paragraph',
      keypoints: 'Key Points',
      abstract: 'Abstract'
    };
    return names[style] || 'Paragraph';
  }
};
