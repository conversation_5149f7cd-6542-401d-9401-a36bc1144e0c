/**
 * <PERSON>minder Model for NexusBot
 * Handles user reminders and scheduled notifications
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Reminder = sequelize.define('Reminder', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.STRING,
      allowNull: false,
      index: true
    },
    guildId: {
      type: DataTypes.STRING,
      allowNull: false,
      index: true
    },
    channelId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    messageId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        len: [1, 2000]
      }
    },
    reminderTime: {
      type: DataTypes.DATE,
      allowNull: false,
      index: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    isRecurring: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    recurringInterval: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isIn: [['daily', 'weekly', 'monthly', 'yearly']]
      }
    },
    isCompleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      index: true
    },
    completedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    metadata: {
      type: DataTypes.JSON,
      defaultValue: {}
    }
  }, {
    tableName: 'reminders',
    indexes: [
      {
        fields: ['userId', 'guildId']
      },
      {
        fields: ['reminderTime', 'isCompleted']
      },
      {
        fields: ['isCompleted', 'reminderTime']
      }
    ],
    hooks: {
      beforeCreate: (reminder) => {
        // Ensure reminder time is in the future
        if (reminder.reminderTime <= new Date()) {
          throw new Error('Reminder time must be in the future');
        }
      },
      beforeUpdate: (reminder) => {
        if (reminder.changed('isCompleted') && reminder.isCompleted) {
          reminder.completedAt = new Date();
        }
      }
    }
  });

  // Instance methods
  Reminder.prototype.isExpired = function() {
    return new Date() >= this.reminderTime && !this.isCompleted;
  };

  Reminder.prototype.getTimeUntilReminder = function() {
    return this.reminderTime.getTime() - Date.now();
  };

  Reminder.prototype.markCompleted = async function() {
    this.isCompleted = true;
    this.completedAt = new Date();
    return await this.save();
  };

  Reminder.prototype.calculateNextRecurrence = function() {
    if (!this.isRecurring || !this.recurringInterval) {
      return null;
    }

    const nextTime = new Date(this.reminderTime);
    
    switch (this.recurringInterval) {
      case 'daily':
        nextTime.setDate(nextTime.getDate() + 1);
        break;
      case 'weekly':
        nextTime.setDate(nextTime.getDate() + 7);
        break;
      case 'monthly':
        nextTime.setMonth(nextTime.getMonth() + 1);
        break;
      case 'yearly':
        nextTime.setFullYear(nextTime.getFullYear() + 1);
        break;
      default:
        return null;
    }

    return nextTime;
  };

  // Class methods
  Reminder.findExpiredReminders = async function() {
    return await this.findAll({
      where: {
        reminderTime: {
          [sequelize.Sequelize.Op.lte]: new Date()
        },
        isCompleted: false
      },
      order: [['reminderTime', 'ASC']]
    });
  };

  Reminder.findUserReminders = async function(userId, guildId, includeCompleted = false) {
    const where = { userId, guildId };
    if (!includeCompleted) {
      where.isCompleted = false;
    }

    return await this.findAll({
      where,
      order: [['reminderTime', 'ASC']]
    });
  };

  Reminder.countUserReminders = async function(userId, guildId) {
    return await this.count({
      where: {
        userId,
        guildId,
        isCompleted: false
      }
    });
  };

  Reminder.cleanupExpiredReminders = async function(daysOld = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const deleted = await this.destroy({
      where: {
        isCompleted: true,
        completedAt: {
          [sequelize.Sequelize.Op.lt]: cutoffDate
        }
      }
    });

    return deleted;
  };

  return Reminder;
};
