const { Sequelize } = require('sequelize');
const config = require('../config/config');
const logger = require('../utils/logger');
const path = require('path');
const fs = require('fs');
const { SupabaseUtils } = require('./supabase');

// Ensure data directory exists for SQLite
if (config.database.dialect === 'sqlite') {
  const dataDir = path.dirname(config.database.url.replace('sqlite:', ''));
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Create Sequelize instance
const sequelize = new Sequelize(config.database.url, {
  dialect: config.database.dialect,
  logging: config.database.logging,
  pool: config.database.pool,
  
  // SQLite specific options
  ...(config.database.dialect === 'sqlite' && {
    storage: config.database.url.replace('sqlite:', ''),
    dialectOptions: {
      timeout: 20000,
    }
  }),

  // PostgreSQL specific options
  ...(config.database.dialect === 'postgres' && {
    dialectOptions: {
      ssl: process.env.NODE_ENV === 'production' ? {
        require: true,
        rejectUnauthorized: false
      } : false
    }
  }),

  // Performance optimizations
  benchmark: config.development.debug,
  retry: {
    max: 3
  }
});

// Test database connection
async function testConnection() {
  try {
    // Try Supabase first
    if (config.supabase.url && config.supabase.serviceKey) {
      const supabaseHealthy = await SupabaseUtils.testConnection();
      if (supabaseHealthy) {
        logger.info('Supabase database connection established successfully');
        return true;
      }
    }

    // Fallback to Sequelize
    await sequelize.authenticate();
    logger.info(`Fallback database connection established successfully (${config.database.dialect})`);
    return true;
  } catch (error) {
    logger.error('Unable to connect to any database:', error);
    return false;
  }
}

// Initialize database with models
async function initializeDatabase() {
  try {
    // Try Supabase first
    if (config.supabase.url && config.supabase.serviceKey) {
      const supabaseInitialized = await SupabaseUtils.initializeTables();
      if (supabaseInitialized) {
        logger.info('Supabase database initialized successfully');
        return true;
      }
    }

    // Fallback to Sequelize
    require('./models');

    // Sync database (create tables if they don't exist)
    await sequelize.sync({
      alter: config.development.nodeEnv === 'development',
      force: false // Never force in production
    });

    logger.info('Fallback database synchronized successfully');
    return true;
  } catch (error) {
    logger.error('Error initializing database:', error);
    throw error;
  }
}

// Close database connection
async function closeConnection() {
  try {
    await sequelize.close();
    logger.info('Database connection closed');
  } catch (error) {
    logger.error('Error closing database connection:', error);
    throw error;
  }
}

// Export sequelize instance and utility functions
module.exports = {
  sequelize,
  testConnection,
  initializeDatabase,
  closeConnection,
  
  // Expose Sequelize for model definitions
  Sequelize,
  
  // Database utilities
  async query(sql, options = {}) {
    try {
      return await sequelize.query(sql, {
        type: Sequelize.QueryTypes.SELECT,
        ...options
      });
    } catch (error) {
      logger.error('Database query error:', error);
      throw error;
    }
  },

  async transaction(callback) {
    const t = await sequelize.transaction();
    try {
      const result = await callback(t);
      await t.commit();
      return result;
    } catch (error) {
      await t.rollback();
      throw error;
    }
  },

  // Health check
  async healthCheck() {
    try {
      await sequelize.authenticate();
      return {
        status: 'healthy',
        dialect: config.database.dialect,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
};
