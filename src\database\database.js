const config = require('../config/config');
const logger = require('../utils/logger');
const { SupabaseUtils } = require('./supabase');

// For now, we'll use Supabase as the primary database
// Sequelize fallback can be added later when needed

// Test database connection
async function testConnection() {
  try {
    // Try Supabase first
    if (config.supabase.url && config.supabase.serviceKey) {
      const supabaseHealthy = await SupabaseUtils.testConnection();
      if (supabaseHealthy) {
        logger.info('Supabase database connection established successfully');
        return true;
      }
    }

    logger.warn('Supabase not configured, bot will run with limited functionality');
    return false;
  } catch (error) {
    logger.error('Unable to connect to database:', error);
    return false;
  }
}

// Initialize database with models
async function initializeDatabase() {
  try {
    // Try Supabase first
    if (config.supabase.url && config.supabase.serviceKey) {
      const supabaseInitialized = await SupabaseUtils.initializeTables();
      if (supabaseInitialized) {
        logger.info('Supabase database initialized successfully');
        return true;
      }
    }

    logger.warn('Database not initialized, bot will run with limited functionality');
    return false;
  } catch (error) {
    logger.error('Error initializing database:', error);
    return false;
  }
}

// Close database connection
async function closeConnection() {
  try {
    logger.info('Database connection closed');
  } catch (error) {
    logger.error('Error closing database connection:', error);
    throw error;
  }
}

// Export utility functions
module.exports = {
  testConnection,
  initializeDatabase,
  closeConnection,

  // Health check
  async healthCheck() {
    try {
      if (config.supabase.url && config.supabase.serviceKey) {
        return await SupabaseUtils.healthCheck();
      }

      return {
        status: 'unhealthy',
        error: 'No database configured',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
};
