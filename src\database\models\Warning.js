const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Warning = sequelize.define('Warning', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    guildId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'guilds',
        key: 'id'
      }
    },
    moderatorId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    reason: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    tableName: 'warnings',
    timestamps: true,
    indexes: [
      {
        fields: ['userId', 'guildId']
      },
      {
        fields: ['active']
      },
      {
        fields: ['expiresAt']
      }
    ]
  });

  Warning.associate = (models) => {
    Warning.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    Warning.belongsTo(models.Guild, {
      foreignKey: 'guildId',
      as: 'guild'
    });
  };

  return Warning;
};
