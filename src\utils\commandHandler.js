const fs = require('fs').promises;
const path = require('path');
const { Collection, PermissionFlagsBits } = require('discord.js');
const { REST } = require('@discordjs/rest');
const { Routes } = require('discord-api-types/v10');
const config = require('../config/config');
const logger = require('./logger');
const rateLimiter = require('./rateLimiter');
const cacheManager = require('./cacheManager');

class CommandHandler {
  constructor() {
    this.commands = new Collection();
    this.slashCommands = new Collection();
    this.cooldowns = new Collection();
    this.customCommands = new Collection();
    this.commandStats = new Collection();
    this.commandCategories = new Set();
    this.disabledCommands = new Set();
    this.ownerOnlyCommands = new Set();
  }

  // Load all commands from the commands directory
  async loadCommands(client) {
    try {
      const commandsPath = path.join(__dirname, '../commands');
      const commandFolders = await fs.readdir(commandsPath);

      const slashCommandsData = [];

      for (const folder of commandFolders) {
        const folderPath = path.join(commandsPath, folder);
        const stat = await fs.stat(folderPath);
        
        if (!stat.isDirectory()) continue;

        const commandFiles = (await fs.readdir(folderPath))
          .filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
          const filePath = path.join(folderPath, file);
          
          try {
            // Clear require cache for hot reloading in development
            delete require.cache[require.resolve(filePath)];
            
            const command = require(filePath);
            
            // Validate command structure
            if (!this.validateCommand(command)) {
              logger.warn(`Invalid command structure in ${filePath}`);
              continue;
            }

            // Set command category
            command.category = folder;

            // Register prefix command
            if (command.name) {
              client.commands.set(command.name, command);
              this.commands.set(command.name, command);
              
              // Register aliases
              if (command.aliases) {
                command.aliases.forEach(alias => {
                  client.commands.set(alias, command);
                  this.commands.set(alias, command);
                });
              }
            }

            // Register slash command
            if (command.data) {
              client.slashCommands.set(command.data.name, command);
              this.slashCommands.set(command.data.name, command);
              slashCommandsData.push(command.data.toJSON());
            }

            logger.debug(`Loaded command: ${command.name || command.data?.name} from ${folder}`);
          } catch (error) {
            logger.error(`Error loading command ${file}:`, error);
          }
        }
      }

      // Register slash commands with Discord
      if (slashCommandsData.length > 0) {
        await this.registerSlashCommands(slashCommandsData);
      }

      logger.info(`Loaded ${this.commands.size} prefix commands and ${this.slashCommands.size} slash commands`);
    } catch (error) {
      logger.error('Error loading commands:', error);
      throw error;
    }
  }

  // Register slash commands with Discord API
  async registerSlashCommands(commandsData) {
    try {
      const rest = new REST({ version: '10' }).setToken(config.discord.token);

      if (config.development.nodeEnv === 'development' && config.discord.guildId) {
        // Register commands for a specific guild in development
        await rest.put(
          Routes.applicationGuildCommands(config.discord.clientId, config.discord.guildId),
          { body: commandsData }
        );
        logger.info(`Registered ${commandsData.length} guild slash commands for development`);
      } else {
        // Register commands globally in production
        await rest.put(
          Routes.applicationCommands(config.discord.clientId),
          { body: commandsData }
        );
        logger.info(`Registered ${commandsData.length} global slash commands`);
      }
    } catch (error) {
      logger.error('Error registering slash commands:', error);
      throw error;
    }
  }

  // Validate command structure
  validateCommand(command) {
    // Check for either prefix command or slash command structure
    const hasPrefix = command.name && typeof command.execute === 'function';
    const hasSlash = command.data && typeof command.execute === 'function';
    
    return hasPrefix || hasSlash;
  }

  // Handle prefix commands with enhanced features
  async handlePrefixCommand(message, client) {
    try {
      // Get server-specific prefix or use default
      const serverPrefix = await this.getServerPrefix(message.guild?.id);
      const prefix = serverPrefix || config.bot.defaultPrefix;

      // Check if message starts with prefix
      if (!message.content.startsWith(prefix) || message.author.bot) return;

      // Parse command and arguments
      const args = message.content.slice(prefix.length).trim().split(/ +/);
      const commandName = args.shift().toLowerCase();

      // Check for custom commands first
      const customCommand = await this.getCustomCommand(message.guild?.id, commandName);
      if (customCommand) {
        return await this.executeCustomCommand(message, customCommand, args);
      }

      // Get built-in command
      const command = client.commands.get(commandName);
      if (!command) return;

      // Check if command is disabled
      if (this.isCommandDisabled(message.guild?.id, commandName)) {
        return message.reply('This command is currently disabled.');
      }

      // Check rate limiting
      const rateLimitResult = await rateLimiter.checkLimit('command', message.author.id);
      if (!rateLimitResult.allowed) {
        return message.reply(`You're being rate limited. Try again in ${rateLimitResult.retryAfter} seconds.`);
      }

      // Check cooldown
      if (this.isOnCooldown(message.author.id, commandName, command.cooldown)) {
        const timeLeft = this.getCooldownTimeLeft(message.author.id, commandName);
        return message.reply(`Please wait ${timeLeft.toFixed(1)} more seconds before using \`${commandName}\` again.`);
      }

      // Check permissions
      const permissionCheck = await this.checkPermissions(message, command);
      if (!permissionCheck.allowed) {
        return message.reply(permissionCheck.message || 'You don\'t have permission to use this command.');
      }

      // Check if user is owner for owner-only commands
      if (this.ownerOnlyCommands.has(commandName) && message.author.id !== config.discord.ownerId) {
        return message.reply('This command is restricted to the bot owner.');
      }

      // Execute command
      await command.execute(message, args, client);

      // Set cooldown
      this.setCooldown(message.author.id, commandName, command.cooldown);

      // Update command statistics
      this.updateCommandStats(commandName, message.author.id, message.guild?.id);

      // Log command usage
      logger.info(`Command executed: ${commandName} by ${message.author.tag} in ${message.guild?.name || 'DM'}`);

    } catch (error) {
      logger.error('Error handling prefix command:', error);

      // Enhanced error handling
      const errorEmbed = {
        color: 0xFF0000,
        title: '❌ Command Error',
        description: 'An error occurred while executing this command.',
        timestamp: new Date().toISOString()
      };

      if (config.development.debug) {
        errorEmbed.fields = [{
          name: 'Error Details',
          value: `\`\`\`${error.message}\`\`\``
        }];
      }

      message.reply({ embeds: [errorEmbed] });
    }
  }

  // Handle slash commands
  async handleSlashCommand(interaction, client) {
    try {
      const command = client.slashCommands.get(interaction.commandName);
      if (!command) return;

      // Check cooldown
      if (this.isOnCooldown(interaction.user.id, interaction.commandName, command.cooldown)) {
        const timeLeft = this.getCooldownTimeLeft(interaction.user.id, interaction.commandName);
        return interaction.reply({
          content: `Please wait ${timeLeft.toFixed(1)} more seconds before using this command again.`,
          ephemeral: true
        });
      }

      // Check permissions
      if (!this.hasPermissions(interaction, command)) {
        return interaction.reply({
          content: 'You don\'t have permission to use this command.',
          ephemeral: true
        });
      }

      // Execute command
      await command.execute(interaction, client);

      // Set cooldown
      this.setCooldown(interaction.user.id, interaction.commandName, command.cooldown);

      // Log command usage
      logger.command(interaction.commandName, interaction.user.id, interaction.guild?.id);

    } catch (error) {
      logger.error('Error handling slash command:', error);
      
      const errorMessage = 'There was an error executing this command.';
      if (interaction.replied || interaction.deferred) {
        interaction.followUp({ content: errorMessage, ephemeral: true });
      } else {
        interaction.reply({ content: errorMessage, ephemeral: true });
      }
    }
  }

  // Cooldown management
  isOnCooldown(userId, commandName, cooldownTime = 3) {
    if (!this.cooldowns.has(commandName)) {
      this.cooldowns.set(commandName, new Collection());
    }

    const now = Date.now();
    const timestamps = this.cooldowns.get(commandName);
    const cooldownAmount = cooldownTime * 1000;

    if (timestamps.has(userId)) {
      const expirationTime = timestamps.get(userId) + cooldownAmount;
      return now < expirationTime;
    }

    return false;
  }

  getCooldownTimeLeft(userId, commandName) {
    const now = Date.now();
    const timestamps = this.cooldowns.get(commandName);
    const cooldownAmount = (this.commands.get(commandName)?.cooldown || 3) * 1000;
    const expirationTime = timestamps.get(userId) + cooldownAmount;
    
    return (expirationTime - now) / 1000;
  }

  setCooldown(userId, commandName, cooldownTime = 3) {
    if (!this.cooldowns.has(commandName)) {
      this.cooldowns.set(commandName, new Collection());
    }

    const timestamps = this.cooldowns.get(commandName);
    timestamps.set(userId, Date.now());

    // Clean up expired cooldowns
    setTimeout(() => timestamps.delete(userId), cooldownTime * 1000);
  }

  // Permission checking
  hasPermissions(messageOrInteraction, command) {
    if (!command.permissions || command.permissions.length === 0) return true;

    const member = messageOrInteraction.member;
    if (!member) return false;

    // Check if user has required permissions
    return command.permissions.every(permission => 
      member.permissions.has(permission)
    );
  }

  // Enhanced permission checking
  async checkPermissions(messageOrInteraction, command) {
    if (!command.permissions || command.permissions.length === 0) {
      return { allowed: true };
    }

    const member = messageOrInteraction.member;
    if (!member) {
      return { allowed: false, message: 'This command can only be used in a server.' };
    }

    // Check if user has required permissions
    const missingPermissions = command.permissions.filter(permission =>
      !member.permissions.has(permission)
    );

    if (missingPermissions.length > 0) {
      return {
        allowed: false,
        message: `You're missing the following permissions: ${missingPermissions.join(', ')}`
      };
    }

    // Check role-based permissions if specified
    if (command.requiredRoles && command.requiredRoles.length > 0) {
      const hasRequiredRole = command.requiredRoles.some(roleId =>
        member.roles.cache.has(roleId)
      );

      if (!hasRequiredRole) {
        return {
          allowed: false,
          message: 'You don\'t have the required role to use this command.'
        };
      }
    }

    return { allowed: true };
  }

  // Get server-specific prefix
  async getServerPrefix(guildId) {
    if (!guildId) return config.bot.defaultPrefix;

    try {
      const cached = await cacheManager.get(`prefix:${guildId}`);
      if (cached) return cached;

      const dbManager = require('../database');
      const guild = await dbManager.getOrCreateGuild(guildId);
      const prefix = guild.settings?.prefix || config.bot.defaultPrefix;

      await cacheManager.set(`prefix:${guildId}`, prefix, 3600); // Cache for 1 hour
      return prefix;
    } catch (error) {
      logger.error('Error getting server prefix:', error);
      return config.bot.defaultPrefix;
    }
  }

  // Custom command handling
  async getCustomCommand(guildId, commandName) {
    if (!guildId) return null;

    try {
      const cached = await cacheManager.get(`custom_cmd:${guildId}:${commandName}`);
      if (cached) return cached;

      const dbManager = require('../database');
      const customCommand = await dbManager.getCustomCommands(guildId);
      const command = customCommand.find(cmd =>
        cmd.name === commandName || cmd.aliases.includes(commandName)
      );

      if (command) {
        await cacheManager.set(`custom_cmd:${guildId}:${commandName}`, command, 1800); // Cache for 30 minutes
      }

      return command;
    } catch (error) {
      logger.error('Error getting custom command:', error);
      return null;
    }
  }

  async executeCustomCommand(message, customCommand, args) {
    try {
      // Check if user can use this command
      if (!customCommand.canUserUse(message.member)) {
        return message.reply('You don\'t have permission to use this custom command.');
      }

      // Check if command can be used in this channel
      if (!customCommand.canUseInChannel(message.channel.id)) {
        return message.reply('This custom command cannot be used in this channel.');
      }

      // Check cooldown
      if (this.isOnCooldown(message.author.id, `custom_${customCommand.name}`, customCommand.cooldown)) {
        const timeLeft = this.getCooldownTimeLeft(message.author.id, `custom_${customCommand.name}`);
        return message.reply(`Please wait ${timeLeft.toFixed(1)} more seconds before using this command again.`);
      }

      // Process command content
      const processedContent = customCommand.processContent({
        user: message.author,
        guild: message.guild,
        channel: message.channel,
        args: args
      });

      // Send response
      if (customCommand.embedConfig) {
        const embed = {
          ...customCommand.embedConfig,
          description: processedContent
        };
        await message.reply({ embeds: [embed] });
      } else {
        await message.reply(processedContent);
      }

      // Set cooldown
      this.setCooldown(message.author.id, `custom_${customCommand.name}`, customCommand.cooldown);

      // Update usage statistics
      await customCommand.incrementUsage();

    } catch (error) {
      logger.error('Error executing custom command:', error);
      message.reply('There was an error executing this custom command.');
    }
  }

  // Command management
  isCommandDisabled(guildId, commandName) {
    return this.disabledCommands.has(`${guildId}:${commandName}`);
  }

  async disableCommand(guildId, commandName) {
    this.disabledCommands.add(`${guildId}:${commandName}`);
    await cacheManager.set(`disabled_cmd:${guildId}:${commandName}`, true, 86400);
  }

  async enableCommand(guildId, commandName) {
    this.disabledCommands.delete(`${guildId}:${commandName}`);
    await cacheManager.delete(`disabled_cmd:${guildId}:${commandName}`);
  }

  // Command statistics
  updateCommandStats(commandName, userId, guildId) {
    const key = `${commandName}:${guildId || 'dm'}`;
    const stats = this.commandStats.get(key) || { count: 0, users: new Set(), lastUsed: null };

    stats.count++;
    stats.users.add(userId);
    stats.lastUsed = new Date();

    this.commandStats.set(key, stats);
  }

  getCommandStats(commandName, guildId) {
    const key = `${commandName}:${guildId || 'dm'}`;
    const stats = this.commandStats.get(key);

    if (!stats) return null;

    return {
      count: stats.count,
      uniqueUsers: stats.users.size,
      lastUsed: stats.lastUsed
    };
  }

  // Get all commands with their categories
  getAllCommands() {
    const commandsByCategory = {};

    for (const [name, command] of this.commands) {
      const category = command.category || 'uncategorized';
      if (!commandsByCategory[category]) {
        commandsByCategory[category] = [];
      }

      commandsByCategory[category].push({
        name: command.name,
        description: command.description,
        usage: command.usage,
        aliases: command.aliases || [],
        permissions: command.permissions || [],
        cooldown: command.cooldown || 3
      });
    }

    return commandsByCategory;
  }

  // Reload a specific command
  async reloadCommand(commandPath) {
    try {
      delete require.cache[require.resolve(commandPath)];
      const command = require(commandPath);

      if (this.validateCommand(command)) {
        this.commands.set(command.name, command);
        if (command.data) {
          this.slashCommands.set(command.data.name, command);
        }
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Error reloading command:', error);
      return false;
    }
  }
}

module.exports = new CommandHandler();
