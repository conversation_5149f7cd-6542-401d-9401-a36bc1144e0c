const fs = require('fs').promises;
const path = require('path');
const { Collection } = require('discord.js');
const { REST } = require('@discordjs/rest');
const { Routes } = require('discord-api-types/v10');
const config = require('../config/config');
const logger = require('./logger');

class CommandHandler {
  constructor() {
    this.commands = new Collection();
    this.slashCommands = new Collection();
    this.cooldowns = new Collection();
  }

  // Load all commands from the commands directory
  async loadCommands(client) {
    try {
      const commandsPath = path.join(__dirname, '../commands');
      const commandFolders = await fs.readdir(commandsPath);

      const slashCommandsData = [];

      for (const folder of commandFolders) {
        const folderPath = path.join(commandsPath, folder);
        const stat = await fs.stat(folderPath);
        
        if (!stat.isDirectory()) continue;

        const commandFiles = (await fs.readdir(folderPath))
          .filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
          const filePath = path.join(folderPath, file);
          
          try {
            // Clear require cache for hot reloading in development
            delete require.cache[require.resolve(filePath)];
            
            const command = require(filePath);
            
            // Validate command structure
            if (!this.validateCommand(command)) {
              logger.warn(`Invalid command structure in ${filePath}`);
              continue;
            }

            // Set command category
            command.category = folder;

            // Register prefix command
            if (command.name) {
              client.commands.set(command.name, command);
              this.commands.set(command.name, command);
              
              // Register aliases
              if (command.aliases) {
                command.aliases.forEach(alias => {
                  client.commands.set(alias, command);
                  this.commands.set(alias, command);
                });
              }
            }

            // Register slash command
            if (command.data) {
              client.slashCommands.set(command.data.name, command);
              this.slashCommands.set(command.data.name, command);
              slashCommandsData.push(command.data.toJSON());
            }

            logger.debug(`Loaded command: ${command.name || command.data?.name} from ${folder}`);
          } catch (error) {
            logger.error(`Error loading command ${file}:`, error);
          }
        }
      }

      // Register slash commands with Discord
      if (slashCommandsData.length > 0) {
        await this.registerSlashCommands(slashCommandsData);
      }

      logger.info(`Loaded ${this.commands.size} prefix commands and ${this.slashCommands.size} slash commands`);
    } catch (error) {
      logger.error('Error loading commands:', error);
      throw error;
    }
  }

  // Register slash commands with Discord API
  async registerSlashCommands(commandsData) {
    try {
      const rest = new REST({ version: '10' }).setToken(config.discord.token);

      if (config.development.nodeEnv === 'development' && config.discord.guildId) {
        // Register commands for a specific guild in development
        await rest.put(
          Routes.applicationGuildCommands(config.discord.clientId, config.discord.guildId),
          { body: commandsData }
        );
        logger.info(`Registered ${commandsData.length} guild slash commands for development`);
      } else {
        // Register commands globally in production
        await rest.put(
          Routes.applicationCommands(config.discord.clientId),
          { body: commandsData }
        );
        logger.info(`Registered ${commandsData.length} global slash commands`);
      }
    } catch (error) {
      logger.error('Error registering slash commands:', error);
      throw error;
    }
  }

  // Validate command structure
  validateCommand(command) {
    // Check for either prefix command or slash command structure
    const hasPrefix = command.name && typeof command.execute === 'function';
    const hasSlash = command.data && typeof command.execute === 'function';
    
    return hasPrefix || hasSlash;
  }

  // Handle prefix commands
  async handlePrefixCommand(message, client) {
    try {
      // Get server-specific prefix or use default
      const serverPrefix = await this.getServerPrefix(message.guild?.id);
      const prefix = serverPrefix || config.bot.defaultPrefix;

      // Check if message starts with prefix
      if (!message.content.startsWith(prefix) || message.author.bot) return;

      // Parse command and arguments
      const args = message.content.slice(prefix.length).trim().split(/ +/);
      const commandName = args.shift().toLowerCase();

      // Get command
      const command = client.commands.get(commandName);
      if (!command) return;

      // Check cooldown
      if (this.isOnCooldown(message.author.id, commandName, command.cooldown)) {
        const timeLeft = this.getCooldownTimeLeft(message.author.id, commandName);
        return message.reply(`Please wait ${timeLeft.toFixed(1)} more seconds before using \`${commandName}\` again.`);
      }

      // Check permissions
      if (!this.hasPermissions(message, command)) {
        return message.reply('You don\'t have permission to use this command.');
      }

      // Execute command
      await command.execute(message, args, client);

      // Set cooldown
      this.setCooldown(message.author.id, commandName, command.cooldown);

      // Log command usage
      logger.command(commandName, message.author.id, message.guild?.id, args);

    } catch (error) {
      logger.error('Error handling prefix command:', error);
      message.reply('There was an error executing this command.');
    }
  }

  // Handle slash commands
  async handleSlashCommand(interaction, client) {
    try {
      const command = client.slashCommands.get(interaction.commandName);
      if (!command) return;

      // Check cooldown
      if (this.isOnCooldown(interaction.user.id, interaction.commandName, command.cooldown)) {
        const timeLeft = this.getCooldownTimeLeft(interaction.user.id, interaction.commandName);
        return interaction.reply({
          content: `Please wait ${timeLeft.toFixed(1)} more seconds before using this command again.`,
          ephemeral: true
        });
      }

      // Check permissions
      if (!this.hasPermissions(interaction, command)) {
        return interaction.reply({
          content: 'You don\'t have permission to use this command.',
          ephemeral: true
        });
      }

      // Execute command
      await command.execute(interaction, client);

      // Set cooldown
      this.setCooldown(interaction.user.id, interaction.commandName, command.cooldown);

      // Log command usage
      logger.command(interaction.commandName, interaction.user.id, interaction.guild?.id);

    } catch (error) {
      logger.error('Error handling slash command:', error);
      
      const errorMessage = 'There was an error executing this command.';
      if (interaction.replied || interaction.deferred) {
        interaction.followUp({ content: errorMessage, ephemeral: true });
      } else {
        interaction.reply({ content: errorMessage, ephemeral: true });
      }
    }
  }

  // Cooldown management
  isOnCooldown(userId, commandName, cooldownTime = 3) {
    if (!this.cooldowns.has(commandName)) {
      this.cooldowns.set(commandName, new Collection());
    }

    const now = Date.now();
    const timestamps = this.cooldowns.get(commandName);
    const cooldownAmount = cooldownTime * 1000;

    if (timestamps.has(userId)) {
      const expirationTime = timestamps.get(userId) + cooldownAmount;
      return now < expirationTime;
    }

    return false;
  }

  getCooldownTimeLeft(userId, commandName) {
    const now = Date.now();
    const timestamps = this.cooldowns.get(commandName);
    const cooldownAmount = (this.commands.get(commandName)?.cooldown || 3) * 1000;
    const expirationTime = timestamps.get(userId) + cooldownAmount;
    
    return (expirationTime - now) / 1000;
  }

  setCooldown(userId, commandName, cooldownTime = 3) {
    if (!this.cooldowns.has(commandName)) {
      this.cooldowns.set(commandName, new Collection());
    }

    const timestamps = this.cooldowns.get(commandName);
    timestamps.set(userId, Date.now());

    // Clean up expired cooldowns
    setTimeout(() => timestamps.delete(userId), cooldownTime * 1000);
  }

  // Permission checking
  hasPermissions(messageOrInteraction, command) {
    if (!command.permissions || command.permissions.length === 0) return true;

    const member = messageOrInteraction.member;
    if (!member) return false;

    // Check if user has required permissions
    return command.permissions.every(permission => 
      member.permissions.has(permission)
    );
  }

  // Get server-specific prefix (placeholder for database integration)
  async getServerPrefix(guildId) {
    // TODO: Implement database lookup for server-specific prefix
    return config.bot.defaultPrefix;
  }
}

module.exports = new CommandHandler();
