# NexusBot Dashboard

A modern, responsive dashboard for NexusBot built with vanilla HTML, CSS, and JavaScript. Designed to showcase the bot's features, provide comprehensive documentation, and guide users through the setup process.

## 🌟 Features

### 📱 **Responsive Design**
- Mobile-first approach with responsive layouts
- Optimized for all screen sizes and devices
- Touch-friendly navigation and interactions

### 🎨 **Modern UI/UX**
- Clean, professional design with NexusBot's color palette
- Smooth animations and transitions
- Interactive elements and hover effects
- Dark theme optimized for Discord users

### 📚 **Comprehensive Documentation**
- **Homepage**: Bot overview, features showcase, and statistics
- **Commands Page**: Interactive command browser with search and filtering
- **Setup Guide**: Step-by-step installation and configuration
- **Responsive Navigation**: Easy access to all sections

### ⚡ **Performance Optimized**
- Vanilla JavaScript (no heavy frameworks)
- Optimized images and assets
- Fast loading times
- SEO-friendly structure

## 🎨 Design System

### **Color Palette**
- **Primary**: Electric Blue (#00D4FF)
- **Secondary**: <PERSON> Purple (#6B46C1)
- **Accent**: Neo<PERSON> (#00FF88)
- **Background**: <PERSON> (#1A1A1A)
- **Surface**: <PERSON> (#2A2A2A)

### **Typography**
- **Font Family**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700
- **Responsive scaling** for optimal readability

### **Components**
- **Buttons**: Primary, secondary, and large variants
- **Cards**: Feature cards, command cards, step cards
- **Navigation**: Fixed navbar with smooth scrolling
- **Modals**: Command details and information overlays

## 📁 File Structure

```
dashboard/
├── index.html              # Homepage
├── commands.html           # Commands documentation
├── setup.html             # Setup guide
├── assets/
│   ├── css/
│   │   ├── style.css       # Main stylesheet
│   │   ├── commands.css    # Commands page styles
│   │   └── setup.css       # Setup page styles
│   ├── js/
│   │   ├── main.js         # Core functionality
│   │   ├── commands.js     # Commands page logic
│   │   └── setup.js        # Setup page logic
│   └── images/             # Images and assets
└── README.md              # This file
```

## 🚀 Deployment

### **GitHub Pages Setup**

1. **Upload Files**:
   ```bash
   # Copy all dashboard files to your GitHub repository root
   cp -r dashboard/* /path/to/your/repo/
   ```

2. **Enable GitHub Pages**:
   - Go to your repository settings
   - Scroll to "Pages" section
   - Select "Deploy from a branch"
   - Choose "main" branch and "/ (root)"
   - Save settings

3. **Access Your Dashboard**:
   - Your dashboard will be available at: `https://yourusername.github.io/`
   - Updates are automatically deployed when you push changes

### **Custom Domain (Optional)**

1. **Add CNAME file**:
   ```bash
   echo "yourdomain.com" > CNAME
   ```

2. **Configure DNS**:
   - Add CNAME record pointing to `yourusername.github.io`
   - Wait for DNS propagation (up to 24 hours)

## 🛠️ Customization

### **Bot Information**
Update the following in the files:

1. **Bot Invite URL** (in all JavaScript files):
   ```javascript
   const inviteUrl = 'https://discord.com/api/oauth2/authorize?client_id=YOUR_BOT_ID&permissions=8&scope=bot%20applications.commands';
   ```

2. **Bot Statistics** (in `main.js`):
   ```javascript
   // Update these values in the hero stats section
   { name: 'Commands', count: 50 },
   { name: 'Servers', count: 1000 },
   { name: 'Users', count: 10000 }
   ```

3. **Support Links** (in all HTML files):
   ```html
   <!-- Update these URLs in the footer -->
   <a href="YOUR_DISCORD_SERVER">Discord Server</a>
   <a href="YOUR_GITHUB_REPO">GitHub</a>
   ```

### **Commands Data**
Update command information in `main.js`:

```javascript
window.commandsData = {
  // Add, remove, or modify commands here
  moderation: [
    {
      name: 'kick',
      description: 'Kick a member from the server',
      usage: 'kick <user> [reason]',
      // ... other properties
    }
  ]
  // ... other categories
};
```

### **Styling**
Modify CSS variables in `style.css`:

```css
:root {
  --primary-color: #00D4FF;      /* Your primary color */
  --secondary-color: #6B46C1;    /* Your secondary color */
  --accent-color: #00FF88;       /* Your accent color */
  /* ... other variables */
}
```

## 📱 Features Breakdown

### **Homepage (`index.html`)**
- Hero section with animated statistics
- Features showcase with interactive cards
- Command categories preview
- Responsive navigation
- Call-to-action buttons

### **Commands Page (`commands.html`)**
- Searchable command database
- Category filtering
- Detailed command modals
- Copy-to-clipboard functionality
- Keyboard shortcuts (Ctrl+K for search)

### **Setup Guide (`setup.html`)**
- Interactive progress indicator
- Step-by-step instructions
- Code examples with copy functionality
- Responsive design for mobile users
- Completion tracking

## 🔧 Technical Details

### **JavaScript Features**
- **Vanilla JS**: No external dependencies
- **ES6+**: Modern JavaScript features
- **Modular**: Organized into separate files
- **Performance**: Debounced scroll events
- **Accessibility**: Keyboard navigation support

### **CSS Features**
- **CSS Grid & Flexbox**: Modern layout techniques
- **CSS Variables**: Easy theming and customization
- **Animations**: Smooth transitions and hover effects
- **Responsive**: Mobile-first design approach
- **Cross-browser**: Compatible with modern browsers

### **SEO Optimization**
- **Meta Tags**: Proper Open Graph and Twitter cards
- **Semantic HTML**: Proper heading hierarchy
- **Alt Text**: Descriptive image alternatives
- **Structured Data**: Schema markup for search engines

## 🎯 Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+
- **Mobile**: iOS Safari 14+, Chrome Mobile 90+

## 📈 Performance

- **Lighthouse Score**: 95+ (Performance, Accessibility, Best Practices, SEO)
- **Load Time**: < 2 seconds on 3G
- **Bundle Size**: < 500KB total
- **Images**: Optimized and compressed

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test on multiple devices/browsers
5. Submit a pull request

## 📄 License

This dashboard is part of the NexusBot project and follows the same MIT license.

---

**Made with ❤️ for the NexusBot community**
