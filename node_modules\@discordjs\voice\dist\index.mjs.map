{"version": 3, "sources": ["../src/VoiceConnection.ts", "../src/DataStore.ts", "../src/networking/Networking.ts", "../src/util/Secretbox.ts", "../src/util/util.ts", "../src/networking/VoiceUDPSocket.ts", "../src/networking/VoiceWebSocket.ts", "../src/receive/VoiceReceiver.ts", "../src/receive/AudioReceiveStream.ts", "../src/audio/AudioPlayer.ts", "../src/audio/AudioPlayerError.ts", "../src/audio/PlayerSubscription.ts", "../src/receive/SSRCMap.ts", "../src/receive/SpeakingMap.ts", "../src/joinVoiceChannel.ts", "../src/audio/AudioResource.ts", "../src/audio/TransformerGraph.ts", "../src/util/generateDependencyReport.ts", "../src/util/entersState.ts", "../src/util/abortAfter.ts", "../src/util/demuxProbe.ts", "../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/unbound-method */\nimport type { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport { EventEmitter } from 'node:events';\nimport type { GatewayVoiceServerUpdateDispatchData, GatewayVoiceStateUpdateDispatchData } from 'discord-api-types/v10';\nimport type { JoinConfig } from './DataStore';\nimport {\n\tgetVoiceConnection,\n\tcreateJoinVoiceChannelPayload,\n\ttrackVoiceConnection,\n\tuntrackVoiceConnection,\n} from './DataStore';\nimport type { AudioPlayer } from './audio/AudioPlayer';\nimport type { PlayerSubscription } from './audio/PlayerSubscription';\nimport type { VoiceWebSocket, VoiceUDPSocket } from './networking';\nimport { Networking, NetworkingStatusCode, type NetworkingState } from './networking/Networking';\nimport { VoiceReceiver } from './receive/index';\nimport type { DiscordGatewayAdapterImplementerMethods } from './util/adapter';\nimport { noop } from './util/util';\nimport type { CreateVoiceConnectionOptions } from './index';\n\n/**\n * The various status codes a voice connection can hold at any one time.\n */\nexport enum VoiceConnectionStatus {\n\t/**\n\t * The `VOICE_SERVER_UPDATE` and `VOICE_STATE_UPDATE` packets have been received, now attempting to establish a voice connection.\n\t */\n\tConnecting = 'connecting',\n\n\t/**\n\t * The voice connection has been destroyed and untracked, it cannot be reused.\n\t */\n\tDestroyed = 'destroyed',\n\n\t/**\n\t * The voice connection has either been severed or not established.\n\t */\n\tDisconnected = 'disconnected',\n\n\t/**\n\t * A voice connection has been established, and is ready to be used.\n\t */\n\tReady = 'ready',\n\n\t/**\n\t * Sending a packet to the main Discord gateway to indicate we want to change our voice state.\n\t */\n\tSignalling = 'signalling',\n}\n\n/**\n * The state that a VoiceConnection will be in when it is waiting to receive a VOICE_SERVER_UPDATE and\n * VOICE_STATE_UPDATE packet from Discord, provided by the adapter.\n */\nexport interface VoiceConnectionSignallingState {\n\tadapter: DiscordGatewayAdapterImplementerMethods;\n\tstatus: VoiceConnectionStatus.Signalling;\n\tsubscription?: PlayerSubscription | undefined;\n}\n\n/**\n * The reasons a voice connection can be in the disconnected state.\n */\nexport enum VoiceConnectionDisconnectReason {\n\t/**\n\t * When the WebSocket connection has been closed.\n\t */\n\tWebSocketClose,\n\n\t/**\n\t * When the adapter was unable to send a message requested by the VoiceConnection.\n\t */\n\tAdapterUnavailable,\n\n\t/**\n\t * When a VOICE_SERVER_UPDATE packet is received with a null endpoint, causing the connection to be severed.\n\t */\n\tEndpointRemoved,\n\n\t/**\n\t * When a manual disconnect was requested.\n\t */\n\tManual,\n}\n\n/**\n * The state that a VoiceConnection will be in when it is not connected to a Discord voice server nor is\n * it attempting to connect. You can manually attempt to reconnect using VoiceConnection#reconnect.\n */\nexport interface VoiceConnectionDisconnectedBaseState {\n\tadapter: DiscordGatewayAdapterImplementerMethods;\n\tstatus: VoiceConnectionStatus.Disconnected;\n\tsubscription?: PlayerSubscription | undefined;\n}\n\n/**\n * The state that a VoiceConnection will be in when it is not connected to a Discord voice server nor is\n * it attempting to connect. You can manually attempt to reconnect using VoiceConnection#reconnect.\n */\nexport interface VoiceConnectionDisconnectedOtherState extends VoiceConnectionDisconnectedBaseState {\n\treason: Exclude<VoiceConnectionDisconnectReason, VoiceConnectionDisconnectReason.WebSocketClose>;\n}\n\n/**\n * The state that a VoiceConnection will be in when its WebSocket connection was closed.\n * You can manually attempt to reconnect using VoiceConnection#reconnect.\n */\nexport interface VoiceConnectionDisconnectedWebSocketState extends VoiceConnectionDisconnectedBaseState {\n\t/**\n\t * The close code of the WebSocket connection to the Discord voice server.\n\t */\n\tcloseCode: number;\n\n\treason: VoiceConnectionDisconnectReason.WebSocketClose;\n}\n\n/**\n * The states that a VoiceConnection can be in when it is not connected to a Discord voice server nor is\n * it attempting to connect. You can manually attempt to connect using VoiceConnection#reconnect.\n */\nexport type VoiceConnectionDisconnectedState =\n\t| VoiceConnectionDisconnectedOtherState\n\t| VoiceConnectionDisconnectedWebSocketState;\n\n/**\n * The state that a VoiceConnection will be in when it is establishing a connection to a Discord\n * voice server.\n */\nexport interface VoiceConnectionConnectingState {\n\tadapter: DiscordGatewayAdapterImplementerMethods;\n\tnetworking: Networking;\n\tstatus: VoiceConnectionStatus.Connecting;\n\tsubscription?: PlayerSubscription | undefined;\n}\n\n/**\n * The state that a VoiceConnection will be in when it has an active connection to a Discord\n * voice server.\n */\nexport interface VoiceConnectionReadyState {\n\tadapter: DiscordGatewayAdapterImplementerMethods;\n\tnetworking: Networking;\n\tstatus: VoiceConnectionStatus.Ready;\n\tsubscription?: PlayerSubscription | undefined;\n}\n\n/**\n * The state that a VoiceConnection will be in when it has been permanently been destroyed by the\n * user and untracked by the library. It cannot be reconnected, instead, a new VoiceConnection\n * needs to be established.\n */\nexport interface VoiceConnectionDestroyedState {\n\tstatus: VoiceConnectionStatus.Destroyed;\n}\n\n/**\n * The various states that a voice connection can be in.\n */\nexport type VoiceConnectionState =\n\t| VoiceConnectionConnectingState\n\t| VoiceConnectionDestroyedState\n\t| VoiceConnectionDisconnectedState\n\t| VoiceConnectionReadyState\n\t| VoiceConnectionSignallingState;\n\nexport interface VoiceConnection extends EventEmitter {\n\t/**\n\t * Emitted when there is an error emitted from the voice connection\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'error', listener: (error: Error) => void): this;\n\t/**\n\t * Emitted debugging information about the voice connection\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'debug', listener: (message: string) => void): this;\n\t/**\n\t * Emitted when the state of the voice connection changes\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'stateChange', listener: (oldState: VoiceConnectionState, newState: VoiceConnectionState) => void): this;\n\t/**\n\t * Emitted when the state of the voice connection changes to a specific status\n\t *\n\t * @eventProperty\n\t */\n\ton<Event extends VoiceConnectionStatus>(\n\t\tevent: Event,\n\t\tlistener: (oldState: VoiceConnectionState, newState: VoiceConnectionState & { status: Event }) => void,\n\t): this;\n}\n\n/**\n * A connection to the voice server of a Guild, can be used to play audio in voice channels.\n */\nexport class VoiceConnection extends EventEmitter {\n\t/**\n\t * The number of consecutive rejoin attempts. Initially 0, and increments for each rejoin.\n\t * When a connection is successfully established, it resets to 0.\n\t */\n\tpublic rejoinAttempts: number;\n\n\t/**\n\t * The state of the voice connection.\n\t */\n\tprivate _state: VoiceConnectionState;\n\n\t/**\n\t * A configuration storing all the data needed to reconnect to a Guild's voice server.\n\t *\n\t * @internal\n\t */\n\tpublic readonly joinConfig: JoinConfig;\n\n\t/**\n\t * The two packets needed to successfully establish a voice connection. They are received\n\t * from the main Discord gateway after signalling to change the voice state.\n\t */\n\tprivate readonly packets: {\n\t\tserver: GatewayVoiceServerUpdateDispatchData | undefined;\n\t\tstate: GatewayVoiceStateUpdateDispatchData | undefined;\n\t};\n\n\t/**\n\t * The receiver of this voice connection. You should join the voice channel with `selfDeaf` set\n\t * to false for this feature to work properly.\n\t */\n\tpublic readonly receiver: VoiceReceiver;\n\n\t/**\n\t * The debug logger function, if debugging is enabled.\n\t */\n\tprivate readonly debug: ((message: string) => void) | null;\n\n\t/**\n\t * Creates a new voice connection.\n\t *\n\t * @param joinConfig - The data required to establish the voice connection\n\t * @param options - The options used to create this voice connection\n\t */\n\tpublic constructor(joinConfig: JoinConfig, options: CreateVoiceConnectionOptions) {\n\t\tsuper();\n\n\t\tthis.debug = options.debug ? (message: string) => this.emit('debug', message) : null;\n\t\tthis.rejoinAttempts = 0;\n\n\t\tthis.receiver = new VoiceReceiver(this);\n\n\t\tthis.onNetworkingClose = this.onNetworkingClose.bind(this);\n\t\tthis.onNetworkingStateChange = this.onNetworkingStateChange.bind(this);\n\t\tthis.onNetworkingError = this.onNetworkingError.bind(this);\n\t\tthis.onNetworkingDebug = this.onNetworkingDebug.bind(this);\n\n\t\tconst adapter = options.adapterCreator({\n\t\t\tonVoiceServerUpdate: (data) => this.addServerPacket(data),\n\t\t\tonVoiceStateUpdate: (data) => this.addStatePacket(data),\n\t\t\tdestroy: () => this.destroy(false),\n\t\t});\n\n\t\tthis._state = { status: VoiceConnectionStatus.Signalling, adapter };\n\n\t\tthis.packets = {\n\t\t\tserver: undefined,\n\t\t\tstate: undefined,\n\t\t};\n\n\t\tthis.joinConfig = joinConfig;\n\t}\n\n\t/**\n\t * The current state of the voice connection.\n\t */\n\tpublic get state() {\n\t\treturn this._state;\n\t}\n\n\t/**\n\t * Updates the state of the voice connection, performing clean-up operations where necessary.\n\t */\n\tpublic set state(newState: VoiceConnectionState) {\n\t\tconst oldState = this._state;\n\t\tconst oldNetworking = Reflect.get(oldState, 'networking') as Networking | undefined;\n\t\tconst newNetworking = Reflect.get(newState, 'networking') as Networking | undefined;\n\n\t\tconst oldSubscription = Reflect.get(oldState, 'subscription') as PlayerSubscription | undefined;\n\t\tconst newSubscription = Reflect.get(newState, 'subscription') as PlayerSubscription | undefined;\n\n\t\tif (oldNetworking !== newNetworking) {\n\t\t\tif (oldNetworking) {\n\t\t\t\toldNetworking.on('error', noop);\n\t\t\t\toldNetworking.off('debug', this.onNetworkingDebug);\n\t\t\t\toldNetworking.off('error', this.onNetworkingError);\n\t\t\t\toldNetworking.off('close', this.onNetworkingClose);\n\t\t\t\toldNetworking.off('stateChange', this.onNetworkingStateChange);\n\t\t\t\toldNetworking.destroy();\n\t\t\t}\n\n\t\t\tif (newNetworking) this.updateReceiveBindings(newNetworking.state, oldNetworking?.state);\n\t\t}\n\n\t\tif (newState.status === VoiceConnectionStatus.Ready) {\n\t\t\tthis.rejoinAttempts = 0;\n\t\t} else if (newState.status === VoiceConnectionStatus.Destroyed) {\n\t\t\tfor (const stream of this.receiver.subscriptions.values()) {\n\t\t\t\tif (!stream.destroyed) stream.destroy();\n\t\t\t}\n\t\t}\n\n\t\t// If destroyed, the adapter can also be destroyed so it can be cleaned up by the user\n\t\tif (oldState.status !== VoiceConnectionStatus.Destroyed && newState.status === VoiceConnectionStatus.Destroyed) {\n\t\t\toldState.adapter.destroy();\n\t\t}\n\n\t\tthis._state = newState;\n\n\t\tif (oldSubscription && oldSubscription !== newSubscription) {\n\t\t\toldSubscription.unsubscribe();\n\t\t}\n\n\t\tthis.emit('stateChange', oldState, newState);\n\t\tif (oldState.status !== newState.status) {\n\t\t\tthis.emit(newState.status, oldState, newState as any);\n\t\t}\n\t}\n\n\t/**\n\t * Registers a `VOICE_SERVER_UPDATE` packet to the voice connection. This will cause it to reconnect using the\n\t * new data provided in the packet.\n\t *\n\t * @param packet - The received `VOICE_SERVER_UPDATE` packet\n\t */\n\tprivate addServerPacket(packet: GatewayVoiceServerUpdateDispatchData) {\n\t\tthis.packets.server = packet;\n\t\tif (packet.endpoint) {\n\t\t\tthis.configureNetworking();\n\t\t} else if (this.state.status !== VoiceConnectionStatus.Destroyed) {\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tstatus: VoiceConnectionStatus.Disconnected,\n\t\t\t\treason: VoiceConnectionDisconnectReason.EndpointRemoved,\n\t\t\t};\n\t\t}\n\t}\n\n\t/**\n\t * Registers a `VOICE_STATE_UPDATE` packet to the voice connection. Most importantly, it stores the id of the\n\t * channel that the client is connected to.\n\t *\n\t * @param packet - The received `VOICE_STATE_UPDATE` packet\n\t */\n\tprivate addStatePacket(packet: GatewayVoiceStateUpdateDispatchData) {\n\t\tthis.packets.state = packet;\n\n\t\tif (packet.self_deaf !== undefined) this.joinConfig.selfDeaf = packet.self_deaf;\n\t\tif (packet.self_mute !== undefined) this.joinConfig.selfMute = packet.self_mute;\n\t\tif (packet.channel_id) this.joinConfig.channelId = packet.channel_id;\n\t\t/*\n\t\t\tthe channel_id being null doesn't necessarily mean it was intended for the client to leave the voice channel\n\t\t\tas it may have disconnected due to network failure. This will be gracefully handled once the voice websocket\n\t\t\tdies, and then it is up to the user to decide how they wish to handle this.\n\t\t*/\n\t}\n\n\t/**\n\t * Called when the networking state changes, and the new ws/udp packet/message handlers need to be rebound\n\t * to the new instances.\n\t *\n\t * @param newState - The new networking state\n\t * @param oldState - The old networking state, if there is one\n\t */\n\tprivate updateReceiveBindings(newState: NetworkingState, oldState?: NetworkingState) {\n\t\tconst oldWs = Reflect.get(oldState ?? {}, 'ws') as VoiceWebSocket | undefined;\n\t\tconst newWs = Reflect.get(newState, 'ws') as VoiceWebSocket | undefined;\n\t\tconst oldUdp = Reflect.get(oldState ?? {}, 'udp') as VoiceUDPSocket | undefined;\n\t\tconst newUdp = Reflect.get(newState, 'udp') as VoiceUDPSocket | undefined;\n\n\t\tif (oldWs !== newWs) {\n\t\t\toldWs?.off('packet', this.receiver.onWsPacket);\n\t\t\tnewWs?.on('packet', this.receiver.onWsPacket);\n\t\t}\n\n\t\tif (oldUdp !== newUdp) {\n\t\t\toldUdp?.off('message', this.receiver.onUdpMessage);\n\t\t\tnewUdp?.on('message', this.receiver.onUdpMessage);\n\t\t}\n\n\t\tthis.receiver.connectionData = Reflect.get(newState, 'connectionData') ?? {};\n\t}\n\n\t/**\n\t * Attempts to configure a networking instance for this voice connection using the received packets.\n\t * Both packets are required, and any existing networking instance will be destroyed.\n\t *\n\t * @remarks\n\t * This is called when the voice server of the connection changes, e.g. if the bot is moved into a\n\t * different channel in the same guild but has a different voice server. In this instance, the connection\n\t * needs to be re-established to the new voice server.\n\t *\n\t * The connection will transition to the Connecting state when this is called.\n\t */\n\tpublic configureNetworking() {\n\t\tconst { server, state } = this.packets;\n\t\tif (!server || !state || this.state.status === VoiceConnectionStatus.Destroyed || !server.endpoint) return;\n\n\t\tconst networking = new Networking(\n\t\t\t{\n\t\t\t\tendpoint: server.endpoint,\n\t\t\t\tserverId: server.guild_id,\n\t\t\t\ttoken: server.token,\n\t\t\t\tsessionId: state.session_id,\n\t\t\t\tuserId: state.user_id,\n\t\t\t},\n\t\t\tBoolean(this.debug),\n\t\t);\n\n\t\tnetworking.once('close', this.onNetworkingClose);\n\t\tnetworking.on('stateChange', this.onNetworkingStateChange);\n\t\tnetworking.on('error', this.onNetworkingError);\n\t\tnetworking.on('debug', this.onNetworkingDebug);\n\n\t\tthis.state = {\n\t\t\t...this.state,\n\t\t\tstatus: VoiceConnectionStatus.Connecting,\n\t\t\tnetworking,\n\t\t};\n\t}\n\n\t/**\n\t * Called when the networking instance for this connection closes. If the close code is 4014 (do not reconnect),\n\t * the voice connection will transition to the Disconnected state which will store the close code. You can\n\t * decide whether or not to reconnect when this occurs by listening for the state change and calling reconnect().\n\t *\n\t * @remarks\n\t * If the close code was anything other than 4014, it is likely that the closing was not intended, and so the\n\t * VoiceConnection will signal to Discord that it would like to rejoin the channel. This automatically attempts\n\t * to re-establish the connection. This would be seen as a transition from the Ready state to the Signalling state.\n\t * @param code - The close code\n\t */\n\tprivate onNetworkingClose(code: number) {\n\t\tif (this.state.status === VoiceConnectionStatus.Destroyed) return;\n\t\t// If networking closes, try to connect to the voice channel again.\n\t\tif (code === 4_014) {\n\t\t\t// Disconnected - networking is already destroyed here\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tstatus: VoiceConnectionStatus.Disconnected,\n\t\t\t\treason: VoiceConnectionDisconnectReason.WebSocketClose,\n\t\t\t\tcloseCode: code,\n\t\t\t};\n\t\t} else {\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tstatus: VoiceConnectionStatus.Signalling,\n\t\t\t};\n\t\t\tthis.rejoinAttempts++;\n\t\t\tif (!this.state.adapter.sendPayload(createJoinVoiceChannelPayload(this.joinConfig))) {\n\t\t\t\tthis.state = {\n\t\t\t\t\t...this.state,\n\t\t\t\t\tstatus: VoiceConnectionStatus.Disconnected,\n\t\t\t\t\treason: VoiceConnectionDisconnectReason.AdapterUnavailable,\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Called when the state of the networking instance changes. This is used to derive the state of the voice connection.\n\t *\n\t * @param oldState - The previous state\n\t * @param newState - The new state\n\t */\n\tprivate onNetworkingStateChange(oldState: NetworkingState, newState: NetworkingState) {\n\t\tthis.updateReceiveBindings(newState, oldState);\n\t\tif (oldState.code === newState.code) return;\n\t\tif (this.state.status !== VoiceConnectionStatus.Connecting && this.state.status !== VoiceConnectionStatus.Ready)\n\t\t\treturn;\n\n\t\tif (newState.code === NetworkingStatusCode.Ready) {\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tstatus: VoiceConnectionStatus.Ready,\n\t\t\t};\n\t\t} else if (newState.code !== NetworkingStatusCode.Closed) {\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tstatus: VoiceConnectionStatus.Connecting,\n\t\t\t};\n\t\t}\n\t}\n\n\t/**\n\t * Propagates errors from the underlying network instance.\n\t *\n\t * @param error - The error to propagate\n\t */\n\tprivate onNetworkingError(error: Error) {\n\t\tthis.emit('error', error);\n\t}\n\n\t/**\n\t * Propagates debug messages from the underlying network instance.\n\t *\n\t * @param message - The debug message to propagate\n\t */\n\tprivate onNetworkingDebug(message: string) {\n\t\tthis.debug?.(`[NW] ${message}`);\n\t}\n\n\t/**\n\t * Prepares an audio packet for dispatch.\n\t *\n\t * @param buffer - The Opus packet to prepare\n\t */\n\tpublic prepareAudioPacket(buffer: Buffer) {\n\t\tconst state = this.state;\n\t\tif (state.status !== VoiceConnectionStatus.Ready) return;\n\t\treturn state.networking.prepareAudioPacket(buffer);\n\t}\n\n\t/**\n\t * Dispatches the previously prepared audio packet (if any)\n\t */\n\tpublic dispatchAudio() {\n\t\tconst state = this.state;\n\t\tif (state.status !== VoiceConnectionStatus.Ready) return;\n\t\treturn state.networking.dispatchAudio();\n\t}\n\n\t/**\n\t * Prepares an audio packet and dispatches it immediately.\n\t *\n\t * @param buffer - The Opus packet to play\n\t */\n\tpublic playOpusPacket(buffer: Buffer) {\n\t\tconst state = this.state;\n\t\tif (state.status !== VoiceConnectionStatus.Ready) return;\n\t\tstate.networking.prepareAudioPacket(buffer);\n\t\treturn state.networking.dispatchAudio();\n\t}\n\n\t/**\n\t * Destroys the VoiceConnection, preventing it from connecting to voice again.\n\t * This method should be called when you no longer require the VoiceConnection to\n\t * prevent memory leaks.\n\t *\n\t * @param adapterAvailable - Whether the adapter can be used\n\t */\n\tpublic destroy(adapterAvailable = true) {\n\t\tif (this.state.status === VoiceConnectionStatus.Destroyed) {\n\t\t\tthrow new Error('Cannot destroy VoiceConnection - it has already been destroyed');\n\t\t}\n\n\t\tif (getVoiceConnection(this.joinConfig.guildId, this.joinConfig.group) === this) {\n\t\t\tuntrackVoiceConnection(this);\n\t\t}\n\n\t\tif (adapterAvailable) {\n\t\t\tthis.state.adapter.sendPayload(createJoinVoiceChannelPayload({ ...this.joinConfig, channelId: null }));\n\t\t}\n\n\t\tthis.state = {\n\t\t\tstatus: VoiceConnectionStatus.Destroyed,\n\t\t};\n\t}\n\n\t/**\n\t * Disconnects the VoiceConnection, allowing the possibility of rejoining later on.\n\t *\n\t * @returns `true` if the connection was successfully disconnected\n\t */\n\tpublic disconnect() {\n\t\tif (\n\t\t\tthis.state.status === VoiceConnectionStatus.Destroyed ||\n\t\t\tthis.state.status === VoiceConnectionStatus.Signalling\n\t\t) {\n\t\t\treturn false;\n\t\t}\n\n\t\tthis.joinConfig.channelId = null;\n\t\tif (!this.state.adapter.sendPayload(createJoinVoiceChannelPayload(this.joinConfig))) {\n\t\t\tthis.state = {\n\t\t\t\tadapter: this.state.adapter,\n\t\t\t\tsubscription: this.state.subscription,\n\t\t\t\tstatus: VoiceConnectionStatus.Disconnected,\n\t\t\t\treason: VoiceConnectionDisconnectReason.AdapterUnavailable,\n\t\t\t};\n\t\t\treturn false;\n\t\t}\n\n\t\tthis.state = {\n\t\t\tadapter: this.state.adapter,\n\t\t\treason: VoiceConnectionDisconnectReason.Manual,\n\t\t\tstatus: VoiceConnectionStatus.Disconnected,\n\t\t};\n\t\treturn true;\n\t}\n\n\t/**\n\t * Attempts to rejoin (better explanation soon:tm:)\n\t *\n\t * @remarks\n\t * Calling this method successfully will automatically increment the `rejoinAttempts` counter,\n\t * which you can use to inform whether or not you'd like to keep attempting to reconnect your\n\t * voice connection.\n\t *\n\t * A state transition from Disconnected to Signalling will be observed when this is called.\n\t */\n\tpublic rejoin(joinConfig?: Omit<JoinConfig, 'group' | 'guildId'>) {\n\t\tif (this.state.status === VoiceConnectionStatus.Destroyed) {\n\t\t\treturn false;\n\t\t}\n\n\t\tconst notReady = this.state.status !== VoiceConnectionStatus.Ready;\n\n\t\tif (notReady) this.rejoinAttempts++;\n\t\tObject.assign(this.joinConfig, joinConfig);\n\t\tif (this.state.adapter.sendPayload(createJoinVoiceChannelPayload(this.joinConfig))) {\n\t\t\tif (notReady) {\n\t\t\t\tthis.state = {\n\t\t\t\t\t...this.state,\n\t\t\t\t\tstatus: VoiceConnectionStatus.Signalling,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn true;\n\t\t}\n\n\t\tthis.state = {\n\t\t\tadapter: this.state.adapter,\n\t\t\tsubscription: this.state.subscription,\n\t\t\tstatus: VoiceConnectionStatus.Disconnected,\n\t\t\treason: VoiceConnectionDisconnectReason.AdapterUnavailable,\n\t\t};\n\t\treturn false;\n\t}\n\n\t/**\n\t * Updates the speaking status of the voice connection. This is used when audio players are done playing audio,\n\t * and need to signal that the connection is no longer playing audio.\n\t *\n\t * @param enabled - Whether or not to show as speaking\n\t */\n\tpublic setSpeaking(enabled: boolean) {\n\t\tif (this.state.status !== VoiceConnectionStatus.Ready) return false;\n\t\t// eslint-disable-next-line @typescript-eslint/no-confusing-void-expression\n\t\treturn this.state.networking.setSpeaking(enabled);\n\t}\n\n\t/**\n\t * Subscribes to an audio player, allowing the player to play audio on this voice connection.\n\t *\n\t * @param player - The audio player to subscribe to\n\t * @returns The created subscription\n\t */\n\tpublic subscribe(player: AudioPlayer) {\n\t\tif (this.state.status === VoiceConnectionStatus.Destroyed) return;\n\n\t\t// eslint-disable-next-line @typescript-eslint/dot-notation\n\t\tconst subscription = player['subscribe'](this);\n\n\t\tthis.state = {\n\t\t\t...this.state,\n\t\t\tsubscription,\n\t\t};\n\n\t\treturn subscription;\n\t}\n\n\t/**\n\t * The latest ping (in milliseconds) for the WebSocket connection and audio playback for this voice\n\t * connection, if this data is available.\n\t *\n\t * @remarks\n\t * For this data to be available, the VoiceConnection must be in the Ready state, and its underlying\n\t * WebSocket connection and UDP socket must have had at least one ping-pong exchange.\n\t */\n\tpublic get ping() {\n\t\tif (\n\t\t\tthis.state.status === VoiceConnectionStatus.Ready &&\n\t\t\tthis.state.networking.state.code === NetworkingStatusCode.Ready\n\t\t) {\n\t\t\treturn {\n\t\t\t\tws: this.state.networking.state.ws.ping,\n\t\t\t\tudp: this.state.networking.state.udp.ping,\n\t\t\t};\n\t\t}\n\n\t\treturn {\n\t\t\tws: undefined,\n\t\t\tudp: undefined,\n\t\t};\n\t}\n\n\t/**\n\t * Called when a subscription of this voice connection to an audio player is removed.\n\t *\n\t * @param subscription - The removed subscription\n\t */\n\tprotected onSubscriptionRemoved(subscription: PlayerSubscription) {\n\t\tif (this.state.status !== VoiceConnectionStatus.Destroyed && this.state.subscription === subscription) {\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tsubscription: undefined,\n\t\t\t};\n\t\t}\n\t}\n}\n\n/**\n * Creates a new voice connection.\n *\n * @param joinConfig - The data required to establish the voice connection\n * @param options - The options to use when joining the voice channel\n */\nexport function createVoiceConnection(joinConfig: JoinConfig, options: CreateVoiceConnectionOptions) {\n\tconst payload = createJoinVoiceChannelPayload(joinConfig);\n\tconst existing = getVoiceConnection(joinConfig.guildId, joinConfig.group);\n\tif (existing && existing.state.status !== VoiceConnectionStatus.Destroyed) {\n\t\tif (existing.state.status === VoiceConnectionStatus.Disconnected) {\n\t\t\texisting.rejoin({\n\t\t\t\tchannelId: joinConfig.channelId,\n\t\t\t\tselfDeaf: joinConfig.selfDeaf,\n\t\t\t\tselfMute: joinConfig.selfMute,\n\t\t\t});\n\t\t} else if (!existing.state.adapter.sendPayload(payload)) {\n\t\t\texisting.state = {\n\t\t\t\t...existing.state,\n\t\t\t\tstatus: VoiceConnectionStatus.Disconnected,\n\t\t\t\treason: VoiceConnectionDisconnectReason.AdapterUnavailable,\n\t\t\t};\n\t\t}\n\n\t\treturn existing;\n\t}\n\n\tconst voiceConnection = new VoiceConnection(joinConfig, options);\n\ttrackVoiceConnection(voiceConnection);\n\tif (\n\t\tvoiceConnection.state.status !== VoiceConnectionStatus.Destroyed &&\n\t\t!voiceConnection.state.adapter.sendPayload(payload)\n\t) {\n\t\tvoiceConnection.state = {\n\t\t\t...voiceConnection.state,\n\t\t\tstatus: VoiceConnectionStatus.Disconnected,\n\t\t\treason: VoiceConnectionDisconnectReason.AdapterUnavailable,\n\t\t};\n\t}\n\n\treturn voiceConnection;\n}\n", "import { GatewayOpcodes } from 'discord-api-types/v10';\nimport type { VoiceConnection } from './VoiceConnection';\nimport type { AudioPlayer } from './audio/index';\n\nexport interface JoinConfig {\n\tchannelId: string | null;\n\tgroup: string;\n\tguildId: string;\n\tselfDeaf: boolean;\n\tselfMute: boolean;\n}\n\n/**\n * Sends a voice state update to the main websocket shard of a guild, to indicate joining/leaving/moving across\n * voice channels.\n *\n * @param config - The configuration to use when joining the voice channel\n */\nexport function createJoinVoiceChannelPayload(config: JoinConfig) {\n\treturn {\n\t\top: GatewayOpcodes.VoiceStateUpdate,\n\t\t// eslint-disable-next-line id-length\n\t\td: {\n\t\t\tguild_id: config.guildId,\n\t\t\tchannel_id: config.channelId,\n\t\t\tself_deaf: config.selfDeaf,\n\t\t\tself_mute: config.selfMute,\n\t\t},\n\t};\n}\n\n// Voice Connections\nconst groups = new Map<string, Map<string, VoiceConnection>>();\ngroups.set('default', new Map());\n\nfunction getOrCreateGroup(group: string) {\n\tconst existing = groups.get(group);\n\tif (existing) return existing;\n\tconst map = new Map<string, VoiceConnection>();\n\tgroups.set(group, map);\n\treturn map;\n}\n\n/**\n * Retrieves the map of group names to maps of voice connections. By default, all voice connections\n * are created under the 'default' group.\n *\n * @returns The group map\n */\nexport function getGroups() {\n\treturn groups;\n}\n\n/**\n * Retrieves all the voice connections under the 'default' group.\n *\n * @param group - The group to look up\n * @returns The map of voice connections\n */\nexport function getVoiceConnections(group?: 'default'): Map<string, VoiceConnection>;\n\n/**\n * Retrieves all the voice connections under the given group name.\n *\n * @param group - The group to look up\n * @returns The map of voice connections\n */\nexport function getVoiceConnections(group: string): Map<string, VoiceConnection> | undefined;\n\n/**\n * Retrieves all the voice connections under the given group name. Defaults to the 'default' group.\n *\n * @param group - The group to look up\n * @returns The map of voice connections\n */\nexport function getVoiceConnections(group = 'default') {\n\treturn groups.get(group);\n}\n\n/**\n * Finds a voice connection with the given guild id and group. Defaults to the 'default' group.\n *\n * @param guildId - The guild id of the voice connection\n * @param group - the group that the voice connection was registered with\n * @returns The voice connection, if it exists\n */\nexport function getVoiceConnection(guildId: string, group = 'default') {\n\treturn getVoiceConnections(group)?.get(guildId);\n}\n\nexport function untrackVoiceConnection(voiceConnection: VoiceConnection) {\n\treturn getVoiceConnections(voiceConnection.joinConfig.group)?.delete(voiceConnection.joinConfig.guildId);\n}\n\nexport function trackVoiceConnection(voiceConnection: VoiceConnection) {\n\treturn getOrCreateGroup(voiceConnection.joinConfig.group).set(voiceConnection.joinConfig.guildId, voiceConnection);\n}\n\n// Audio Players\n\n// Each audio packet is 20ms long\nconst FRAME_LENGTH = 20;\n\nlet audioCycleInterval: NodeJS.Timeout | undefined;\nlet nextTime = -1;\n\n/**\n * A list of created audio players that are still active and haven't been destroyed.\n */\nconst audioPlayers: AudioPlayer[] = [];\n\n/**\n * Called roughly every 20 milliseconds. Dispatches audio from all players, and then gets the players to prepare\n * the next audio frame.\n */\nfunction audioCycleStep() {\n\tif (nextTime === -1) return;\n\n\tnextTime += FRAME_LENGTH;\n\tconst available = audioPlayers.filter((player) => player.checkPlayable());\n\n\tfor (const player of available) {\n\t\t// eslint-disable-next-line @typescript-eslint/dot-notation\n\t\tplayer['_stepDispatch']();\n\t}\n\n\tprepareNextAudioFrame(available);\n}\n\n/**\n * Recursively gets the players that have been passed as parameters to prepare audio frames that can be played\n * at the start of the next cycle.\n */\nfunction prepareNextAudioFrame(players: AudioPlayer[]) {\n\tconst nextPlayer = players.shift();\n\n\tif (!nextPlayer) {\n\t\tif (nextTime !== -1) {\n\t\t\taudioCycleInterval = setTimeout(() => audioCycleStep(), nextTime - Date.now());\n\t\t}\n\n\t\treturn;\n\t}\n\n\t// eslint-disable-next-line @typescript-eslint/dot-notation\n\tnextPlayer['_stepPrepare']();\n\n\t// setImmediate to avoid long audio player chains blocking other scheduled tasks\n\tsetImmediate(() => prepareNextAudioFrame(players));\n}\n\n/**\n * Checks whether or not the given audio player is being driven by the data store clock.\n *\n * @param target - The target to test for\n * @returns `true` if it is being tracked, `false` otherwise\n */\nexport function hasAudioPlayer(target: AudioPlayer) {\n\treturn audioPlayers.includes(target);\n}\n\n/**\n * Adds an audio player to the data store tracking list, if it isn't already there.\n *\n * @param player - The player to track\n */\nexport function addAudioPlayer(player: AudioPlayer) {\n\tif (hasAudioPlayer(player)) return player;\n\taudioPlayers.push(player);\n\tif (audioPlayers.length === 1) {\n\t\tnextTime = Date.now();\n\t\tsetImmediate(() => audioCycleStep());\n\t}\n\n\treturn player;\n}\n\n/**\n * Removes an audio player from the data store tracking list, if it is present there.\n */\nexport function deleteAudioPlayer(player: AudioPlayer) {\n\tconst index = audioPlayers.indexOf(player);\n\tif (index === -1) return;\n\taudioPlayers.splice(index, 1);\n\tif (audioPlayers.length === 0) {\n\t\tnextTime = -1;\n\t\tif (audioCycleInterval !== undefined) clearTimeout(audioCycleInterval);\n\t}\n}\n", "/* eslint-disable jsdoc/check-param-names */\n/* eslint-disable id-length */\n/* eslint-disable @typescript-eslint/unbound-method */\nimport { <PERSON>uffer } from 'node:buffer';\nimport { EventEmitter } from 'node:events';\nimport { VoiceOpcodes } from 'discord-api-types/voice/v4';\nimport type { CloseEvent } from 'ws';\nimport * as secretbox from '../util/Secretbox';\nimport { noop } from '../util/util';\nimport { VoiceUDPSocket } from './VoiceUDPSocket';\nimport { VoiceWebSocket } from './VoiceWebSocket';\n\n// The number of audio channels required by Discord\nconst CHANNELS = 2;\nconst TIMESTAMP_INC = (48_000 / 100) * CHANNELS;\nconst MAX_NONCE_SIZE = 2 ** 32 - 1;\n\nexport const SUPPORTED_ENCRYPTION_MODES = ['xsalsa20_poly1305_lite', 'xsalsa20_poly1305_suffix', 'xsalsa20_poly1305'];\n\n/**\n * The different statuses that a networking instance can hold. The order\n * of the states between OpeningWs and Ready is chronological (first the\n * instance enters OpeningWs, then it enters Identifying etc.)\n */\nexport enum NetworkingStatusCode {\n\tOpeningWs,\n\tIdentifying,\n\tUdpHandshaking,\n\tSelectingProtocol,\n\tReady,\n\tResuming,\n\tClosed,\n}\n\n/**\n * The initial Networking state. Instances will be in this state when a WebSocket connection to a Discord\n * voice gateway is being opened.\n */\nexport interface NetworkingOpeningWsState {\n\tcode: NetworkingStatusCode.OpeningWs;\n\tconnectionOptions: ConnectionOptions;\n\tws: VoiceWebSocket;\n}\n\n/**\n * The state that a Networking instance will be in when it is attempting to authorize itself.\n */\nexport interface NetworkingIdentifyingState {\n\tcode: NetworkingStatusCode.Identifying;\n\tconnectionOptions: ConnectionOptions;\n\tws: VoiceWebSocket;\n}\n\n/**\n * The state that a Networking instance will be in when opening a UDP connection to the IP and port provided\n * by Discord, as well as performing IP discovery.\n */\nexport interface NetworkingUdpHandshakingState {\n\tcode: NetworkingStatusCode.UdpHandshaking;\n\tconnectionData: Pick<ConnectionData, 'ssrc'>;\n\tconnectionOptions: ConnectionOptions;\n\tudp: VoiceUDPSocket;\n\tws: VoiceWebSocket;\n}\n\n/**\n * The state that a Networking instance will be in when selecting an encryption protocol for audio packets.\n */\nexport interface NetworkingSelectingProtocolState {\n\tcode: NetworkingStatusCode.SelectingProtocol;\n\tconnectionData: Pick<ConnectionData, 'ssrc'>;\n\tconnectionOptions: ConnectionOptions;\n\tudp: VoiceUDPSocket;\n\tws: VoiceWebSocket;\n}\n\n/**\n * The state that a Networking instance will be in when it has a fully established connection to a Discord\n * voice server.\n */\nexport interface NetworkingReadyState {\n\tcode: NetworkingStatusCode.Ready;\n\tconnectionData: ConnectionData;\n\tconnectionOptions: ConnectionOptions;\n\tpreparedPacket?: Buffer | undefined;\n\tudp: VoiceUDPSocket;\n\tws: VoiceWebSocket;\n}\n\n/**\n * The state that a Networking instance will be in when its connection has been dropped unexpectedly, and it\n * is attempting to resume an existing session.\n */\nexport interface NetworkingResumingState {\n\tcode: NetworkingStatusCode.Resuming;\n\tconnectionData: ConnectionData;\n\tconnectionOptions: ConnectionOptions;\n\tpreparedPacket?: Buffer | undefined;\n\tudp: VoiceUDPSocket;\n\tws: VoiceWebSocket;\n}\n\n/**\n * The state that a Networking instance will be in when it has been destroyed. It cannot be recovered from this\n * state.\n */\nexport interface NetworkingClosedState {\n\tcode: NetworkingStatusCode.Closed;\n}\n\n/**\n * The various states that a networking instance can be in.\n */\nexport type NetworkingState =\n\t| NetworkingClosedState\n\t| NetworkingIdentifyingState\n\t| NetworkingOpeningWsState\n\t| NetworkingReadyState\n\t| NetworkingResumingState\n\t| NetworkingSelectingProtocolState\n\t| NetworkingUdpHandshakingState;\n\n/**\n * Details required to connect to the Discord voice gateway. These details\n * are first received on the main bot gateway, in the form of VOICE_SERVER_UPDATE\n * and VOICE_STATE_UPDATE packets.\n */\ninterface ConnectionOptions {\n\tendpoint: string;\n\tserverId: string;\n\tsessionId: string;\n\ttoken: string;\n\tuserId: string;\n}\n\n/**\n * Information about the current connection, e.g. which encryption mode is to be used on\n * the connection, timing information for playback of streams.\n */\nexport interface ConnectionData {\n\tencryptionMode: string;\n\tnonce: number;\n\tnonceBuffer: Buffer;\n\tpacketsPlayed: number;\n\tsecretKey: Uint8Array;\n\tsequence: number;\n\tspeaking: boolean;\n\tssrc: number;\n\ttimestamp: number;\n}\n\n/**\n * An empty buffer that is reused in packet encryption by many different networking instances.\n */\nconst nonce = Buffer.alloc(24);\n\nexport interface Networking extends EventEmitter {\n\t/**\n\t * Debug event for Networking.\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'debug', listener: (message: string) => void): this;\n\ton(event: 'error', listener: (error: Error) => void): this;\n\ton(event: 'stateChange', listener: (oldState: NetworkingState, newState: NetworkingState) => void): this;\n\ton(event: 'close', listener: (code: number) => void): this;\n}\n\n/**\n * Stringifies a NetworkingState.\n *\n * @param state - The state to stringify\n */\nfunction stringifyState(state: NetworkingState) {\n\treturn JSON.stringify({\n\t\t...state,\n\t\tws: Reflect.has(state, 'ws'),\n\t\tudp: Reflect.has(state, 'udp'),\n\t});\n}\n\n/**\n * Chooses an encryption mode from a list of given options. Chooses the most preferred option.\n *\n * @param options - The available encryption options\n */\nfunction chooseEncryptionMode(options: string[]): string {\n\tconst option = options.find((option) => SUPPORTED_ENCRYPTION_MODES.includes(option));\n\tif (!option) {\n\t\tthrow new Error(`No compatible encryption modes. Available include: ${options.join(', ')}`);\n\t}\n\n\treturn option;\n}\n\n/**\n * Returns a random number that is in the range of n bits.\n *\n * @param numberOfBits - The number of bits\n */\nfunction randomNBit(numberOfBits: number) {\n\treturn Math.floor(Math.random() * 2 ** numberOfBits);\n}\n\n/**\n * Manages the networking required to maintain a voice connection and dispatch audio packets\n */\nexport class Networking extends EventEmitter {\n\tprivate _state: NetworkingState;\n\n\t/**\n\t * The debug logger function, if debugging is enabled.\n\t */\n\tprivate readonly debug: ((message: string) => void) | null;\n\n\t/**\n\t * Creates a new Networking instance.\n\t */\n\tpublic constructor(options: ConnectionOptions, debug: boolean) {\n\t\tsuper();\n\n\t\tthis.onWsOpen = this.onWsOpen.bind(this);\n\t\tthis.onChildError = this.onChildError.bind(this);\n\t\tthis.onWsPacket = this.onWsPacket.bind(this);\n\t\tthis.onWsClose = this.onWsClose.bind(this);\n\t\tthis.onWsDebug = this.onWsDebug.bind(this);\n\t\tthis.onUdpDebug = this.onUdpDebug.bind(this);\n\t\tthis.onUdpClose = this.onUdpClose.bind(this);\n\n\t\tthis.debug = debug ? (message: string) => this.emit('debug', message) : null;\n\n\t\tthis._state = {\n\t\t\tcode: NetworkingStatusCode.OpeningWs,\n\t\t\tws: this.createWebSocket(options.endpoint),\n\t\t\tconnectionOptions: options,\n\t\t};\n\t}\n\n\t/**\n\t * Destroys the Networking instance, transitioning it into the Closed state.\n\t */\n\tpublic destroy() {\n\t\tthis.state = {\n\t\t\tcode: NetworkingStatusCode.Closed,\n\t\t};\n\t}\n\n\t/**\n\t * The current state of the networking instance.\n\t */\n\tpublic get state(): NetworkingState {\n\t\treturn this._state;\n\t}\n\n\t/**\n\t * Sets a new state for the networking instance, performing clean-up operations where necessary.\n\t */\n\tpublic set state(newState: NetworkingState) {\n\t\tconst oldWs = Reflect.get(this._state, 'ws') as VoiceWebSocket | undefined;\n\t\tconst newWs = Reflect.get(newState, 'ws') as VoiceWebSocket | undefined;\n\t\tif (oldWs && oldWs !== newWs) {\n\t\t\t// The old WebSocket is being freed - remove all handlers from it\n\t\t\toldWs.off('debug', this.onWsDebug);\n\t\t\toldWs.on('error', noop);\n\t\t\toldWs.off('error', this.onChildError);\n\t\t\toldWs.off('open', this.onWsOpen);\n\t\t\toldWs.off('packet', this.onWsPacket);\n\t\t\toldWs.off('close', this.onWsClose);\n\t\t\toldWs.destroy();\n\t\t}\n\n\t\tconst oldUdp = Reflect.get(this._state, 'udp') as VoiceUDPSocket | undefined;\n\t\tconst newUdp = Reflect.get(newState, 'udp') as VoiceUDPSocket | undefined;\n\n\t\tif (oldUdp && oldUdp !== newUdp) {\n\t\t\toldUdp.on('error', noop);\n\t\t\toldUdp.off('error', this.onChildError);\n\t\t\toldUdp.off('close', this.onUdpClose);\n\t\t\toldUdp.off('debug', this.onUdpDebug);\n\t\t\toldUdp.destroy();\n\t\t}\n\n\t\tconst oldState = this._state;\n\t\tthis._state = newState;\n\t\tthis.emit('stateChange', oldState, newState);\n\n\t\tthis.debug?.(`state change:\\nfrom ${stringifyState(oldState)}\\nto ${stringifyState(newState)}`);\n\t}\n\n\t/**\n\t * Creates a new WebSocket to a Discord Voice gateway.\n\t *\n\t * @param endpoint - The endpoint to connect to\n\t */\n\tprivate createWebSocket(endpoint: string) {\n\t\tconst ws = new VoiceWebSocket(`wss://${endpoint}?v=4`, Boolean(this.debug));\n\n\t\tws.on('error', this.onChildError);\n\t\tws.once('open', this.onWsOpen);\n\t\tws.on('packet', this.onWsPacket);\n\t\tws.once('close', this.onWsClose);\n\t\tws.on('debug', this.onWsDebug);\n\n\t\treturn ws;\n\t}\n\n\t/**\n\t * Propagates errors from the children VoiceWebSocket and VoiceUDPSocket.\n\t *\n\t * @param error - The error that was emitted by a child\n\t */\n\tprivate onChildError(error: Error) {\n\t\tthis.emit('error', error);\n\t}\n\n\t/**\n\t * Called when the WebSocket opens. Depending on the state that the instance is in,\n\t * it will either identify with a new session, or it will attempt to resume an existing session.\n\t */\n\tprivate onWsOpen() {\n\t\tif (this.state.code === NetworkingStatusCode.OpeningWs) {\n\t\t\tconst packet = {\n\t\t\t\top: VoiceOpcodes.Identify,\n\t\t\t\td: {\n\t\t\t\t\tserver_id: this.state.connectionOptions.serverId,\n\t\t\t\t\tuser_id: this.state.connectionOptions.userId,\n\t\t\t\t\tsession_id: this.state.connectionOptions.sessionId,\n\t\t\t\t\ttoken: this.state.connectionOptions.token,\n\t\t\t\t},\n\t\t\t};\n\t\t\tthis.state.ws.sendPacket(packet);\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tcode: NetworkingStatusCode.Identifying,\n\t\t\t};\n\t\t} else if (this.state.code === NetworkingStatusCode.Resuming) {\n\t\t\tconst packet = {\n\t\t\t\top: VoiceOpcodes.Resume,\n\t\t\t\td: {\n\t\t\t\t\tserver_id: this.state.connectionOptions.serverId,\n\t\t\t\t\tsession_id: this.state.connectionOptions.sessionId,\n\t\t\t\t\ttoken: this.state.connectionOptions.token,\n\t\t\t\t},\n\t\t\t};\n\t\t\tthis.state.ws.sendPacket(packet);\n\t\t}\n\t}\n\n\t/**\n\t * Called when the WebSocket closes. Based on the reason for closing (given by the code parameter),\n\t * the instance will either attempt to resume, or enter the closed state and emit a 'close' event\n\t * with the close code, allowing the user to decide whether or not they would like to reconnect.\n\t *\n\t * @param code - The close code\n\t */\n\tprivate onWsClose({ code }: CloseEvent) {\n\t\tconst canResume = code === 4_015 || code < 4_000;\n\t\tif (canResume && this.state.code === NetworkingStatusCode.Ready) {\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tcode: NetworkingStatusCode.Resuming,\n\t\t\t\tws: this.createWebSocket(this.state.connectionOptions.endpoint),\n\t\t\t};\n\t\t} else if (this.state.code !== NetworkingStatusCode.Closed) {\n\t\t\tthis.destroy();\n\t\t\tthis.emit('close', code);\n\t\t}\n\t}\n\n\t/**\n\t * Called when the UDP socket has closed itself if it has stopped receiving replies from Discord.\n\t */\n\tprivate onUdpClose() {\n\t\tif (this.state.code === NetworkingStatusCode.Ready) {\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tcode: NetworkingStatusCode.Resuming,\n\t\t\t\tws: this.createWebSocket(this.state.connectionOptions.endpoint),\n\t\t\t};\n\t\t}\n\t}\n\n\t/**\n\t * Called when a packet is received on the connection's WebSocket.\n\t *\n\t * @param packet - The received packet\n\t */\n\tprivate onWsPacket(packet: any) {\n\t\tif (packet.op === VoiceOpcodes.Hello && this.state.code !== NetworkingStatusCode.Closed) {\n\t\t\tthis.state.ws.setHeartbeatInterval(packet.d.heartbeat_interval);\n\t\t} else if (packet.op === VoiceOpcodes.Ready && this.state.code === NetworkingStatusCode.Identifying) {\n\t\t\tconst { ip, port, ssrc, modes } = packet.d;\n\n\t\t\tconst udp = new VoiceUDPSocket({ ip, port });\n\t\t\tudp.on('error', this.onChildError);\n\t\t\tudp.on('debug', this.onUdpDebug);\n\t\t\tudp.once('close', this.onUdpClose);\n\t\t\tudp\n\t\t\t\t.performIPDiscovery(ssrc)\n\t\t\t\t// eslint-disable-next-line promise/prefer-await-to-then\n\t\t\t\t.then((localConfig) => {\n\t\t\t\t\tif (this.state.code !== NetworkingStatusCode.UdpHandshaking) return;\n\t\t\t\t\tthis.state.ws.sendPacket({\n\t\t\t\t\t\top: VoiceOpcodes.SelectProtocol,\n\t\t\t\t\t\td: {\n\t\t\t\t\t\t\tprotocol: 'udp',\n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\taddress: localConfig.ip,\n\t\t\t\t\t\t\t\tport: localConfig.port,\n\t\t\t\t\t\t\t\tmode: chooseEncryptionMode(modes),\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t});\n\t\t\t\t\tthis.state = {\n\t\t\t\t\t\t...this.state,\n\t\t\t\t\t\tcode: NetworkingStatusCode.SelectingProtocol,\n\t\t\t\t\t};\n\t\t\t\t})\n\t\t\t\t// eslint-disable-next-line promise/prefer-await-to-then, promise/prefer-await-to-callbacks\n\t\t\t\t.catch((error: Error) => this.emit('error', error));\n\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tcode: NetworkingStatusCode.UdpHandshaking,\n\t\t\t\tudp,\n\t\t\t\tconnectionData: {\n\t\t\t\t\tssrc,\n\t\t\t\t},\n\t\t\t};\n\t\t} else if (\n\t\t\tpacket.op === VoiceOpcodes.SessionDescription &&\n\t\t\tthis.state.code === NetworkingStatusCode.SelectingProtocol\n\t\t) {\n\t\t\tconst { mode: encryptionMode, secret_key: secretKey } = packet.d;\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tcode: NetworkingStatusCode.Ready,\n\t\t\t\tconnectionData: {\n\t\t\t\t\t...this.state.connectionData,\n\t\t\t\t\tencryptionMode,\n\t\t\t\t\tsecretKey: new Uint8Array(secretKey),\n\t\t\t\t\tsequence: randomNBit(16),\n\t\t\t\t\ttimestamp: randomNBit(32),\n\t\t\t\t\tnonce: 0,\n\t\t\t\t\tnonceBuffer: Buffer.alloc(24),\n\t\t\t\t\tspeaking: false,\n\t\t\t\t\tpacketsPlayed: 0,\n\t\t\t\t},\n\t\t\t};\n\t\t} else if (packet.op === VoiceOpcodes.Resumed && this.state.code === NetworkingStatusCode.Resuming) {\n\t\t\tthis.state = {\n\t\t\t\t...this.state,\n\t\t\t\tcode: NetworkingStatusCode.Ready,\n\t\t\t};\n\t\t\tthis.state.connectionData.speaking = false;\n\t\t}\n\t}\n\n\t/**\n\t * Propagates debug messages from the child WebSocket.\n\t *\n\t * @param message - The emitted debug message\n\t */\n\tprivate onWsDebug(message: string) {\n\t\tthis.debug?.(`[WS] ${message}`);\n\t}\n\n\t/**\n\t * Propagates debug messages from the child UDPSocket.\n\t *\n\t * @param message - The emitted debug message\n\t */\n\tprivate onUdpDebug(message: string) {\n\t\tthis.debug?.(`[UDP] ${message}`);\n\t}\n\n\t/**\n\t * Prepares an Opus packet for playback. This includes attaching metadata to it and encrypting it.\n\t * It will be stored within the instance, and can be played by dispatchAudio()\n\t *\n\t * @remarks\n\t * Calling this method while there is already a prepared audio packet that has not yet been dispatched\n\t * will overwrite the existing audio packet. This should be avoided.\n\t * @param opusPacket - The Opus packet to encrypt\n\t * @returns The audio packet that was prepared\n\t */\n\tpublic prepareAudioPacket(opusPacket: Buffer) {\n\t\tconst state = this.state;\n\t\tif (state.code !== NetworkingStatusCode.Ready) return;\n\t\tstate.preparedPacket = this.createAudioPacket(opusPacket, state.connectionData);\n\t\treturn state.preparedPacket;\n\t}\n\n\t/**\n\t * Dispatches the audio packet previously prepared by prepareAudioPacket(opusPacket). The audio packet\n\t * is consumed and cannot be dispatched again.\n\t */\n\tpublic dispatchAudio() {\n\t\tconst state = this.state;\n\t\tif (state.code !== NetworkingStatusCode.Ready) return false;\n\t\tif (state.preparedPacket !== undefined) {\n\t\t\tthis.playAudioPacket(state.preparedPacket);\n\t\t\tstate.preparedPacket = undefined;\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t * Plays an audio packet, updating timing metadata used for playback.\n\t *\n\t * @param audioPacket - The audio packet to play\n\t */\n\tprivate playAudioPacket(audioPacket: Buffer) {\n\t\tconst state = this.state;\n\t\tif (state.code !== NetworkingStatusCode.Ready) return;\n\t\tconst { connectionData } = state;\n\t\tconnectionData.packetsPlayed++;\n\t\tconnectionData.sequence++;\n\t\tconnectionData.timestamp += TIMESTAMP_INC;\n\t\tif (connectionData.sequence >= 2 ** 16) connectionData.sequence = 0;\n\t\tif (connectionData.timestamp >= 2 ** 32) connectionData.timestamp = 0;\n\t\tthis.setSpeaking(true);\n\t\tstate.udp.send(audioPacket);\n\t}\n\n\t/**\n\t * Sends a packet to the voice gateway indicating that the client has start/stopped sending\n\t * audio.\n\t *\n\t * @param speaking - Whether or not the client should be shown as speaking\n\t */\n\tpublic setSpeaking(speaking: boolean) {\n\t\tconst state = this.state;\n\t\tif (state.code !== NetworkingStatusCode.Ready) return;\n\t\tif (state.connectionData.speaking === speaking) return;\n\t\tstate.connectionData.speaking = speaking;\n\t\tstate.ws.sendPacket({\n\t\t\top: VoiceOpcodes.Speaking,\n\t\t\td: {\n\t\t\t\tspeaking: speaking ? 1 : 0,\n\t\t\t\tdelay: 0,\n\t\t\t\tssrc: state.connectionData.ssrc,\n\t\t\t},\n\t\t});\n\t}\n\n\t/**\n\t * Creates a new audio packet from an Opus packet. This involves encrypting the packet,\n\t * then prepending a header that includes metadata.\n\t *\n\t * @param opusPacket - The Opus packet to prepare\n\t * @param connectionData - The current connection data of the instance\n\t */\n\tprivate createAudioPacket(opusPacket: Buffer, connectionData: ConnectionData) {\n\t\tconst packetBuffer = Buffer.alloc(12);\n\t\tpacketBuffer[0] = 0x80;\n\t\tpacketBuffer[1] = 0x78;\n\n\t\tconst { sequence, timestamp, ssrc } = connectionData;\n\n\t\tpacketBuffer.writeUIntBE(sequence, 2, 2);\n\t\tpacketBuffer.writeUIntBE(timestamp, 4, 4);\n\t\tpacketBuffer.writeUIntBE(ssrc, 8, 4);\n\n\t\tpacketBuffer.copy(nonce, 0, 0, 12);\n\t\treturn Buffer.concat([packetBuffer, ...this.encryptOpusPacket(opusPacket, connectionData)]);\n\t}\n\n\t/**\n\t * Encrypts an Opus packet using the format agreed upon by the instance and Discord.\n\t *\n\t * @param opusPacket - The Opus packet to encrypt\n\t * @param connectionData - The current connection data of the instance\n\t */\n\tprivate encryptOpusPacket(opusPacket: Buffer, connectionData: ConnectionData) {\n\t\tconst { secretKey, encryptionMode } = connectionData;\n\n\t\tif (encryptionMode === 'xsalsa20_poly1305_lite') {\n\t\t\tconnectionData.nonce++;\n\t\t\tif (connectionData.nonce > MAX_NONCE_SIZE) connectionData.nonce = 0;\n\t\t\tconnectionData.nonceBuffer.writeUInt32BE(connectionData.nonce, 0);\n\t\t\treturn [\n\t\t\t\tsecretbox.methods.close(opusPacket, connectionData.nonceBuffer, secretKey),\n\t\t\t\tconnectionData.nonceBuffer.slice(0, 4),\n\t\t\t];\n\t\t} else if (encryptionMode === 'xsalsa20_poly1305_suffix') {\n\t\t\tconst random = secretbox.methods.random(24, connectionData.nonceBuffer);\n\t\t\treturn [secretbox.methods.close(opusPacket, random, secretKey), random];\n\t\t}\n\n\t\treturn [secretbox.methods.close(opusPacket, nonce, secretKey)];\n\t}\n}\n", "import { Buffer } from 'node:buffer';\n\ninterface Methods {\n\tclose(opusPacket: <PERSON><PERSON><PERSON>, nonce: <PERSON><PERSON><PERSON>, secretKey: Uint8Array): Buffer;\n\topen(buffer: <PERSON><PERSON>er, nonce: Buffer, secretKey: Uint8Array): Buffer | null;\n\trandom(bytes: number, nonce: Buffer): Buffer;\n}\n\nconst libs = {\n\t'sodium-native': (sodium: any): Methods => ({\n\t\topen: (buffer: Buffer, nonce: Buffer, secretKey: Uint8Array) => {\n\t\t\tif (buffer) {\n\t\t\t\tconst output = Buffer.allocUnsafe(buffer.length - sodium.crypto_box_MACBYTES);\n\t\t\t\tif (sodium.crypto_secretbox_open_easy(output, buffer, nonce, secretKey)) return output;\n\t\t\t}\n\n\t\t\treturn null;\n\t\t},\n\t\tclose: (opusPacket: Buffer, nonce: Buffer, secretKey: Uint8Array) => {\n\t\t\tconst output = Buffer.allocUnsafe(opusPacket.length + sodium.crypto_box_MACBYTES);\n\t\t\tsodium.crypto_secretbox_easy(output, opusPacket, nonce, secretKey);\n\t\t\treturn output;\n\t\t},\n\t\trandom: (num: number, buffer: Buffer = Buffer.allocUnsafe(num)) => {\n\t\t\tsodium.randombytes_buf(buffer);\n\t\t\treturn buffer;\n\t\t},\n\t}),\n\tsodium: (sodium: any): Methods => ({\n\t\topen: sodium.api.crypto_secretbox_open_easy,\n\t\tclose: sodium.api.crypto_secretbox_easy,\n\t\trandom: (num: number, buffer: Buffer = Buffer.allocUnsafe(num)) => {\n\t\t\tsodium.api.randombytes_buf(buffer);\n\t\t\treturn buffer;\n\t\t},\n\t}),\n\t'libsodium-wrappers': (sodium: any): Methods => ({\n\t\topen: sodium.crypto_secretbox_open_easy,\n\t\tclose: sodium.crypto_secretbox_easy,\n\t\trandom: sodium.randombytes_buf,\n\t}),\n\ttweetnacl: (tweetnacl: any): Methods => ({\n\t\topen: tweetnacl.secretbox.open,\n\t\tclose: tweetnacl.secretbox,\n\t\trandom: tweetnacl.randomBytes,\n\t}),\n} as const;\n\nconst fallbackError = () => {\n\tthrow new Error(\n\t\t`Cannot play audio as no valid encryption package is installed.\n- Install sodium, libsodium-wrappers, or tweetnacl.\n- Use the generateDependencyReport() function for more information.\\n`,\n\t);\n};\n\nconst methods: Methods = {\n\topen: fallbackError,\n\tclose: fallbackError,\n\trandom: fallbackError,\n};\n\nvoid (async () => {\n\tfor (const libName of Object.keys(libs) as (keyof typeof libs)[]) {\n\t\ttry {\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-require-imports, @typescript-eslint/no-var-requires\n\t\t\tconst lib = require(libName);\n\t\t\tif (libName === 'libsodium-wrappers' && lib.ready) await lib.ready;\n\t\t\tObject.assign(methods, libs[libName](lib));\n\t\t\tbreak;\n\t\t} catch {}\n\t}\n})();\n\nexport { methods };\n", "export const noop = () => {};\n", "import { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport { createSocket, type Socket } from 'node:dgram';\nimport { EventEmitter } from 'node:events';\nimport { isIPv4 } from 'node:net';\n\n/**\n * Stores an IP address and port. Used to store socket details for the local client as well as\n * for Discord.\n */\nexport interface SocketConfig {\n\tip: string;\n\tport: number;\n}\n\n/**\n * Parses the response from Discord to aid with local IP discovery.\n *\n * @param message - The received message\n */\nexport function parseLocalPacket(message: Buffer): SocketConfig {\n\tconst packet = Buffer.from(message);\n\n\tconst ip = packet.slice(8, packet.indexOf(0, 8)).toString('utf8');\n\n\tif (!isIPv4(ip)) {\n\t\tthrow new Error('Malformed IP address');\n\t}\n\n\tconst port = packet.readUInt16BE(packet.length - 2);\n\n\treturn { ip, port };\n}\n\n/**\n * The interval in milliseconds at which keep alive datagrams are sent.\n */\nconst KEEP_ALIVE_INTERVAL = 5e3;\n\n/**\n * The maximum value of the keep alive counter.\n */\nconst MAX_COUNTER_VALUE = 2 ** 32 - 1;\n\nexport interface VoiceUDPSocket extends EventEmitter {\n\ton(event: 'error', listener: (error: Error) => void): this;\n\ton(event: 'close', listener: () => void): this;\n\ton(event: 'debug', listener: (message: string) => void): this;\n\ton(event: 'message', listener: (message: Buffer) => void): this;\n}\n\n/**\n * Manages the UDP networking for a voice connection.\n */\nexport class VoiceUDPSocket extends EventEmitter {\n\t/**\n\t * The underlying network Socket for the VoiceUDPSocket.\n\t */\n\tprivate readonly socket: Socket;\n\n\t/**\n\t * The socket details for Discord (remote)\n\t */\n\tprivate readonly remote: SocketConfig;\n\n\t/**\n\t * The counter used in the keep alive mechanism.\n\t */\n\tprivate keepAliveCounter = 0;\n\n\t/**\n\t * The buffer used to write the keep alive counter into.\n\t */\n\tprivate readonly keepAliveBuffer: Buffer;\n\n\t/**\n\t * The Node.js interval for the keep-alive mechanism.\n\t */\n\tprivate readonly keepAliveInterval: NodeJS.Timeout;\n\n\t/**\n\t * The time taken to receive a response to keep alive messages.\n\t *\n\t * @deprecated This field is no longer updated as keep alive messages are no longer tracked.\n\t */\n\tpublic ping?: number;\n\n\t/**\n\t * Creates a new VoiceUDPSocket.\n\t *\n\t * @param remote - Details of the remote socket\n\t */\n\tpublic constructor(remote: SocketConfig) {\n\t\tsuper();\n\t\tthis.socket = createSocket('udp4');\n\t\tthis.socket.on('error', (error: Error) => this.emit('error', error));\n\t\tthis.socket.on('message', (buffer: Buffer) => this.onMessage(buffer));\n\t\tthis.socket.on('close', () => this.emit('close'));\n\t\tthis.remote = remote;\n\t\tthis.keepAliveBuffer = Buffer.alloc(8);\n\t\tthis.keepAliveInterval = setInterval(() => this.keepAlive(), KEEP_ALIVE_INTERVAL);\n\t\tsetImmediate(() => this.keepAlive());\n\t}\n\n\t/**\n\t * Called when a message is received on the UDP socket.\n\t *\n\t * @param buffer - The received buffer\n\t */\n\tprivate onMessage(buffer: Buffer): void {\n\t\t// Propagate the message\n\t\tthis.emit('message', buffer);\n\t}\n\n\t/**\n\t * Called at a regular interval to check whether we are still able to send datagrams to Discord.\n\t */\n\tprivate keepAlive() {\n\t\tthis.keepAliveBuffer.writeUInt32LE(this.keepAliveCounter, 0);\n\t\tthis.send(this.keepAliveBuffer);\n\t\tthis.keepAliveCounter++;\n\t\tif (this.keepAliveCounter > MAX_COUNTER_VALUE) {\n\t\t\tthis.keepAliveCounter = 0;\n\t\t}\n\t}\n\n\t/**\n\t * Sends a buffer to Discord.\n\t *\n\t * @param buffer - The buffer to send\n\t */\n\tpublic send(buffer: Buffer) {\n\t\tthis.socket.send(buffer, this.remote.port, this.remote.ip);\n\t}\n\n\t/**\n\t * Closes the socket, the instance will not be able to be reused.\n\t */\n\tpublic destroy() {\n\t\ttry {\n\t\t\tthis.socket.close();\n\t\t} catch {}\n\n\t\tclearInterval(this.keepAliveInterval);\n\t}\n\n\t/**\n\t * Performs IP discovery to discover the local address and port to be used for the voice connection.\n\t *\n\t * @param ssrc - The SSRC received from Discord\n\t */\n\tpublic async performIPDiscovery(ssrc: number): Promise<SocketConfig> {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tconst listener = (message: Buffer) => {\n\t\t\t\ttry {\n\t\t\t\t\tif (message.readUInt16BE(0) !== 2) return;\n\t\t\t\t\tconst packet = parseLocalPacket(message);\n\t\t\t\t\tthis.socket.off('message', listener);\n\t\t\t\t\tresolve(packet);\n\t\t\t\t} catch {}\n\t\t\t};\n\n\t\t\tthis.socket.on('message', listener);\n\t\t\tthis.socket.once('close', () => reject(new Error('Cannot perform IP discovery - socket closed')));\n\n\t\t\tconst discoveryBuffer = Buffer.alloc(74);\n\n\t\t\tdiscoveryBuffer.writeUInt16BE(1, 0);\n\t\t\tdiscoveryBuffer.writeUInt16BE(70, 2);\n\t\t\tdiscoveryBuffer.writeUInt32BE(ssrc, 4);\n\t\t\tthis.send(discoveryBuffer);\n\t\t});\n\t}\n}\n", "import { EventEmitter } from 'node:events';\nimport { VoiceOpcodes } from 'discord-api-types/voice/v4';\nimport WebSocket, { type MessageEvent } from 'ws';\n\nexport interface VoiceWebSocket extends EventEmitter {\n\ton(event: 'error', listener: (error: Error) => void): this;\n\ton(event: 'open', listener: (event: WebSocket.Event) => void): this;\n\ton(event: 'close', listener: (event: WebSocket.CloseEvent) => void): this;\n\t/**\n\t * Debug event for VoiceWebSocket.\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'debug', listener: (message: string) => void): this;\n\t/**\n\t * Packet event.\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'packet', listener: (packet: any) => void): this;\n}\n\n/**\n * An extension of the WebSocket class to provide helper functionality when interacting\n * with the Discord Voice gateway.\n */\nexport class VoiceWebSocket extends EventEmitter {\n\t/**\n\t * The current heartbeat interval, if any.\n\t */\n\tprivate heartbeatInterval?: NodeJS.Timeout;\n\n\t/**\n\t * The time (milliseconds since UNIX epoch) that the last heartbeat acknowledgement packet was received.\n\t * This is set to 0 if an acknowledgement packet hasn't been received yet.\n\t */\n\tprivate lastHeartbeatAck: number;\n\n\t/**\n\t * The time (milliseconds since UNIX epoch) that the last heartbeat was sent. This is set to 0 if a heartbeat\n\t * hasn't been sent yet.\n\t */\n\tprivate lastHeartbeatSend: number;\n\n\t/**\n\t * The number of consecutively missed heartbeats.\n\t */\n\tprivate missedHeartbeats = 0;\n\n\t/**\n\t * The last recorded ping.\n\t */\n\tpublic ping?: number;\n\n\t/**\n\t * The debug logger function, if debugging is enabled.\n\t */\n\tprivate readonly debug: ((message: string) => void) | null;\n\n\t/**\n\t * The underlying WebSocket of this wrapper.\n\t */\n\tprivate readonly ws: WebSocket;\n\n\t/**\n\t * Creates a new VoiceWebSocket.\n\t *\n\t * @param address - The address to connect to\n\t */\n\tpublic constructor(address: string, debug: boolean) {\n\t\tsuper();\n\t\tthis.ws = new WebSocket(address);\n\t\tthis.ws.onmessage = (err) => this.onMessage(err);\n\t\tthis.ws.onopen = (err) => this.emit('open', err);\n\t\tthis.ws.onerror = (err: Error | WebSocket.ErrorEvent) => this.emit('error', err instanceof Error ? err : err.error);\n\t\tthis.ws.onclose = (err) => this.emit('close', err);\n\n\t\tthis.lastHeartbeatAck = 0;\n\t\tthis.lastHeartbeatSend = 0;\n\n\t\tthis.debug = debug ? (message: string) => this.emit('debug', message) : null;\n\t}\n\n\t/**\n\t * Destroys the VoiceWebSocket. The heartbeat interval is cleared, and the connection is closed.\n\t */\n\tpublic destroy() {\n\t\ttry {\n\t\t\tthis.debug?.('destroyed');\n\t\t\tthis.setHeartbeatInterval(-1);\n\t\t\tthis.ws.close(1_000);\n\t\t} catch (error) {\n\t\t\tconst err = error as Error;\n\t\t\tthis.emit('error', err);\n\t\t}\n\t}\n\n\t/**\n\t * Handles message events on the WebSocket. Attempts to JSON parse the messages and emit them\n\t * as packets.\n\t *\n\t * @param event - The message event\n\t */\n\tpublic onMessage(event: MessageEvent) {\n\t\tif (typeof event.data !== 'string') return;\n\n\t\tthis.debug?.(`<< ${event.data}`);\n\n\t\tlet packet: any;\n\t\ttry {\n\t\t\tpacket = JSON.parse(event.data);\n\t\t} catch (error) {\n\t\t\tconst err = error as Error;\n\t\t\tthis.emit('error', err);\n\t\t\treturn;\n\t\t}\n\n\t\tif (packet.op === VoiceOpcodes.HeartbeatAck) {\n\t\t\tthis.lastHeartbeatAck = Date.now();\n\t\t\tthis.missedHeartbeats = 0;\n\t\t\tthis.ping = this.lastHeartbeatAck - this.lastHeartbeatSend;\n\t\t}\n\n\t\tthis.emit('packet', packet);\n\t}\n\n\t/**\n\t * Sends a JSON-stringifiable packet over the WebSocket.\n\t *\n\t * @param packet - The packet to send\n\t */\n\tpublic sendPacket(packet: any) {\n\t\ttry {\n\t\t\tconst stringified = JSON.stringify(packet);\n\t\t\tthis.debug?.(`>> ${stringified}`);\n\t\t\tthis.ws.send(stringified);\n\t\t} catch (error) {\n\t\t\tconst err = error as Error;\n\t\t\tthis.emit('error', err);\n\t\t}\n\t}\n\n\t/**\n\t * Sends a heartbeat over the WebSocket.\n\t */\n\tprivate sendHeartbeat() {\n\t\tthis.lastHeartbeatSend = Date.now();\n\t\tthis.missedHeartbeats++;\n\t\tconst nonce = this.lastHeartbeatSend;\n\t\tthis.sendPacket({\n\t\t\top: VoiceOpcodes.Heartbeat,\n\t\t\t// eslint-disable-next-line id-length\n\t\t\td: nonce,\n\t\t});\n\t}\n\n\t/**\n\t * Sets/clears an interval to send heartbeats over the WebSocket.\n\t *\n\t * @param ms - The interval in milliseconds. If negative, the interval will be unset\n\t */\n\tpublic setHeartbeatInterval(ms: number) {\n\t\tif (this.heartbeatInterval !== undefined) clearInterval(this.heartbeatInterval);\n\t\tif (ms > 0) {\n\t\t\tthis.heartbeatInterval = setInterval(() => {\n\t\t\t\tif (this.lastHeartbeatSend !== 0 && this.missedHeartbeats >= 3) {\n\t\t\t\t\t// Missed too many heartbeats - disconnect\n\t\t\t\t\tthis.ws.close();\n\t\t\t\t\tthis.setHeartbeatInterval(-1);\n\t\t\t\t}\n\n\t\t\t\tthis.sendHeartbeat();\n\t\t\t}, ms);\n\t\t}\n\t}\n}\n", "/* eslint-disable jsdoc/check-param-names */\n\nimport { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport { VoiceOpcodes } from 'discord-api-types/voice/v4';\nimport type { VoiceConnection } from '../VoiceConnection';\nimport type { ConnectionData } from '../networking/Networking';\nimport { methods } from '../util/Secretbox';\nimport {\n\tAudioReceiveStream,\n\tcreateDefaultAudioReceiveStreamOptions,\n\ttype AudioReceiveStreamOptions,\n} from './AudioReceiveStream';\nimport { SSRCMap } from './SSRCMap';\nimport { SpeakingMap } from './SpeakingMap';\n\n/**\n * Attaches to a VoiceConnection, allowing you to receive audio packets from other\n * users that are speaking.\n *\n * @beta\n */\nexport class VoiceReceiver {\n\t/**\n\t * The attached connection of this receiver.\n\t */\n\tpublic readonly voiceConnection;\n\n\t/**\n\t * Maps SSRCs to Discord user ids.\n\t */\n\tpublic readonly ssrcMap: SSRCMap;\n\n\t/**\n\t * The current audio subscriptions of this receiver.\n\t */\n\tpublic readonly subscriptions: Map<string, AudioReceiveStream>;\n\n\t/**\n\t * The connection data of the receiver.\n\t *\n\t * @internal\n\t */\n\tpublic connectionData: Partial<ConnectionData>;\n\n\t/**\n\t * The speaking map of the receiver.\n\t */\n\tpublic readonly speaking: SpeakingMap;\n\n\tpublic constructor(voiceConnection: VoiceConnection) {\n\t\tthis.voiceConnection = voiceConnection;\n\t\tthis.ssrcMap = new SSRCMap();\n\t\tthis.speaking = new SpeakingMap();\n\t\tthis.subscriptions = new Map();\n\t\tthis.connectionData = {};\n\n\t\tthis.onWsPacket = this.onWsPacket.bind(this);\n\t\tthis.onUdpMessage = this.onUdpMessage.bind(this);\n\t}\n\n\t/**\n\t * Called when a packet is received on the attached connection's WebSocket.\n\t *\n\t * @param packet - The received packet\n\t * @internal\n\t */\n\tpublic onWsPacket(packet: any) {\n\t\tif (packet.op === VoiceOpcodes.ClientDisconnect && typeof packet.d?.user_id === 'string') {\n\t\t\tthis.ssrcMap.delete(packet.d.user_id);\n\t\t} else if (\n\t\t\tpacket.op === VoiceOpcodes.Speaking &&\n\t\t\ttypeof packet.d?.user_id === 'string' &&\n\t\t\ttypeof packet.d?.ssrc === 'number'\n\t\t) {\n\t\t\tthis.ssrcMap.update({ userId: packet.d.user_id, audioSSRC: packet.d.ssrc });\n\t\t} else if (\n\t\t\tpacket.op === VoiceOpcodes.ClientConnect &&\n\t\t\ttypeof packet.d?.user_id === 'string' &&\n\t\t\ttypeof packet.d?.audio_ssrc === 'number'\n\t\t) {\n\t\t\tthis.ssrcMap.update({\n\t\t\t\tuserId: packet.d.user_id,\n\t\t\t\taudioSSRC: packet.d.audio_ssrc,\n\t\t\t\tvideoSSRC: packet.d.video_ssrc === 0 ? undefined : packet.d.video_ssrc,\n\t\t\t});\n\t\t}\n\t}\n\n\tprivate decrypt(buffer: Buffer, mode: string, nonce: Buffer, secretKey: Uint8Array) {\n\t\t// Choose correct nonce depending on encryption\n\t\tlet end;\n\t\tif (mode === 'xsalsa20_poly1305_lite') {\n\t\t\tbuffer.copy(nonce, 0, buffer.length - 4);\n\t\t\tend = buffer.length - 4;\n\t\t} else if (mode === 'xsalsa20_poly1305_suffix') {\n\t\t\tbuffer.copy(nonce, 0, buffer.length - 24);\n\t\t\tend = buffer.length - 24;\n\t\t} else {\n\t\t\tbuffer.copy(nonce, 0, 0, 12);\n\t\t}\n\n\t\t// Open packet\n\t\tconst decrypted = methods.open(buffer.slice(12, end), nonce, secretKey);\n\t\tif (!decrypted) return;\n\t\treturn Buffer.from(decrypted);\n\t}\n\n\t/**\n\t * Parses an audio packet, decrypting it to yield an Opus packet.\n\t *\n\t * @param buffer - The buffer to parse\n\t * @param mode - The encryption mode\n\t * @param nonce - The nonce buffer used by the connection for encryption\n\t * @param secretKey - The secret key used by the connection for encryption\n\t * @returns The parsed Opus packet\n\t */\n\tprivate parsePacket(buffer: Buffer, mode: string, nonce: Buffer, secretKey: Uint8Array) {\n\t\tlet packet = this.decrypt(buffer, mode, nonce, secretKey);\n\t\tif (!packet) return;\n\n\t\t// Strip RTP Header Extensions (one-byte only)\n\t\tif (packet[0] === 0xbe && packet[1] === 0xde) {\n\t\t\tconst headerExtensionLength = packet.readUInt16BE(2);\n\t\t\tpacket = packet.subarray(4 + 4 * headerExtensionLength);\n\t\t}\n\n\t\treturn packet;\n\t}\n\n\t/**\n\t * Called when the UDP socket of the attached connection receives a message.\n\t *\n\t * @param msg - The received message\n\t * @internal\n\t */\n\tpublic onUdpMessage(msg: Buffer) {\n\t\tif (msg.length <= 8) return;\n\t\tconst ssrc = msg.readUInt32BE(8);\n\n\t\tconst userData = this.ssrcMap.get(ssrc);\n\t\tif (!userData) return;\n\n\t\tthis.speaking.onPacket(userData.userId);\n\n\t\tconst stream = this.subscriptions.get(userData.userId);\n\t\tif (!stream) return;\n\n\t\tif (this.connectionData.encryptionMode && this.connectionData.nonceBuffer && this.connectionData.secretKey) {\n\t\t\tconst packet = this.parsePacket(\n\t\t\t\tmsg,\n\t\t\t\tthis.connectionData.encryptionMode,\n\t\t\t\tthis.connectionData.nonceBuffer,\n\t\t\t\tthis.connectionData.secretKey,\n\t\t\t);\n\t\t\tif (packet) {\n\t\t\t\tstream.push(packet);\n\t\t\t} else {\n\t\t\t\tstream.destroy(new Error('Failed to parse packet'));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Creates a subscription for the given user id.\n\t *\n\t * @param target - The id of the user to subscribe to\n\t * @returns A readable stream of Opus packets received from the target\n\t */\n\tpublic subscribe(userId: string, options?: Partial<AudioReceiveStreamOptions>) {\n\t\tconst existing = this.subscriptions.get(userId);\n\t\tif (existing) return existing;\n\n\t\tconst stream = new AudioReceiveStream({\n\t\t\t...createDefaultAudioReceiveStreamOptions(),\n\t\t\t...options,\n\t\t});\n\n\t\tstream.once('close', () => this.subscriptions.delete(userId));\n\t\tthis.subscriptions.set(userId, stream);\n\t\treturn stream;\n\t}\n}\n", "import type { Buffer } from 'node:buffer';\nimport { Readable, type ReadableOptions } from 'node:stream';\nimport { SILENCE_FRAME } from '../audio/AudioPlayer';\n\n/**\n * The different behaviors an audio receive stream can have for deciding when to end.\n */\nexport enum EndBehaviorType {\n\t/**\n\t * The stream will only end when manually destroyed.\n\t */\n\tManual,\n\n\t/**\n\t * The stream will end after a given time period of silence/no audio packets.\n\t */\n\tAfterSilence,\n\n\t/**\n\t * The stream will end after a given time period of no audio packets.\n\t */\n\tAfterInactivity,\n}\n\nexport type EndBehavior =\n\t| {\n\t\t\tbehavior: EndBehaviorType.AfterInactivity | EndBehaviorType.AfterSilence;\n\t\t\tduration: number;\n\t  }\n\t| {\n\t\t\tbehavior: EndBehaviorType.Manual;\n\t  };\n\nexport interface AudioReceiveStreamOptions extends ReadableOptions {\n\tend: EndBehavior;\n}\n\nexport function createDefaultAudioReceiveStreamOptions(): AudioReceiveStreamOptions {\n\treturn {\n\t\tend: {\n\t\t\tbehavior: EndBehaviorType.Manual,\n\t\t},\n\t};\n}\n\n/**\n * A readable stream of Opus packets received from a specific entity\n * in a Discord voice connection.\n */\nexport class AudioReceiveStream extends Readable {\n\t/**\n\t * The end behavior of the receive stream.\n\t */\n\tpublic readonly end: EndBehavior;\n\n\tprivate endTimeout?: NodeJS.Timeout;\n\n\tpublic constructor({ end, ...options }: AudioReceiveStreamOptions) {\n\t\tsuper({\n\t\t\t...options,\n\t\t\tobjectMode: true,\n\t\t});\n\n\t\tthis.end = end;\n\t}\n\n\tpublic override push(buffer: Buffer | null) {\n\t\tif (\n\t\t\tbuffer &&\n\t\t\t(this.end.behavior === EndBehaviorType.AfterInactivity ||\n\t\t\t\t(this.end.behavior === EndBehaviorType.AfterSilence &&\n\t\t\t\t\t(buffer.compare(SILENCE_FRAME) !== 0 || this.endTimeout === undefined)))\n\t\t) {\n\t\t\tthis.renewEndTimeout(this.end);\n\t\t}\n\n\t\treturn super.push(buffer);\n\t}\n\n\tprivate renewEndTimeout(end: EndBehavior & { duration: number }) {\n\t\tif (this.endTimeout) {\n\t\t\tclearTimeout(this.endTimeout);\n\t\t}\n\n\t\tthis.endTimeout = setTimeout(() => this.push(null), end.duration);\n\t}\n\n\tpublic override _read() {}\n}\n", "/* eslint-disable @typescript-eslint/prefer-ts-expect-error, @typescript-eslint/method-signature-style */\nimport { Buffer } from 'node:buffer';\nimport { EventEmitter } from 'node:events';\nimport { addAudioPlayer, deleteAudioPlayer } from '../DataStore';\nimport { VoiceConnectionStatus, type VoiceConnection } from '../VoiceConnection';\nimport { noop } from '../util/util';\nimport { AudioPlayerError } from './AudioPlayerError';\nimport type { AudioResource } from './AudioResource';\nimport { PlayerSubscription } from './PlayerSubscription';\n\n// The Opus \"silent\" frame\nexport const SILENCE_FRAME = Buffer.from([0xf8, 0xff, 0xfe]);\n\n/**\n * Describes the behavior of the player when an audio packet is played but there are no available\n * voice connections to play to.\n */\nexport enum NoSubscriberBehavior {\n\t/**\n\t * Pauses playing the stream until a voice connection becomes available.\n\t */\n\tPause = 'pause',\n\n\t/**\n\t * Continues to play through the resource regardless.\n\t */\n\tPlay = 'play',\n\n\t/**\n\t * The player stops and enters the Idle state.\n\t */\n\tStop = 'stop',\n}\n\nexport enum AudioPlayerStatus {\n\t/**\n\t * When the player has paused itself. Only possible with the \"pause\" no subscriber behavior.\n\t */\n\tAutoPaused = 'autopaused',\n\n\t/**\n\t * When the player is waiting for an audio resource to become readable before transitioning to Playing.\n\t */\n\tBuffering = 'buffering',\n\n\t/**\n\t * When there is currently no resource for the player to be playing.\n\t */\n\tIdle = 'idle',\n\n\t/**\n\t * When the player has been manually paused.\n\t */\n\tPaused = 'paused',\n\n\t/**\n\t * When the player is actively playing an audio resource.\n\t */\n\tPlaying = 'playing',\n}\n\n/**\n * Options that can be passed when creating an audio player, used to specify its behavior.\n */\nexport interface CreateAudioPlayerOptions {\n\tbehaviors?: {\n\t\tmaxMissedFrames?: number;\n\t\tnoSubscriber?: NoSubscriberBehavior;\n\t};\n\tdebug?: boolean;\n}\n\n/**\n * The state that an AudioPlayer is in when it has no resource to play. This is the starting state.\n */\nexport interface AudioPlayerIdleState {\n\tstatus: AudioPlayerStatus.Idle;\n}\n\n/**\n * The state that an AudioPlayer is in when it is waiting for a resource to become readable. Once this\n * happens, the AudioPlayer will enter the Playing state. If the resource ends/errors before this, then\n * it will re-enter the Idle state.\n */\nexport interface AudioPlayerBufferingState {\n\tonFailureCallback: () => void;\n\tonReadableCallback: () => void;\n\tonStreamError: (error: Error) => void;\n\t/**\n\t * The resource that the AudioPlayer is waiting for\n\t */\n\tresource: AudioResource;\n\tstatus: AudioPlayerStatus.Buffering;\n}\n\n/**\n * The state that an AudioPlayer is in when it is actively playing an AudioResource. When playback ends,\n * it will enter the Idle state.\n */\nexport interface AudioPlayerPlayingState {\n\t/**\n\t * The number of consecutive times that the audio resource has been unable to provide an Opus frame.\n\t */\n\tmissedFrames: number;\n\tonStreamError: (error: Error) => void;\n\n\t/**\n\t * The playback duration in milliseconds of the current audio resource. This includes filler silence packets\n\t * that have been played when the resource was buffering.\n\t */\n\tplaybackDuration: number;\n\n\t/**\n\t * The resource that is being played.\n\t */\n\tresource: AudioResource;\n\n\tstatus: AudioPlayerStatus.Playing;\n}\n\n/**\n * The state that an AudioPlayer is in when it has either been explicitly paused by the user, or done\n * automatically by the AudioPlayer itself if there are no available subscribers.\n */\nexport interface AudioPlayerPausedState {\n\tonStreamError: (error: Error) => void;\n\t/**\n\t * The playback duration in milliseconds of the current audio resource. This includes filler silence packets\n\t * that have been played when the resource was buffering.\n\t */\n\tplaybackDuration: number;\n\n\t/**\n\t * The current resource of the audio player.\n\t */\n\tresource: AudioResource;\n\n\t/**\n\t * How many silence packets still need to be played to avoid audio interpolation due to the stream suddenly pausing.\n\t */\n\tsilencePacketsRemaining: number;\n\n\tstatus: AudioPlayerStatus.AutoPaused | AudioPlayerStatus.Paused;\n}\n\n/**\n * The various states that the player can be in.\n */\nexport type AudioPlayerState =\n\t| AudioPlayerBufferingState\n\t| AudioPlayerIdleState\n\t| AudioPlayerPausedState\n\t| AudioPlayerPlayingState;\n\nexport interface AudioPlayer extends EventEmitter {\n\t/**\n\t * Emitted when there is an error emitted from the audio resource played by the audio player\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'error', listener: (error: AudioPlayerError) => void): this;\n\t/**\n\t * Emitted debugging information about the audio player\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'debug', listener: (message: string) => void): this;\n\t/**\n\t * Emitted when the state of the audio player changes\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'stateChange', listener: (oldState: AudioPlayerState, newState: AudioPlayerState) => void): this;\n\t/**\n\t * Emitted when the audio player is subscribed to a voice connection\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'subscribe' | 'unsubscribe', listener: (subscription: PlayerSubscription) => void): this;\n\t/**\n\t * Emitted when the status of state changes to a specific status\n\t *\n\t * @eventProperty\n\t */\n\ton<Event extends AudioPlayerStatus>(\n\t\tevent: Event,\n\t\tlistener: (oldState: AudioPlayerState, newState: AudioPlayerState & { status: Event }) => void,\n\t): this;\n}\n\n/**\n * Stringifies an AudioPlayerState instance.\n *\n * @param state - The state to stringify\n */\nfunction stringifyState(state: AudioPlayerState) {\n\treturn JSON.stringify({\n\t\t...state,\n\t\tresource: Reflect.has(state, 'resource'),\n\t\tstepTimeout: Reflect.has(state, 'stepTimeout'),\n\t});\n}\n\n/**\n * Used to play audio resources (i.e. tracks, streams) to voice connections.\n *\n * @remarks\n * Audio players are designed to be re-used - even if a resource has finished playing, the player itself\n * can still be used.\n *\n * The AudioPlayer drives the timing of playback, and therefore is unaffected by voice connections\n * becoming unavailable. Its behavior in these scenarios can be configured.\n */\nexport class AudioPlayer extends EventEmitter {\n\t/**\n\t * The state that the AudioPlayer is in.\n\t */\n\tprivate _state: AudioPlayerState;\n\n\t/**\n\t * A list of VoiceConnections that are registered to this AudioPlayer. The player will attempt to play audio\n\t * to the streams in this list.\n\t */\n\tprivate readonly subscribers: PlayerSubscription[] = [];\n\n\t/**\n\t * The behavior that the player should follow when it enters certain situations.\n\t */\n\tprivate readonly behaviors: {\n\t\tmaxMissedFrames: number;\n\t\tnoSubscriber: NoSubscriberBehavior;\n\t};\n\n\t/**\n\t * The debug logger function, if debugging is enabled.\n\t */\n\tprivate readonly debug: ((message: string) => void) | null;\n\n\t/**\n\t * Creates a new AudioPlayer.\n\t */\n\tpublic constructor(options: CreateAudioPlayerOptions = {}) {\n\t\tsuper();\n\t\tthis._state = { status: AudioPlayerStatus.Idle };\n\t\tthis.behaviors = {\n\t\t\tnoSubscriber: NoSubscriberBehavior.Pause,\n\t\t\tmaxMissedFrames: 5,\n\t\t\t...options.behaviors,\n\t\t};\n\t\tthis.debug = options.debug === false ? null : (message: string) => this.emit('debug', message);\n\t}\n\n\t/**\n\t * A list of subscribed voice connections that can currently receive audio to play.\n\t */\n\tpublic get playable() {\n\t\treturn this.subscribers\n\t\t\t.filter(({ connection }) => connection.state.status === VoiceConnectionStatus.Ready)\n\t\t\t.map(({ connection }) => connection);\n\t}\n\n\t/**\n\t * Subscribes a VoiceConnection to the audio player's play list. If the VoiceConnection is already subscribed,\n\t * then the existing subscription is used.\n\t *\n\t * @remarks\n\t * This method should not be directly called. Instead, use VoiceConnection#subscribe.\n\t * @param connection - The connection to subscribe\n\t * @returns The new subscription if the voice connection is not yet subscribed, otherwise the existing subscription\n\t */\n\t// @ts-ignore\n\tprivate subscribe(connection: VoiceConnection) {\n\t\tconst existingSubscription = this.subscribers.find((subscription) => subscription.connection === connection);\n\t\tif (!existingSubscription) {\n\t\t\tconst subscription = new PlayerSubscription(connection, this);\n\t\t\tthis.subscribers.push(subscription);\n\t\t\tsetImmediate(() => this.emit('subscribe', subscription));\n\t\t\treturn subscription;\n\t\t}\n\n\t\treturn existingSubscription;\n\t}\n\n\t/**\n\t * Unsubscribes a subscription - i.e. removes a voice connection from the play list of the audio player.\n\t *\n\t * @remarks\n\t * This method should not be directly called. Instead, use PlayerSubscription#unsubscribe.\n\t * @param subscription - The subscription to remove\n\t * @returns Whether or not the subscription existed on the player and was removed\n\t */\n\t// @ts-ignore\n\tprivate unsubscribe(subscription: PlayerSubscription) {\n\t\tconst index = this.subscribers.indexOf(subscription);\n\t\tconst exists = index !== -1;\n\t\tif (exists) {\n\t\t\tthis.subscribers.splice(index, 1);\n\t\t\tsubscription.connection.setSpeaking(false);\n\t\t\tthis.emit('unsubscribe', subscription);\n\t\t}\n\n\t\treturn exists;\n\t}\n\n\t/**\n\t * The state that the player is in.\n\t */\n\tpublic get state() {\n\t\treturn this._state;\n\t}\n\n\t/**\n\t * Sets a new state for the player, performing clean-up operations where necessary.\n\t */\n\tpublic set state(newState: AudioPlayerState) {\n\t\tconst oldState = this._state;\n\t\tconst newResource = Reflect.get(newState, 'resource') as AudioResource | undefined;\n\n\t\tif (oldState.status !== AudioPlayerStatus.Idle && oldState.resource !== newResource) {\n\t\t\toldState.resource.playStream.on('error', noop);\n\t\t\toldState.resource.playStream.off('error', oldState.onStreamError);\n\t\t\toldState.resource.audioPlayer = undefined;\n\t\t\toldState.resource.playStream.destroy();\n\t\t\toldState.resource.playStream.read(); // required to ensure buffered data is drained, prevents memory leak\n\t\t}\n\n\t\t// When leaving the Buffering state (or buffering a new resource), then remove the event listeners from it\n\t\tif (\n\t\t\toldState.status === AudioPlayerStatus.Buffering &&\n\t\t\t(newState.status !== AudioPlayerStatus.Buffering || newState.resource !== oldState.resource)\n\t\t) {\n\t\t\toldState.resource.playStream.off('end', oldState.onFailureCallback);\n\t\t\toldState.resource.playStream.off('close', oldState.onFailureCallback);\n\t\t\toldState.resource.playStream.off('finish', oldState.onFailureCallback);\n\t\t\toldState.resource.playStream.off('readable', oldState.onReadableCallback);\n\t\t}\n\n\t\t// transitioning into an idle should ensure that connections stop speaking\n\t\tif (newState.status === AudioPlayerStatus.Idle) {\n\t\t\tthis._signalStopSpeaking();\n\t\t\tdeleteAudioPlayer(this);\n\t\t}\n\n\t\t// attach to the global audio player timer\n\t\tif (newResource) {\n\t\t\taddAudioPlayer(this);\n\t\t}\n\n\t\t// playing -> playing state changes should still transition if a resource changed (seems like it would be useful!)\n\t\tconst didChangeResources =\n\t\t\toldState.status !== AudioPlayerStatus.Idle &&\n\t\t\tnewState.status === AudioPlayerStatus.Playing &&\n\t\t\toldState.resource !== newState.resource;\n\n\t\tthis._state = newState;\n\n\t\tthis.emit('stateChange', oldState, this._state);\n\t\tif (oldState.status !== newState.status || didChangeResources) {\n\t\t\tthis.emit(newState.status, oldState, this._state as any);\n\t\t}\n\n\t\tthis.debug?.(`state change:\\nfrom ${stringifyState(oldState)}\\nto ${stringifyState(newState)}`);\n\t}\n\n\t/**\n\t * Plays a new resource on the player. If the player is already playing a resource, the existing resource is destroyed\n\t * (it cannot be reused, even in another player) and is replaced with the new resource.\n\t *\n\t * @remarks\n\t * The player will transition to the Playing state once playback begins, and will return to the Idle state once\n\t * playback is ended.\n\t *\n\t * If the player was previously playing a resource and this method is called, the player will not transition to the\n\t * Idle state during the swap over.\n\t * @param resource - The resource to play\n\t * @throws Will throw if attempting to play an audio resource that has already ended, or is being played by another player\n\t */\n\tpublic play<Metadata>(resource: AudioResource<Metadata>) {\n\t\tif (resource.ended) {\n\t\t\tthrow new Error('Cannot play a resource that has already ended.');\n\t\t}\n\n\t\tif (resource.audioPlayer) {\n\t\t\tif (resource.audioPlayer === this) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthrow new Error('Resource is already being played by another audio player.');\n\t\t}\n\n\t\tresource.audioPlayer = this;\n\n\t\t// Attach error listeners to the stream that will propagate the error and then return to the Idle\n\t\t// state if the resource is still being used.\n\t\tconst onStreamError = (error: Error) => {\n\t\t\tif (this.state.status !== AudioPlayerStatus.Idle) {\n\t\t\t\tthis.emit('error', new AudioPlayerError(error, this.state.resource));\n\t\t\t}\n\n\t\t\tif (this.state.status !== AudioPlayerStatus.Idle && this.state.resource === resource) {\n\t\t\t\tthis.state = {\n\t\t\t\t\tstatus: AudioPlayerStatus.Idle,\n\t\t\t\t};\n\t\t\t}\n\t\t};\n\n\t\tresource.playStream.once('error', onStreamError);\n\n\t\tif (resource.started) {\n\t\t\tthis.state = {\n\t\t\t\tstatus: AudioPlayerStatus.Playing,\n\t\t\t\tmissedFrames: 0,\n\t\t\t\tplaybackDuration: 0,\n\t\t\t\tresource,\n\t\t\t\tonStreamError,\n\t\t\t};\n\t\t} else {\n\t\t\tconst onReadableCallback = () => {\n\t\t\t\tif (this.state.status === AudioPlayerStatus.Buffering && this.state.resource === resource) {\n\t\t\t\t\tthis.state = {\n\t\t\t\t\t\tstatus: AudioPlayerStatus.Playing,\n\t\t\t\t\t\tmissedFrames: 0,\n\t\t\t\t\t\tplaybackDuration: 0,\n\t\t\t\t\t\tresource,\n\t\t\t\t\t\tonStreamError,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tconst onFailureCallback = () => {\n\t\t\t\tif (this.state.status === AudioPlayerStatus.Buffering && this.state.resource === resource) {\n\t\t\t\t\tthis.state = {\n\t\t\t\t\t\tstatus: AudioPlayerStatus.Idle,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tresource.playStream.once('readable', onReadableCallback);\n\n\t\t\tresource.playStream.once('end', onFailureCallback);\n\t\t\tresource.playStream.once('close', onFailureCallback);\n\t\t\tresource.playStream.once('finish', onFailureCallback);\n\n\t\t\tthis.state = {\n\t\t\t\tstatus: AudioPlayerStatus.Buffering,\n\t\t\t\tresource,\n\t\t\t\tonReadableCallback,\n\t\t\t\tonFailureCallback,\n\t\t\t\tonStreamError,\n\t\t\t};\n\t\t}\n\t}\n\n\t/**\n\t * Pauses playback of the current resource, if any.\n\t *\n\t * @param interpolateSilence - If true, the player will play 5 packets of silence after pausing to prevent audio glitches\n\t * @returns `true` if the player was successfully paused, otherwise `false`\n\t */\n\tpublic pause(interpolateSilence = true) {\n\t\tif (this.state.status !== AudioPlayerStatus.Playing) return false;\n\t\tthis.state = {\n\t\t\t...this.state,\n\t\t\tstatus: AudioPlayerStatus.Paused,\n\t\t\tsilencePacketsRemaining: interpolateSilence ? 5 : 0,\n\t\t};\n\t\treturn true;\n\t}\n\n\t/**\n\t * Unpauses playback of the current resource, if any.\n\t *\n\t * @returns `true` if the player was successfully unpaused, otherwise `false`\n\t */\n\tpublic unpause() {\n\t\tif (this.state.status !== AudioPlayerStatus.Paused) return false;\n\t\tthis.state = {\n\t\t\t...this.state,\n\t\t\tstatus: AudioPlayerStatus.Playing,\n\t\t\tmissedFrames: 0,\n\t\t};\n\t\treturn true;\n\t}\n\n\t/**\n\t * Stops playback of the current resource and destroys the resource. The player will either transition to the Idle state,\n\t * or remain in its current state until the silence padding frames of the resource have been played.\n\t *\n\t * @param force - If true, will force the player to enter the Idle state even if the resource has silence padding frames\n\t * @returns `true` if the player will come to a stop, otherwise `false`\n\t */\n\tpublic stop(force = false) {\n\t\tif (this.state.status === AudioPlayerStatus.Idle) return false;\n\t\tif (force || this.state.resource.silencePaddingFrames === 0) {\n\t\t\tthis.state = {\n\t\t\t\tstatus: AudioPlayerStatus.Idle,\n\t\t\t};\n\t\t} else if (this.state.resource.silenceRemaining === -1) {\n\t\t\tthis.state.resource.silenceRemaining = this.state.resource.silencePaddingFrames;\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Checks whether the underlying resource (if any) is playable (readable)\n\t *\n\t * @returns `true` if the resource is playable, otherwise `false`\n\t */\n\tpublic checkPlayable() {\n\t\tconst state = this._state;\n\t\tif (state.status === AudioPlayerStatus.Idle || state.status === AudioPlayerStatus.Buffering) return false;\n\n\t\t// If the stream has been destroyed or is no longer readable, then transition to the Idle state.\n\t\tif (!state.resource.readable) {\n\t\t\tthis.state = {\n\t\t\t\tstatus: AudioPlayerStatus.Idle,\n\t\t\t};\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Called roughly every 20ms by the global audio player timer. Dispatches any audio packets that are buffered\n\t * by the active connections of this audio player.\n\t */\n\t// @ts-ignore\n\tprivate _stepDispatch() {\n\t\tconst state = this._state;\n\n\t\t// Guard against the Idle state\n\t\tif (state.status === AudioPlayerStatus.Idle || state.status === AudioPlayerStatus.Buffering) return;\n\n\t\t// Dispatch any audio packets that were prepared in the previous cycle\n\t\tfor (const connection of this.playable) {\n\t\t\tconnection.dispatchAudio();\n\t\t}\n\t}\n\n\t/**\n\t * Called roughly every 20ms by the global audio player timer. Attempts to read an audio packet from the\n\t * underlying resource of the stream, and then has all the active connections of the audio player prepare it\n\t * (encrypt it, append header data) so that it is ready to play at the start of the next cycle.\n\t */\n\t// @ts-ignore\n\tprivate _stepPrepare() {\n\t\tconst state = this._state;\n\n\t\t// Guard against the Idle state\n\t\tif (state.status === AudioPlayerStatus.Idle || state.status === AudioPlayerStatus.Buffering) return;\n\n\t\t// List of connections that can receive the packet\n\t\tconst playable = this.playable;\n\n\t\t/* If the player was previously in the AutoPaused state, check to see whether there are newly available\n\t\t   connections, allowing us to transition out of the AutoPaused state back into the Playing state */\n\t\tif (state.status === AudioPlayerStatus.AutoPaused && playable.length > 0) {\n\t\t\tthis.state = {\n\t\t\t\t...state,\n\t\t\t\tstatus: AudioPlayerStatus.Playing,\n\t\t\t\tmissedFrames: 0,\n\t\t\t};\n\t\t}\n\n\t\t/* If the player is (auto)paused, check to see whether silence packets should be played and\n\t\t   set a timeout to begin the next cycle, ending the current cycle here. */\n\t\tif (state.status === AudioPlayerStatus.Paused || state.status === AudioPlayerStatus.AutoPaused) {\n\t\t\tif (state.silencePacketsRemaining > 0) {\n\t\t\t\tstate.silencePacketsRemaining--;\n\t\t\t\tthis._preparePacket(SILENCE_FRAME, playable, state);\n\t\t\t\tif (state.silencePacketsRemaining === 0) {\n\t\t\t\t\tthis._signalStopSpeaking();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t// If there are no available connections in this cycle, observe the configured \"no subscriber\" behavior.\n\t\tif (playable.length === 0) {\n\t\t\tif (this.behaviors.noSubscriber === NoSubscriberBehavior.Pause) {\n\t\t\t\tthis.state = {\n\t\t\t\t\t...state,\n\t\t\t\t\tstatus: AudioPlayerStatus.AutoPaused,\n\t\t\t\t\tsilencePacketsRemaining: 5,\n\t\t\t\t};\n\t\t\t\treturn;\n\t\t\t} else if (this.behaviors.noSubscriber === NoSubscriberBehavior.Stop) {\n\t\t\t\tthis.stop(true);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Attempt to read an Opus packet from the resource. If there isn't an available packet,\n\t\t * play a silence packet. If there are 5 consecutive cycles with failed reads, then the\n\t\t * playback will end.\n\t\t */\n\t\tconst packet: Buffer | null = state.resource.read();\n\n\t\tif (state.status === AudioPlayerStatus.Playing) {\n\t\t\tif (packet) {\n\t\t\t\tthis._preparePacket(packet, playable, state);\n\t\t\t\tstate.missedFrames = 0;\n\t\t\t} else {\n\t\t\t\tthis._preparePacket(SILENCE_FRAME, playable, state);\n\t\t\t\tstate.missedFrames++;\n\t\t\t\tif (state.missedFrames >= this.behaviors.maxMissedFrames) {\n\t\t\t\t\tthis.stop();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Signals to all the subscribed connections that they should send a packet to Discord indicating\n\t * they are no longer speaking. Called once playback of a resource ends.\n\t */\n\tprivate _signalStopSpeaking() {\n\t\tfor (const { connection } of this.subscribers) {\n\t\t\tconnection.setSpeaking(false);\n\t\t}\n\t}\n\n\t/**\n\t * Instructs the given connections to each prepare this packet to be played at the start of the\n\t * next cycle.\n\t *\n\t * @param packet - The Opus packet to be prepared by each receiver\n\t * @param receivers - The connections that should play this packet\n\t */\n\tprivate _preparePacket(\n\t\tpacket: Buffer,\n\t\treceivers: VoiceConnection[],\n\t\tstate: AudioPlayerPausedState | AudioPlayerPlayingState,\n\t) {\n\t\tstate.playbackDuration += 20;\n\t\tfor (const connection of receivers) {\n\t\t\tconnection.prepareAudioPacket(packet);\n\t\t}\n\t}\n}\n\n/**\n * Creates a new AudioPlayer to be used.\n */\nexport function createAudioPlayer(options?: CreateAudioPlayerOptions) {\n\treturn new AudioPlayer(options);\n}\n", "import type { AudioResource } from './AudioResource';\n\n/**\n * An error emitted by an AudioPlayer. Contains an attached resource to aid with\n * debugging and identifying where the error came from.\n */\nexport class AudioPlayerError extends Error {\n\t/**\n\t * The resource associated with the audio player at the time the error was thrown.\n\t */\n\tpublic readonly resource: AudioResource;\n\n\tpublic constructor(error: Error, resource: AudioResource) {\n\t\tsuper(error.message);\n\t\tthis.resource = resource;\n\t\tthis.name = error.name;\n\t\tthis.stack = error.stack!;\n\t}\n}\n", "/* eslint-disable @typescript-eslint/dot-notation */\nimport type { VoiceConnection } from '../VoiceConnection';\nimport type { AudioPlayer } from './AudioPlayer';\n\n/**\n * Represents a subscription of a voice connection to an audio player, allowing\n * the audio player to play audio on the voice connection.\n */\nexport class PlayerSubscription {\n\t/**\n\t * The voice connection of this subscription.\n\t */\n\tpublic readonly connection: VoiceConnection;\n\n\t/**\n\t * The audio player of this subscription.\n\t */\n\tpublic readonly player: AudioPlayer;\n\n\tpublic constructor(connection: VoiceConnection, player: AudioPlayer) {\n\t\tthis.connection = connection;\n\t\tthis.player = player;\n\t}\n\n\t/**\n\t * Unsubscribes the connection from the audio player, meaning that the\n\t * audio player cannot stream audio to it until a new subscription is made.\n\t */\n\tpublic unsubscribe() {\n\t\tthis.connection['onSubscriptionRemoved'](this);\n\t\tthis.player['unsubscribe'](this);\n\t}\n}\n", "import { EventEmitter } from 'node:events';\n\n/**\n * The known data for a user in a Discord voice connection.\n */\nexport interface VoiceUserData {\n\t/**\n\t * The SSRC of the user's audio stream.\n\t */\n\taudioSSRC: number;\n\n\t/**\n\t * The Discord user id of the user.\n\t */\n\tuserId: string;\n\n\t/**\n\t * The SSRC of the user's video stream (if one exists)\n\t * Cannot be 0. If undefined, the user has no video stream.\n\t */\n\tvideoSSRC?: number;\n}\n\nexport interface SSRCMap extends EventEmitter {\n\ton(event: 'create', listener: (newData: VoiceUserData) => void): this;\n\ton(event: 'update', listener: (oldData: VoiceUserData | undefined, newData: VoiceUserData) => void): this;\n\ton(event: 'delete', listener: (deletedData: VoiceUserData) => void): this;\n}\n\n/**\n * Maps audio SSRCs to data of users in voice connections.\n */\nexport class SSRCMap extends EventEmitter {\n\t/**\n\t * The underlying map.\n\t */\n\tprivate readonly map: Map<number, VoiceUserData>;\n\n\tpublic constructor() {\n\t\tsuper();\n\t\tthis.map = new Map();\n\t}\n\n\t/**\n\t * Updates the map with new user data\n\t *\n\t * @param data - The data to update with\n\t */\n\tpublic update(data: VoiceUserData) {\n\t\tconst existing = this.map.get(data.audioSSRC);\n\n\t\tconst newValue = {\n\t\t\t...this.map.get(data.audioSSRC),\n\t\t\t...data,\n\t\t};\n\n\t\tthis.map.set(data.audioSSRC, newValue);\n\t\tif (!existing) this.emit('create', newValue);\n\t\tthis.emit('update', existing, newValue);\n\t}\n\n\t/**\n\t * Gets the stored voice data of a user.\n\t *\n\t * @param target - The target, either their user id or audio SSRC\n\t */\n\tpublic get(target: number | string) {\n\t\tif (typeof target === 'number') {\n\t\t\treturn this.map.get(target);\n\t\t}\n\n\t\tfor (const data of this.map.values()) {\n\t\t\tif (data.userId === target) {\n\t\t\t\treturn data;\n\t\t\t}\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * Deletes the stored voice data about a user.\n\t *\n\t * @param target - The target of the delete operation, either their audio SSRC or user id\n\t * @returns The data that was deleted, if any\n\t */\n\tpublic delete(target: number | string) {\n\t\tif (typeof target === 'number') {\n\t\t\tconst existing = this.map.get(target);\n\t\t\tif (existing) {\n\t\t\t\tthis.map.delete(target);\n\t\t\t\tthis.emit('delete', existing);\n\t\t\t}\n\n\t\t\treturn existing;\n\t\t}\n\n\t\tfor (const [audioSSRC, data] of this.map.entries()) {\n\t\t\tif (data.userId === target) {\n\t\t\t\tthis.map.delete(audioSSRC);\n\t\t\t\tthis.emit('delete', data);\n\t\t\t\treturn data;\n\t\t\t}\n\t\t}\n\n\t\treturn undefined;\n\t}\n}\n", "/* eslint-disable @typescript-eslint/unified-signatures */\nimport { EventEmitter } from 'node:events';\n\nexport interface SpeakingMap extends EventEmitter {\n\t/**\n\t * Emitted when a user starts speaking.\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'start', listener: (userId: string) => void): this;\n\n\t/**\n\t * Emitted when a user ends speaking.\n\t *\n\t * @eventProperty\n\t */\n\ton(event: 'end', listener: (userId: string) => void): this;\n}\n\n/**\n * Tracks the speaking states of users in a voice channel.\n */\nexport class SpeakingMap extends EventEmitter {\n\t/**\n\t * The delay after a packet is received from a user until they're marked as not speaking anymore.\n\t */\n\tpublic static readonly DELAY = 100;\n\n\t/**\n\t * The currently speaking users, mapped to the milliseconds since UNIX epoch at which they started speaking.\n\t */\n\tpublic readonly users: Map<string, number>;\n\n\tprivate readonly speakingTimeouts: Map<string, NodeJS.Timeout>;\n\n\tpublic constructor() {\n\t\tsuper();\n\t\tthis.users = new Map();\n\t\tthis.speakingTimeouts = new Map();\n\t}\n\n\tpublic onPacket(userId: string) {\n\t\tconst timeout = this.speakingTimeouts.get(userId);\n\t\tif (timeout) {\n\t\t\tclearTimeout(timeout);\n\t\t} else {\n\t\t\tthis.users.set(userId, Date.now());\n\t\t\tthis.emit('start', userId);\n\t\t}\n\n\t\tthis.startTimeout(userId);\n\t}\n\n\tprivate startTimeout(userId: string) {\n\t\tthis.speakingTimeouts.set(\n\t\t\tuserId,\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.emit('end', userId);\n\t\t\t\tthis.speakingTimeouts.delete(userId);\n\t\t\t\tthis.users.delete(userId);\n\t\t\t}, SpeakingMap.DELAY),\n\t\t);\n\t}\n}\n", "import type { JoinConfig } from './DataStore';\nimport { createVoiceConnection } from './VoiceConnection';\nimport type { DiscordGatewayAdapterCreator } from './util/adapter';\n\n/**\n * The options that can be given when creating a voice connection.\n */\nexport interface CreateVoiceConnectionOptions {\n\tadapterCreator: DiscordGatewayAdapterCreator;\n\n\t/**\n\t * If true, debug messages will be enabled for the voice connection and its\n\t * related components. Defaults to false.\n\t */\n\tdebug?: boolean | undefined;\n}\n\n/**\n * The options that can be given when joining a voice channel.\n */\nexport interface JoinVoiceChannelOptions {\n\t/**\n\t * The id of the Discord voice channel to join.\n\t */\n\tchannelId: string;\n\n\t/**\n\t * An optional group identifier for the voice connection.\n\t */\n\tgroup?: string;\n\n\t/**\n\t * The id of the guild that the voice channel belongs to.\n\t */\n\tguildId: string;\n\n\t/**\n\t * Whether to join the channel deafened (defaults to true)\n\t */\n\tselfDeaf?: boolean;\n\n\t/**\n\t * Whether to join the channel muted (defaults to true)\n\t */\n\tselfMute?: boolean;\n}\n\n/**\n * Creates a VoiceConnection to a Discord voice channel.\n *\n * @param options - the options for joining the voice channel\n */\nexport function joinVoiceChannel(options: CreateVoiceConnectionOptions & JoinVoiceChannelOptions) {\n\tconst joinConfig: JoinConfig = {\n\t\tselfDeaf: true,\n\t\tselfMute: false,\n\t\tgroup: 'default',\n\t\t...options,\n\t};\n\n\treturn createVoiceConnection(joinConfig, {\n\t\tadapterCreator: options.adapterCreator,\n\t\tdebug: options.debug,\n\t});\n}\n", "import type { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport { pipeline, type Readable } from 'node:stream';\nimport prism from 'prism-media';\nimport { noop } from '../util/util';\nimport { SILENCE_FRAME, type AudioPlayer } from './AudioPlayer';\nimport { findPipeline, StreamType, TransformerType, type Edge } from './TransformerGraph';\n\n/**\n * Options that are set when creating a new audio resource.\n *\n * @typeParam Metadata - the type for the metadata (if any) of the audio resource\n */\nexport interface CreateAudioResourceOptions<Metadata> {\n\t/**\n\t * Whether or not inline volume should be enabled. If enabled, you will be able to change the volume\n\t * of the stream on-the-fly. However, this also increases the performance cost of playback. Defaults to `false`.\n\t */\n\tinlineVolume?: boolean;\n\n\t/**\n\t * The type of the input stream. Defaults to `StreamType.Arbitrary`.\n\t */\n\tinputType?: StreamType;\n\n\t/**\n\t * Optional metadata that can be attached to the resource (e.g. track title, random id).\n\t * This is useful for identification purposes when the resource is passed around in events.\n\t * See {@link AudioResource.metadata}\n\t */\n\tmetadata?: Metadata;\n\n\t/**\n\t * The number of silence frames to append to the end of the resource's audio stream, to prevent interpolation glitches.\n\t * Defaults to 5.\n\t */\n\tsilencePaddingFrames?: number;\n}\n\n/**\n * Represents an audio resource that can be played by an audio player.\n *\n * @typeParam Metadata - the type for the metadata (if any) of the audio resource\n */\nexport class AudioResource<Metadata = unknown> {\n\t/**\n\t * An object-mode Readable stream that emits Opus packets. This is what is played by audio players.\n\t */\n\tpublic readonly playStream: Readable;\n\n\t/**\n\t * The pipeline used to convert the input stream into a playable format. For example, this may\n\t * contain an FFmpeg component for arbitrary inputs, and it may contain a VolumeTransformer component\n\t * for resources with inline volume transformation enabled.\n\t */\n\tpublic readonly edges: readonly Edge[];\n\n\t/**\n\t * Optional metadata that can be used to identify the resource.\n\t */\n\tpublic metadata: Metadata;\n\n\t/**\n\t * If the resource was created with inline volume transformation enabled, then this will be a\n\t * prism-media VolumeTransformer. You can use this to alter the volume of the stream.\n\t */\n\tpublic readonly volume?: prism.VolumeTransformer;\n\n\t/**\n\t * If using an Opus encoder to create this audio resource, then this will be a prism-media opus.Encoder.\n\t * You can use this to control settings such as bitrate, FEC, PLP.\n\t */\n\tpublic readonly encoder?: prism.opus.Encoder;\n\n\t/**\n\t * The audio player that the resource is subscribed to, if any.\n\t */\n\tpublic audioPlayer?: AudioPlayer | undefined;\n\n\t/**\n\t * The playback duration of this audio resource, given in milliseconds.\n\t */\n\tpublic playbackDuration = 0;\n\n\t/**\n\t * Whether or not the stream for this resource has started (data has become readable)\n\t */\n\tpublic started = false;\n\n\t/**\n\t * The number of silence frames to append to the end of the resource's audio stream, to prevent interpolation glitches.\n\t */\n\tpublic readonly silencePaddingFrames: number;\n\n\t/**\n\t * The number of remaining silence frames to play. If -1, the frames have not yet started playing.\n\t */\n\tpublic silenceRemaining = -1;\n\n\tpublic constructor(\n\t\tedges: readonly Edge[],\n\t\tstreams: readonly Readable[],\n\t\tmetadata: Metadata,\n\t\tsilencePaddingFrames: number,\n\t) {\n\t\tthis.edges = edges;\n\t\tthis.playStream = streams.length > 1 ? (pipeline(streams, noop) as any as Readable) : streams[0]!;\n\t\tthis.metadata = metadata;\n\t\tthis.silencePaddingFrames = silencePaddingFrames;\n\n\t\tfor (const stream of streams) {\n\t\t\tif (stream instanceof prism.VolumeTransformer) {\n\t\t\t\tthis.volume = stream;\n\t\t\t} else if (stream instanceof prism.opus.Encoder) {\n\t\t\t\tthis.encoder = stream;\n\t\t\t}\n\t\t}\n\n\t\tthis.playStream.once('readable', () => (this.started = true));\n\t}\n\n\t/**\n\t * Whether this resource is readable. If the underlying resource is no longer readable, this will still return true\n\t * while there are silence padding frames left to play.\n\t */\n\tpublic get readable() {\n\t\tif (this.silenceRemaining === 0) return false;\n\t\tconst real = this.playStream.readable;\n\t\tif (!real) {\n\t\t\tif (this.silenceRemaining === -1) this.silenceRemaining = this.silencePaddingFrames;\n\t\t\treturn this.silenceRemaining !== 0;\n\t\t}\n\n\t\treturn real;\n\t}\n\n\t/**\n\t * Whether this resource has ended or not.\n\t */\n\tpublic get ended() {\n\t\treturn this.playStream.readableEnded || this.playStream.destroyed || this.silenceRemaining === 0;\n\t}\n\n\t/**\n\t * Attempts to read an Opus packet from the audio resource. If a packet is available, the playbackDuration\n\t * is incremented.\n\t *\n\t * @remarks\n\t * It is advisable to check that the playStream is readable before calling this method. While no runtime\n\t * errors will be thrown, you should check that the resource is still available before attempting to\n\t * read from it.\n\t * @internal\n\t */\n\tpublic read(): Buffer | null {\n\t\tif (this.silenceRemaining === 0) {\n\t\t\treturn null;\n\t\t} else if (this.silenceRemaining > 0) {\n\t\t\tthis.silenceRemaining--;\n\t\t\treturn SILENCE_FRAME;\n\t\t}\n\n\t\tconst packet = this.playStream.read() as Buffer | null;\n\t\tif (packet) {\n\t\t\tthis.playbackDuration += 20;\n\t\t}\n\n\t\treturn packet;\n\t}\n}\n\n/**\n * Ensures that a path contains at least one volume transforming component.\n *\n * @param path - The path to validate constraints on\n */\nexport const VOLUME_CONSTRAINT = (path: Edge[]) => path.some((edge) => edge.type === TransformerType.InlineVolume);\n\nexport const NO_CONSTRAINT = () => true;\n\n/**\n * Tries to infer the type of a stream to aid with transcoder pipelining.\n *\n * @param stream - The stream to infer the type of\n */\nexport function inferStreamType(stream: Readable): {\n\thasVolume: boolean;\n\tstreamType: StreamType;\n} {\n\tif (stream instanceof prism.opus.Encoder) {\n\t\treturn { streamType: StreamType.Opus, hasVolume: false };\n\t} else if (stream instanceof prism.opus.Decoder) {\n\t\treturn { streamType: StreamType.Raw, hasVolume: false };\n\t} else if (stream instanceof prism.VolumeTransformer) {\n\t\treturn { streamType: StreamType.Raw, hasVolume: true };\n\t} else if (stream instanceof prism.opus.OggDemuxer) {\n\t\treturn { streamType: StreamType.Opus, hasVolume: false };\n\t} else if (stream instanceof prism.opus.WebmDemuxer) {\n\t\treturn { streamType: StreamType.Opus, hasVolume: false };\n\t}\n\n\treturn { streamType: StreamType.Arbitrary, hasVolume: false };\n}\n\n/**\n * Creates an audio resource that can be played by audio players.\n *\n * @remarks\n * If the input is given as a string, then the inputType option will be overridden and FFmpeg will be used.\n *\n * If the input is not in the correct format, then a pipeline of transcoders and transformers will be created\n * to ensure that the resultant stream is in the correct format for playback. This could involve using FFmpeg,\n * Opus transcoders, and Ogg/WebM demuxers.\n * @param input - The resource to play\n * @param options - Configurable options for creating the resource\n * @typeParam Metadata - the type for the metadata (if any) of the audio resource\n */\nexport function createAudioResource<Metadata>(\n\tinput: Readable | string,\n\toptions: CreateAudioResourceOptions<Metadata> &\n\t\tPick<\n\t\t\tMetadata extends null | undefined\n\t\t\t\t? CreateAudioResourceOptions<Metadata>\n\t\t\t\t: Required<CreateAudioResourceOptions<Metadata>>,\n\t\t\t'metadata'\n\t\t>,\n): AudioResource<Metadata extends null | undefined ? null : Metadata>;\n\n/**\n * Creates an audio resource that can be played by audio players.\n *\n * @remarks\n * If the input is given as a string, then the inputType option will be overridden and FFmpeg will be used.\n *\n * If the input is not in the correct format, then a pipeline of transcoders and transformers will be created\n * to ensure that the resultant stream is in the correct format for playback. This could involve using FFmpeg,\n * Opus transcoders, and Ogg/WebM demuxers.\n * @param input - The resource to play\n * @param options - Configurable options for creating the resource\n * @typeParam Metadata - the type for the metadata (if any) of the audio resource\n */\nexport function createAudioResource<Metadata extends null | undefined>(\n\tinput: Readable | string,\n\toptions?: Omit<CreateAudioResourceOptions<Metadata>, 'metadata'>,\n): AudioResource<null>;\n\n/**\n * Creates an audio resource that can be played by audio players.\n *\n * @remarks\n * If the input is given as a string, then the inputType option will be overridden and FFmpeg will be used.\n *\n * If the input is not in the correct format, then a pipeline of transcoders and transformers will be created\n * to ensure that the resultant stream is in the correct format for playback. This could involve using FFmpeg,\n * Opus transcoders, and Ogg/WebM demuxers.\n * @param input - The resource to play\n * @param options - Configurable options for creating the resource\n * @typeParam Metadata - the type for the metadata (if any) of the audio resource\n */\nexport function createAudioResource<Metadata>(\n\tinput: Readable | string,\n\toptions: CreateAudioResourceOptions<Metadata> = {},\n): AudioResource<Metadata> {\n\tlet inputType = options.inputType;\n\tlet needsInlineVolume = Boolean(options.inlineVolume);\n\n\t// string inputs can only be used with FFmpeg\n\tif (typeof input === 'string') {\n\t\tinputType = StreamType.Arbitrary;\n\t} else if (inputType === undefined) {\n\t\tconst analysis = inferStreamType(input);\n\t\tinputType = analysis.streamType;\n\t\tneedsInlineVolume = needsInlineVolume && !analysis.hasVolume;\n\t}\n\n\tconst transformerPipeline = findPipeline(inputType, needsInlineVolume ? VOLUME_CONSTRAINT : NO_CONSTRAINT);\n\n\tif (transformerPipeline.length === 0) {\n\t\tif (typeof input === 'string') throw new Error(`Invalid pipeline constructed for string resource '${input}'`);\n\t\t// No adjustments required\n\t\treturn new AudioResource<Metadata>(\n\t\t\t[],\n\t\t\t[input],\n\t\t\t(options.metadata ?? null) as Metadata,\n\t\t\toptions.silencePaddingFrames ?? 5,\n\t\t);\n\t}\n\n\tconst streams = transformerPipeline.map((edge) => edge.transformer(input));\n\tif (typeof input !== 'string') streams.unshift(input);\n\n\treturn new AudioResource<Metadata>(\n\t\ttransformerPipeline,\n\t\tstreams,\n\t\t(options.metadata ?? null) as Metadata,\n\t\toptions.silencePaddingFrames ?? 5,\n\t);\n}\n", "import type { Readable } from 'node:stream';\nimport prism from 'prism-media';\n\n/**\n * This module creates a Transformer Graph to figure out what the most efficient way\n * of transforming the input stream into something playable would be.\n */\n\nconst FFMPEG_PCM_ARGUMENTS = ['-analyzeduration', '0', '-loglevel', '0', '-f', 's16le', '-ar', '48000', '-ac', '2'];\nconst FFMPEG_OPUS_ARGUMENTS = [\n\t'-analyzeduration',\n\t'0',\n\t'-loglevel',\n\t'0',\n\t'-acodec',\n\t'libopus',\n\t'-f',\n\t'opus',\n\t'-ar',\n\t'48000',\n\t'-ac',\n\t'2',\n];\n\n/**\n * The different types of stream that can exist within the pipeline.\n */\nexport enum StreamType {\n\t/**\n\t * The type of the stream at this point is unknown.\n\t */\n\tArbitrary = 'arbitrary',\n\t/**\n\t * The stream at this point is Opus audio encoded in an Ogg wrapper.\n\t */\n\tOggOpus = 'ogg/opus',\n\t/**\n\t * The stream at this point is Opus audio, and the stream is in object-mode. This is ready to play.\n\t */\n\tOpus = 'opus',\n\t/**\n\t * The stream at this point is s16le PCM.\n\t */\n\tRaw = 'raw',\n\t/**\n\t * The stream at this point is Opus audio encoded in a WebM wrapper.\n\t */\n\tWebmOpus = 'webm/opus',\n}\n\n/**\n * The different types of transformers that can exist within the pipeline.\n */\nexport enum TransformerType {\n\tFFmpegOgg = 'ffmpeg ogg',\n\tFFmpegPCM = 'ffmpeg pcm',\n\tInlineVolume = 'volume transformer',\n\tOggOpusDemuxer = 'ogg/opus demuxer',\n\tOpusDecoder = 'opus decoder',\n\tOpusEncoder = 'opus encoder',\n\tWebmOpusDemuxer = 'webm/opus demuxer',\n}\n\n/**\n * Represents a pathway from one stream type to another using a transformer.\n */\nexport interface Edge {\n\tcost: number;\n\tfrom: Node;\n\tto: Node;\n\ttransformer(input: Readable | string): Readable;\n\ttype: TransformerType;\n}\n\n/**\n * Represents a type of stream within the graph, e.g. an Opus stream, or a stream of raw audio.\n */\nexport class Node {\n\t/**\n\t * The outbound edges from this node.\n\t */\n\tpublic readonly edges: Edge[] = [];\n\n\t/**\n\t * The type of stream for this node.\n\t */\n\tpublic readonly type: StreamType;\n\n\tpublic constructor(type: StreamType) {\n\t\tthis.type = type;\n\t}\n\n\t/**\n\t * Creates an outbound edge from this node.\n\t *\n\t * @param edge - The edge to create\n\t */\n\tpublic addEdge(edge: Omit<Edge, 'from'>) {\n\t\tthis.edges.push({ ...edge, from: this });\n\t}\n}\n\n// Create a node for each stream type\nlet NODES: Map<StreamType, Node> | null = null;\n\n/**\n * Gets a node from its stream type.\n *\n * @param type - The stream type of the target node\n */\nexport function getNode(type: StreamType) {\n\tconst node = (NODES ??= initializeNodes()).get(type);\n\tif (!node) throw new Error(`Node type '${type}' does not exist!`);\n\treturn node;\n}\n\n// Try to enable FFmpeg Ogg optimizations\nfunction canEnableFFmpegOptimizations(): boolean {\n\ttry {\n\t\treturn prism.FFmpeg.getInfo().output.includes('--enable-libopus');\n\t} catch {}\n\n\treturn false;\n}\n\nfunction initializeNodes(): Map<StreamType, Node> {\n\tconst nodes = new Map<StreamType, Node>();\n\tfor (const streamType of Object.values(StreamType)) {\n\t\tnodes.set(streamType, new Node(streamType));\n\t}\n\n\tnodes.get(StreamType.Raw)!.addEdge({\n\t\ttype: TransformerType.OpusEncoder,\n\t\tto: nodes.get(StreamType.Opus)!,\n\t\tcost: 1.5,\n\t\ttransformer: () => new prism.opus.Encoder({ rate: 48_000, channels: 2, frameSize: 960 }),\n\t});\n\n\tnodes.get(StreamType.Opus)!.addEdge({\n\t\ttype: TransformerType.OpusDecoder,\n\t\tto: nodes.get(StreamType.Raw)!,\n\t\tcost: 1.5,\n\t\ttransformer: () => new prism.opus.Decoder({ rate: 48_000, channels: 2, frameSize: 960 }),\n\t});\n\n\tnodes.get(StreamType.OggOpus)!.addEdge({\n\t\ttype: TransformerType.OggOpusDemuxer,\n\t\tto: nodes.get(StreamType.Opus)!,\n\t\tcost: 1,\n\t\ttransformer: () => new prism.opus.OggDemuxer(),\n\t});\n\n\tnodes.get(StreamType.WebmOpus)!.addEdge({\n\t\ttype: TransformerType.WebmOpusDemuxer,\n\t\tto: nodes.get(StreamType.Opus)!,\n\t\tcost: 1,\n\t\ttransformer: () => new prism.opus.WebmDemuxer(),\n\t});\n\n\tconst FFMPEG_PCM_EDGE: Omit<Edge, 'from'> = {\n\t\ttype: TransformerType.FFmpegPCM,\n\t\tto: nodes.get(StreamType.Raw)!,\n\t\tcost: 2,\n\t\ttransformer: (input) =>\n\t\t\tnew prism.FFmpeg({\n\t\t\t\targs: ['-i', typeof input === 'string' ? input : '-', ...FFMPEG_PCM_ARGUMENTS],\n\t\t\t}),\n\t};\n\n\tnodes.get(StreamType.Arbitrary)!.addEdge(FFMPEG_PCM_EDGE);\n\tnodes.get(StreamType.OggOpus)!.addEdge(FFMPEG_PCM_EDGE);\n\tnodes.get(StreamType.WebmOpus)!.addEdge(FFMPEG_PCM_EDGE);\n\n\tnodes.get(StreamType.Raw)!.addEdge({\n\t\ttype: TransformerType.InlineVolume,\n\t\tto: nodes.get(StreamType.Raw)!,\n\t\tcost: 0.5,\n\t\ttransformer: () => new prism.VolumeTransformer({ type: 's16le' }),\n\t});\n\n\tif (canEnableFFmpegOptimizations()) {\n\t\tconst FFMPEG_OGG_EDGE: Omit<Edge, 'from'> = {\n\t\t\ttype: TransformerType.FFmpegOgg,\n\t\t\tto: nodes.get(StreamType.OggOpus)!,\n\t\t\tcost: 2,\n\t\t\ttransformer: (input) =>\n\t\t\t\tnew prism.FFmpeg({\n\t\t\t\t\targs: ['-i', typeof input === 'string' ? input : '-', ...FFMPEG_OPUS_ARGUMENTS],\n\t\t\t\t}),\n\t\t};\n\t\tnodes.get(StreamType.Arbitrary)!.addEdge(FFMPEG_OGG_EDGE);\n\t\t// Include Ogg and WebM as well in case they have different sampling rates or are mono instead of stereo\n\t\t// at the moment, this will not do anything. However, if/when detection for correct Opus headers is\n\t\t// implemented, this will help inform the voice engine that it is able to transcode the audio.\n\t\tnodes.get(StreamType.OggOpus)!.addEdge(FFMPEG_OGG_EDGE);\n\t\tnodes.get(StreamType.WebmOpus)!.addEdge(FFMPEG_OGG_EDGE);\n\t}\n\n\treturn nodes;\n}\n\n/**\n * Represents a step in the path from node A to node B.\n */\ninterface Step {\n\t/**\n\t * The cost of the steps after this step.\n\t */\n\tcost: number;\n\n\t/**\n\t * The edge associated with this step.\n\t */\n\tedge?: Edge;\n\n\t/**\n\t * The next step.\n\t */\n\tnext?: Step;\n}\n\n/**\n * Finds the shortest cost path from node A to node B.\n *\n * @param from - The start node\n * @param constraints - Extra validation for a potential solution. Takes a path, returns true if the path is valid\n * @param goal - The target node\n * @param path - The running path\n * @param depth - The number of remaining recursions\n */\nfunction findPath(\n\tfrom: Node,\n\tconstraints: (path: Edge[]) => boolean,\n\tgoal = getNode(StreamType.Opus),\n\tpath: Edge[] = [],\n\tdepth = 5,\n): Step {\n\tif (from === goal && constraints(path)) {\n\t\treturn { cost: 0 };\n\t} else if (depth === 0) {\n\t\treturn { cost: Number.POSITIVE_INFINITY };\n\t}\n\n\tlet currentBest: Step | undefined;\n\tfor (const edge of from.edges) {\n\t\tif (currentBest && edge.cost > currentBest.cost) continue;\n\t\tconst next = findPath(edge.to, constraints, goal, [...path, edge], depth - 1);\n\t\tconst cost = edge.cost + next.cost;\n\t\tif (!currentBest || cost < currentBest.cost) {\n\t\t\tcurrentBest = { cost, edge, next };\n\t\t}\n\t}\n\n\treturn currentBest ?? { cost: Number.POSITIVE_INFINITY };\n}\n\n/**\n * Takes the solution from findPath and assembles it into a list of edges.\n *\n * @param step - The first step of the path\n */\nfunction constructPipeline(step: Step) {\n\tconst edges = [];\n\tlet current: Step | undefined = step;\n\twhile (current?.edge) {\n\t\tedges.push(current.edge);\n\t\tcurrent = current.next;\n\t}\n\n\treturn edges;\n}\n\n/**\n * Finds the lowest-cost pipeline to convert the input stream type into an Opus stream.\n *\n * @param from - The stream type to start from\n * @param constraint - Extra constraints that may be imposed on potential solution\n */\nexport function findPipeline(from: StreamType, constraint: (path: Edge[]) => boolean) {\n\treturn constructPipeline(findPath(getNode(from), constraint));\n}\n", "/* eslint-disable @typescript-eslint/no-var-requires */\n/* eslint-disable @typescript-eslint/no-require-imports */\nimport { resolve, dirname } from 'node:path';\nimport prism from 'prism-media';\n\n/**\n * Tries to find the package.json file for a given module.\n *\n * @param dir - The directory to look in\n * @param packageName - The name of the package to look for\n * @param depth - The maximum recursion depth\n */\nfunction findPackageJSON(\n\tdir: string,\n\tpackageName: string,\n\tdepth: number,\n): { name: string; version: string } | undefined {\n\tif (depth === 0) return undefined;\n\tconst attemptedPath = resolve(dir, './package.json');\n\ttry {\n\t\tconst pkg = require(attemptedPath);\n\t\tif (pkg.name !== packageName) throw new Error('package.json does not match');\n\t\treturn pkg;\n\t} catch {\n\t\treturn findPackageJSON(resolve(dir, '..'), packageName, depth - 1);\n\t}\n}\n\n/**\n * Tries to find the version of a dependency.\n *\n * @param name - The package to find the version of\n */\nfunction version(name: string): string {\n\ttry {\n\t\tif (name === '@discordjs/voice') {\n\t\t\treturn '0.16.1';\n\t\t}\n\n\t\tconst pkg = findPackageJSON(dirname(require.resolve(name)), name, 3);\n\t\treturn pkg?.version ?? 'not found';\n\t} catch {\n\t\treturn 'not found';\n\t}\n}\n\n/**\n * Generates a report of the dependencies used by the \\@discordjs/voice module.\n * Useful for debugging.\n */\nexport function generateDependencyReport() {\n\tconst report = [];\n\tconst addVersion = (name: string) => report.push(`- ${name}: ${version(name)}`);\n\t// general\n\treport.push('Core Dependencies');\n\taddVersion('@discordjs/voice');\n\taddVersion('prism-media');\n\treport.push('');\n\n\t// opus\n\treport.push('Opus Libraries');\n\taddVersion('@discordjs/opus');\n\taddVersion('opusscript');\n\treport.push('');\n\n\t// encryption\n\treport.push('Encryption Libraries');\n\taddVersion('sodium-native');\n\taddVersion('sodium');\n\taddVersion('libsodium-wrappers');\n\taddVersion('tweetnacl');\n\treport.push('');\n\n\t// ffmpeg\n\treport.push('FFmpeg');\n\ttry {\n\t\tconst info = prism.FFmpeg.getInfo();\n\t\treport.push(`- version: ${info.version}`);\n\t\treport.push(`- libopus: ${info.output.includes('--enable-libopus') ? 'yes' : 'no'}`);\n\t} catch {\n\t\treport.push('- not found');\n\t}\n\n\treturn ['-'.repeat(50), ...report, '-'.repeat(50)].join('\\n');\n}\n", "import { type EventEmitter, once } from 'node:events';\nimport type { VoiceConnection, VoiceConnectionStatus } from '../VoiceConnection';\nimport type { AudioPlayer, AudioPlayerStatus } from '../audio/AudioPlayer';\nimport { abortAfter } from './abortAfter';\n\n/**\n * Allows a voice connection a specified amount of time to enter a given state, otherwise rejects with an error.\n *\n * @param target - The voice connection that we want to observe the state change for\n * @param status - The status that the voice connection should be in\n * @param timeoutOrSignal - The maximum time we are allowing for this to occur, or a signal that will abort the operation\n */\nexport function entersState(\n\ttarget: VoiceConnection,\n\tstatus: VoiceConnectionStatus,\n\ttimeoutOrSignal: AbortSignal | number,\n): Promise<VoiceConnection>;\n\n/**\n * Allows an audio player a specified amount of time to enter a given state, otherwise rejects with an error.\n *\n * @param target - The audio player that we want to observe the state change for\n * @param status - The status that the audio player should be in\n * @param timeoutOrSignal - The maximum time we are allowing for this to occur, or a signal that will abort the operation\n */\nexport function entersState(\n\ttarget: AudioPlayer,\n\tstatus: AudioPlayerStatus,\n\ttimeoutOrSignal: AbortSignal | number,\n): Promise<AudioPlayer>;\n\n/**\n * Allows a target a specified amount of time to enter a given state, otherwise rejects with an error.\n *\n * @param target - The object that we want to observe the state change for\n * @param status - The status that the target should be in\n * @param timeoutOrSignal - The maximum time we are allowing for this to occur, or a signal that will abort the operation\n */\nexport async function entersState<Target extends AudioPlayer | VoiceConnection>(\n\ttarget: Target,\n\tstatus: AudioPlayerStatus | VoiceConnectionStatus,\n\ttimeoutOrSignal: AbortSignal | number,\n) {\n\tif (target.state.status !== status) {\n\t\tconst [ac, signal] =\n\t\t\ttypeof timeoutOrSignal === 'number' ? abortAfter(timeoutOrSignal) : [undefined, timeoutOrSignal];\n\t\ttry {\n\t\t\tawait once(target as EventEmitter, status, { signal });\n\t\t} finally {\n\t\t\tac?.abort();\n\t\t}\n\t}\n\n\treturn target;\n}\n", "/**\n * Creates an abort controller that aborts after the given time.\n *\n * @param delay - The time in milliseconds to wait before aborting\n */\nexport function abortAfter(delay: number): [AbortController, AbortSignal] {\n\tconst ac = new AbortController();\n\tconst timeout = setTimeout(() => ac.abort(), delay);\n\t// @ts-expect-error: No type for timeout\n\tac.signal.addEventListener('abort', () => clearTimeout(timeout));\n\treturn [ac, ac.signal];\n}\n", "import { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport process from 'node:process';\nimport { Readable } from 'node:stream';\nimport prism from 'prism-media';\nimport { StreamType } from '..';\nimport { noop } from './util';\n\n/**\n * Takes an Opus Head, and verifies whether the associated Opus audio is suitable to play in a Discord voice channel.\n *\n * @param opusHead - The Opus Head to validate\n * @returns `true` if suitable to play in a Discord voice channel, otherwise `false`\n */\nexport function validateDiscordOpusHead(opusHead: Buffer): boolean {\n\tconst channels = opusHead.readUInt8(9);\n\tconst sampleRate = opusHead.readUInt32LE(12);\n\treturn channels === 2 && sampleRate === 48_000;\n}\n\n/**\n * The resulting information after probing an audio stream\n */\nexport interface ProbeInfo {\n\t/**\n\t * The readable audio stream to use. You should use this rather than the input stream, as the probing\n\t * function can sometimes read the input stream to its end and cause the stream to close.\n\t */\n\tstream: Readable;\n\n\t/**\n\t * The recommended stream type for this audio stream.\n\t */\n\ttype: StreamType;\n}\n\n/**\n * Attempt to probe a readable stream to figure out whether it can be demuxed using an Ogg or WebM Opus demuxer.\n *\n * @param stream - The readable stream to probe\n * @param probeSize - The number of bytes to attempt to read before giving up on the probe\n * @param validator - The Opus Head validator function\n * @experimental\n */\nexport async function demuxProbe(\n\tstream: Readable,\n\tprobeSize = 1_024,\n\tvalidator = validateDiscordOpusHead,\n): Promise<ProbeInfo> {\n\treturn new Promise((resolve, reject) => {\n\t\t// Preconditions\n\t\tif (stream.readableObjectMode) {\n\t\t\treject(new Error('Cannot probe a readable stream in object mode'));\n\t\t\treturn;\n\t\t}\n\n\t\tif (stream.readableEnded) {\n\t\t\treject(new Error('Cannot probe a stream that has ended'));\n\t\t\treturn;\n\t\t}\n\n\t\tlet readBuffer = Buffer.alloc(0);\n\n\t\tlet resolved: StreamType | undefined;\n\n\t\tconst finish = (type: StreamType) => {\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-use-before-define\n\t\t\tstream.off('data', onData);\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-use-before-define\n\t\t\tstream.off('close', onClose);\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-use-before-define\n\t\t\tstream.off('end', onClose);\n\t\t\tstream.pause();\n\t\t\tresolved = type;\n\t\t\tif (stream.readableEnded) {\n\t\t\t\tresolve({\n\t\t\t\t\tstream: Readable.from(readBuffer),\n\t\t\t\t\ttype,\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tif (readBuffer.length > 0) {\n\t\t\t\t\tstream.push(readBuffer);\n\t\t\t\t}\n\n\t\t\t\tresolve({\n\t\t\t\t\tstream,\n\t\t\t\t\ttype,\n\t\t\t\t});\n\t\t\t}\n\t\t};\n\n\t\tconst foundHead = (type: StreamType) => (head: Buffer) => {\n\t\t\tif (validator(head)) {\n\t\t\t\tfinish(type);\n\t\t\t}\n\t\t};\n\n\t\tconst webm = new prism.opus.WebmDemuxer();\n\t\twebm.once('error', noop);\n\t\twebm.on('head', foundHead(StreamType.WebmOpus));\n\n\t\tconst ogg = new prism.opus.OggDemuxer();\n\t\togg.once('error', noop);\n\t\togg.on('head', foundHead(StreamType.OggOpus));\n\n\t\tconst onClose = () => {\n\t\t\tif (!resolved) {\n\t\t\t\tfinish(StreamType.Arbitrary);\n\t\t\t}\n\t\t};\n\n\t\tconst onData = (buffer: Buffer) => {\n\t\t\treadBuffer = Buffer.concat([readBuffer, buffer]);\n\n\t\t\twebm.write(buffer);\n\t\t\togg.write(buffer);\n\n\t\t\tif (readBuffer.length >= probeSize) {\n\t\t\t\tstream.off('data', onData);\n\t\t\t\tstream.pause();\n\t\t\t\tprocess.nextTick(onClose);\n\t\t\t}\n\t\t};\n\n\t\tstream.once('error', reject);\n\t\tstream.on('data', onData);\n\t\tstream.once('close', onClose);\n\t\tstream.once('end', onClose);\n\t});\n}\n", "export * from './joinVoiceChannel';\nexport * from './audio/index';\nexport * from './util/index';\nexport * from './receive/index';\n\nexport {\n\tVoiceConnection,\n\ttype VoiceConnectionState,\n\tVoiceConnectionStatus,\n\ttype VoiceConnectionConnectingState,\n\ttype VoiceConnect<PERSON>D<PERSON>royedState,\n\ttype VoiceConnectionDisconnectedState,\n\ttype VoiceConnectionDisconnectedBaseState,\n\ttype VoiceConnectionDisconnectedOtherState,\n\ttype VoiceConnectionDisconnectedWebSocketState,\n\tVoiceConnectionDisconnectReason,\n\ttype VoiceConnectionReadyState,\n\ttype VoiceConnectionSignallingState,\n} from './VoiceConnection';\n\nexport { type JoinConfig, getVoiceConnection, getVoiceConnections, getGroups } from './DataStore';\n\n/**\n * The {@link https://github.com/discordjs/discord.js/blob/main/packages/voice/#readme | @discordjs/voice} version\n * that you are currently using.\n */\n// This needs to explicitly be `string` so it is not typed as a \"const string\" that gets injected by esbuild\nexport const version = '0.16.1' as string;\n"], "mappings": ";;;;;;;;;;;AAEA,SAAS,gBAAAA,qBAAoB;;;ACF7B,SAAS,sBAAsB;AAkBxB,SAAS,8BAA8B,QAAoB;AACjE,SAAO;AAAA,IACN,IAAI,eAAe;AAAA;AAAA,IAEnB,GAAG;AAAA,MACF,UAAU,OAAO;AAAA,MACjB,YAAY,OAAO;AAAA,MACnB,WAAW,OAAO;AAAA,MAClB,WAAW,OAAO;AAAA,IACnB;AAAA,EACD;AACD;AAXgB;AAchB,IAAM,SAAS,oBAAI,IAA0C;AAC7D,OAAO,IAAI,WAAW,oBAAI,IAAI,CAAC;AAE/B,SAAS,iBAAiB,OAAe;AACxC,QAAM,WAAW,OAAO,IAAI,KAAK;AACjC,MAAI;AAAU,WAAO;AACrB,QAAM,MAAM,oBAAI,IAA6B;AAC7C,SAAO,IAAI,OAAO,GAAG;AACrB,SAAO;AACR;AANS;AAcF,SAAS,YAAY;AAC3B,SAAO;AACR;AAFgB;AA0BT,SAAS,oBAAoB,QAAQ,WAAW;AACtD,SAAO,OAAO,IAAI,KAAK;AACxB;AAFgB;AAWT,SAAS,mBAAmB,SAAiB,QAAQ,WAAW;AACtE,SAAO,oBAAoB,KAAK,GAAG,IAAI,OAAO;AAC/C;AAFgB;AAIT,SAAS,uBAAuB,iBAAkC;AACxE,SAAO,oBAAoB,gBAAgB,WAAW,KAAK,GAAG,OAAO,gBAAgB,WAAW,OAAO;AACxG;AAFgB;AAIT,SAAS,qBAAqB,iBAAkC;AACtE,SAAO,iBAAiB,gBAAgB,WAAW,KAAK,EAAE,IAAI,gBAAgB,WAAW,SAAS,eAAe;AAClH;AAFgB;AAOhB,IAAM,eAAe;AAErB,IAAI;AACJ,IAAI,WAAW;AAKf,IAAM,eAA8B,CAAC;AAMrC,SAAS,iBAAiB;AACzB,MAAI,aAAa;AAAI;AAErB,cAAY;AACZ,QAAM,YAAY,aAAa,OAAO,CAAC,WAAW,OAAO,cAAc,CAAC;AAExE,aAAW,UAAU,WAAW;AAE/B,WAAO,eAAe,EAAE;AAAA,EACzB;AAEA,wBAAsB,SAAS;AAChC;AAZS;AAkBT,SAAS,sBAAsB,SAAwB;AACtD,QAAM,aAAa,QAAQ,MAAM;AAEjC,MAAI,CAAC,YAAY;AAChB,QAAI,aAAa,IAAI;AACpB,2BAAqB,WAAW,MAAM,eAAe,GAAG,WAAW,KAAK,IAAI,CAAC;AAAA,IAC9E;AAEA;AAAA,EACD;AAGA,aAAW,cAAc,EAAE;AAG3B,eAAa,MAAM,sBAAsB,OAAO,CAAC;AAClD;AAhBS;AAwBF,SAAS,eAAe,QAAqB;AACnD,SAAO,aAAa,SAAS,MAAM;AACpC;AAFgB;AAST,SAAS,eAAe,QAAqB;AACnD,MAAI,eAAe,MAAM;AAAG,WAAO;AACnC,eAAa,KAAK,MAAM;AACxB,MAAI,aAAa,WAAW,GAAG;AAC9B,eAAW,KAAK,IAAI;AACpB,iBAAa,MAAM,eAAe,CAAC;AAAA,EACpC;AAEA,SAAO;AACR;AATgB;AAcT,SAAS,kBAAkB,QAAqB;AACtD,QAAM,QAAQ,aAAa,QAAQ,MAAM;AACzC,MAAI,UAAU;AAAI;AAClB,eAAa,OAAO,OAAO,CAAC;AAC5B,MAAI,aAAa,WAAW,GAAG;AAC9B,eAAW;AACX,QAAI,uBAAuB;AAAW,mBAAa,kBAAkB;AAAA,EACtE;AACD;AARgB;;;ACjLhB,SAAS,UAAAC,eAAc;AACvB,SAAS,gBAAAC,qBAAoB;AAC7B,SAAS,gBAAAC,qBAAoB;;;ACL7B,SAAS,UAAAC,eAAc;AAQvB,IAAM,OAAO;AAAA,EACZ,iBAAiB,CAAC,YAA0B;AAAA,IAC3C,MAAM,CAAC,QAAgBC,QAAe,cAA0B;AAC/D,UAAI,QAAQ;AACX,cAAM,SAASC,QAAO,YAAY,OAAO,SAAS,OAAO,mBAAmB;AAC5E,YAAI,OAAO,2BAA2B,QAAQ,QAAQD,QAAO,SAAS;AAAG,iBAAO;AAAA,MACjF;AAEA,aAAO;AAAA,IACR;AAAA,IACA,OAAO,CAAC,YAAoBA,QAAe,cAA0B;AACpE,YAAM,SAASC,QAAO,YAAY,WAAW,SAAS,OAAO,mBAAmB;AAChF,aAAO,sBAAsB,QAAQ,YAAYD,QAAO,SAAS;AACjE,aAAO;AAAA,IACR;AAAA,IACA,QAAQ,CAAC,KAAa,SAAiBC,QAAO,YAAY,GAAG,MAAM;AAClE,aAAO,gBAAgB,MAAM;AAC7B,aAAO;AAAA,IACR;AAAA,EACD;AAAA,EACA,QAAQ,CAAC,YAA0B;AAAA,IAClC,MAAM,OAAO,IAAI;AAAA,IACjB,OAAO,OAAO,IAAI;AAAA,IAClB,QAAQ,CAAC,KAAa,SAAiBA,QAAO,YAAY,GAAG,MAAM;AAClE,aAAO,IAAI,gBAAgB,MAAM;AACjC,aAAO;AAAA,IACR;AAAA,EACD;AAAA,EACA,sBAAsB,CAAC,YAA0B;AAAA,IAChD,MAAM,OAAO;AAAA,IACb,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,EAChB;AAAA,EACA,WAAW,CAAC,eAA6B;AAAA,IACxC,MAAM,UAAU,UAAU;AAAA,IAC1B,OAAO,UAAU;AAAA,IACjB,QAAQ,UAAU;AAAA,EACnB;AACD;AAEA,IAAM,gBAAgB,6BAAM;AAC3B,QAAM,IAAI;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,EAGD;AACD,GANsB;AAQtB,IAAM,UAAmB;AAAA,EACxB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACT;AAEA,MAAM,YAAY;AACjB,aAAW,WAAW,OAAO,KAAK,IAAI,GAA4B;AACjE,QAAI;AAEH,YAAM,MAAM,UAAQ,OAAO;AAC3B,UAAI,YAAY,wBAAwB,IAAI;AAAO,cAAM,IAAI;AAC7D,aAAO,OAAO,SAAS,KAAK,OAAO,EAAE,GAAG,CAAC;AACzC;AAAA,IACD,QAAQ;AAAA,IAAC;AAAA,EACV;AACD,GAAG;;;ACxEI,IAAM,OAAO,6BAAM;AAAC,GAAP;;;ACApB,SAAS,UAAAC,eAAc;AACvB,SAAS,oBAAiC;AAC1C,SAAS,oBAAoB;AAC7B,SAAS,cAAc;AAgBhB,SAAS,iBAAiB,SAA+B;AAC/D,QAAM,SAASC,QAAO,KAAK,OAAO;AAElC,QAAM,KAAK,OAAO,MAAM,GAAG,OAAO,QAAQ,GAAG,CAAC,CAAC,EAAE,SAAS,MAAM;AAEhE,MAAI,CAAC,OAAO,EAAE,GAAG;AAChB,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACvC;AAEA,QAAM,OAAO,OAAO,aAAa,OAAO,SAAS,CAAC;AAElD,SAAO,EAAE,IAAI,KAAK;AACnB;AAZgB;AAiBhB,IAAM,sBAAsB;AAK5B,IAAM,oBAAoB,KAAK,KAAK;AAY7B,IAAM,iBAAN,cAA6B,aAAa;AAAA,EArDjD,OAqDiD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAI/B;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA,EAKT,mBAAmB;AAAA;AAAA;AAAA;AAAA,EAKV;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,QAAsB;AACxC,UAAM;AACN,SAAK,SAAS,aAAa,MAAM;AACjC,SAAK,OAAO,GAAG,SAAS,CAAC,UAAiB,KAAK,KAAK,SAAS,KAAK,CAAC;AACnE,SAAK,OAAO,GAAG,WAAW,CAAC,WAAmB,KAAK,UAAU,MAAM,CAAC;AACpE,SAAK,OAAO,GAAG,SAAS,MAAM,KAAK,KAAK,OAAO,CAAC;AAChD,SAAK,SAAS;AACd,SAAK,kBAAkBA,QAAO,MAAM,CAAC;AACrC,SAAK,oBAAoB,YAAY,MAAM,KAAK,UAAU,GAAG,mBAAmB;AAChF,iBAAa,MAAM,KAAK,UAAU,CAAC;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,UAAU,QAAsB;AAEvC,SAAK,KAAK,WAAW,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKQ,YAAY;AACnB,SAAK,gBAAgB,cAAc,KAAK,kBAAkB,CAAC;AAC3D,SAAK,KAAK,KAAK,eAAe;AAC9B,SAAK;AACL,QAAI,KAAK,mBAAmB,mBAAmB;AAC9C,WAAK,mBAAmB;AAAA,IACzB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,KAAK,QAAgB;AAC3B,SAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,MAAM,KAAK,OAAO,EAAE;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA,EAKO,UAAU;AAChB,QAAI;AACH,WAAK,OAAO,MAAM;AAAA,IACnB,QAAQ;AAAA,IAAC;AAET,kBAAc,KAAK,iBAAiB;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAa,mBAAmB,MAAqC;AACpE,WAAO,IAAI,QAAQ,CAACC,UAAS,WAAW;AACvC,YAAM,WAAW,wBAAC,YAAoB;AACrC,YAAI;AACH,cAAI,QAAQ,aAAa,CAAC,MAAM;AAAG;AACnC,gBAAM,SAAS,iBAAiB,OAAO;AACvC,eAAK,OAAO,IAAI,WAAW,QAAQ;AACnC,UAAAA,SAAQ,MAAM;AAAA,QACf,QAAQ;AAAA,QAAC;AAAA,MACV,GAPiB;AASjB,WAAK,OAAO,GAAG,WAAW,QAAQ;AAClC,WAAK,OAAO,KAAK,SAAS,MAAM,OAAO,IAAI,MAAM,6CAA6C,CAAC,CAAC;AAEhG,YAAM,kBAAkBD,QAAO,MAAM,EAAE;AAEvC,sBAAgB,cAAc,GAAG,CAAC;AAClC,sBAAgB,cAAc,IAAI,CAAC;AACnC,sBAAgB,cAAc,MAAM,CAAC;AACrC,WAAK,KAAK,eAAe;AAAA,IAC1B,CAAC;AAAA,EACF;AACD;;;AC5KA,SAAS,gBAAAE,qBAAoB;AAC7B,SAAS,oBAAoB;AAC7B,OAAO,eAAsC;AAwBtC,IAAM,iBAAN,cAA6BC,cAAa;AAAA,EA1BjD,OA0BiD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIxC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAAA;AAAA;AAAA;AAAA,EAKpB;AAAA;AAAA;AAAA;AAAA,EAKU;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOV,YAAY,SAAiB,OAAgB;AACnD,UAAM;AACN,SAAK,KAAK,IAAI,UAAU,OAAO;AAC/B,SAAK,GAAG,YAAY,CAAC,QAAQ,KAAK,UAAU,GAAG;AAC/C,SAAK,GAAG,SAAS,CAAC,QAAQ,KAAK,KAAK,QAAQ,GAAG;AAC/C,SAAK,GAAG,UAAU,CAAC,QAAsC,KAAK,KAAK,SAAS,eAAe,QAAQ,MAAM,IAAI,KAAK;AAClH,SAAK,GAAG,UAAU,CAAC,QAAQ,KAAK,KAAK,SAAS,GAAG;AAEjD,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AAEzB,SAAK,QAAQ,QAAQ,CAAC,YAAoB,KAAK,KAAK,SAAS,OAAO,IAAI;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA,EAKO,UAAU;AAChB,QAAI;AACH,WAAK,QAAQ,WAAW;AACxB,WAAK,qBAAqB,EAAE;AAC5B,WAAK,GAAG,MAAM,GAAK;AAAA,IACpB,SAAS,OAAO;AACf,YAAM,MAAM;AACZ,WAAK,KAAK,SAAS,GAAG;AAAA,IACvB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,UAAU,OAAqB;AACrC,QAAI,OAAO,MAAM,SAAS;AAAU;AAEpC,SAAK,QAAQ,MAAM,MAAM,IAAI,EAAE;AAE/B,QAAI;AACJ,QAAI;AACH,eAAS,KAAK,MAAM,MAAM,IAAI;AAAA,IAC/B,SAAS,OAAO;AACf,YAAM,MAAM;AACZ,WAAK,KAAK,SAAS,GAAG;AACtB;AAAA,IACD;AAEA,QAAI,OAAO,OAAO,aAAa,cAAc;AAC5C,WAAK,mBAAmB,KAAK,IAAI;AACjC,WAAK,mBAAmB;AACxB,WAAK,OAAO,KAAK,mBAAmB,KAAK;AAAA,IAC1C;AAEA,SAAK,KAAK,UAAU,MAAM;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,WAAW,QAAa;AAC9B,QAAI;AACH,YAAM,cAAc,KAAK,UAAU,MAAM;AACzC,WAAK,QAAQ,MAAM,WAAW,EAAE;AAChC,WAAK,GAAG,KAAK,WAAW;AAAA,IACzB,SAAS,OAAO;AACf,YAAM,MAAM;AACZ,WAAK,KAAK,SAAS,GAAG;AAAA,IACvB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKQ,gBAAgB;AACvB,SAAK,oBAAoB,KAAK,IAAI;AAClC,SAAK;AACL,UAAMC,SAAQ,KAAK;AACnB,SAAK,WAAW;AAAA,MACf,IAAI,aAAa;AAAA;AAAA,MAEjB,GAAGA;AAAA,IACJ,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,qBAAqB,IAAY;AACvC,QAAI,KAAK,sBAAsB;AAAW,oBAAc,KAAK,iBAAiB;AAC9E,QAAI,KAAK,GAAG;AACX,WAAK,oBAAoB,YAAY,MAAM;AAC1C,YAAI,KAAK,sBAAsB,KAAK,KAAK,oBAAoB,GAAG;AAE/D,eAAK,GAAG,MAAM;AACd,eAAK,qBAAqB,EAAE;AAAA,QAC7B;AAEA,aAAK,cAAc;AAAA,MACpB,GAAG,EAAE;AAAA,IACN;AAAA,EACD;AACD;;;AJlKA,IAAM,WAAW;AACjB,IAAM,gBAAiB,OAAS,MAAO;AACvC,IAAM,iBAAiB,KAAK,KAAK;AAE1B,IAAM,6BAA6B,CAAC,0BAA0B,4BAA4B,mBAAmB;AAyIpH,IAAM,QAAQC,QAAO,MAAM,EAAE;AAmB7B,SAAS,eAAe,OAAwB;AAC/C,SAAO,KAAK,UAAU;AAAA,IACrB,GAAG;AAAA,IACH,IAAI,QAAQ,IAAI,OAAO,IAAI;AAAA,IAC3B,KAAK,QAAQ,IAAI,OAAO,KAAK;AAAA,EAC9B,CAAC;AACF;AANS;AAaT,SAAS,qBAAqB,SAA2B;AACxD,QAAM,SAAS,QAAQ,KAAK,CAACC,YAAW,2BAA2B,SAASA,OAAM,CAAC;AACnF,MAAI,CAAC,QAAQ;AACZ,UAAM,IAAI,MAAM,sDAAsD,QAAQ,KAAK,IAAI,CAAC,EAAE;AAAA,EAC3F;AAEA,SAAO;AACR;AAPS;AAcT,SAAS,WAAW,cAAsB;AACzC,SAAO,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,YAAY;AACpD;AAFS;AAOF,IAAM,aAAN,cAAyBC,cAAa;AAAA,EA/M7C,OA+M6C;AAAA;AAAA;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAKS;AAAA;AAAA;AAAA;AAAA,EAKV,YAAY,SAA4B,OAAgB;AAC9D,UAAM;AAEN,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAE3C,SAAK,QAAQ,QAAQ,CAAC,YAAoB,KAAK,KAAK,SAAS,OAAO,IAAI;AAExE,SAAK,SAAS;AAAA,MACb,MAAM;AAAA,MACN,IAAI,KAAK,gBAAgB,QAAQ,QAAQ;AAAA,MACzC,mBAAmB;AAAA,IACpB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKO,UAAU;AAChB,SAAK,QAAQ;AAAA,MACZ,MAAM;AAAA,IACP;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,QAAyB;AACnC,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,MAAM,UAA2B;AAC3C,UAAM,QAAQ,QAAQ,IAAI,KAAK,QAAQ,IAAI;AAC3C,UAAM,QAAQ,QAAQ,IAAI,UAAU,IAAI;AACxC,QAAI,SAAS,UAAU,OAAO;AAE7B,YAAM,IAAI,SAAS,KAAK,SAAS;AACjC,YAAM,GAAG,SAAS,IAAI;AACtB,YAAM,IAAI,SAAS,KAAK,YAAY;AACpC,YAAM,IAAI,QAAQ,KAAK,QAAQ;AAC/B,YAAM,IAAI,UAAU,KAAK,UAAU;AACnC,YAAM,IAAI,SAAS,KAAK,SAAS;AACjC,YAAM,QAAQ;AAAA,IACf;AAEA,UAAM,SAAS,QAAQ,IAAI,KAAK,QAAQ,KAAK;AAC7C,UAAM,SAAS,QAAQ,IAAI,UAAU,KAAK;AAE1C,QAAI,UAAU,WAAW,QAAQ;AAChC,aAAO,GAAG,SAAS,IAAI;AACvB,aAAO,IAAI,SAAS,KAAK,YAAY;AACrC,aAAO,IAAI,SAAS,KAAK,UAAU;AACnC,aAAO,IAAI,SAAS,KAAK,UAAU;AACnC,aAAO,QAAQ;AAAA,IAChB;AAEA,UAAM,WAAW,KAAK;AACtB,SAAK,SAAS;AACd,SAAK,KAAK,eAAe,UAAU,QAAQ;AAE3C,SAAK,QAAQ;AAAA,OAAuB,eAAe,QAAQ,CAAC;AAAA,KAAQ,eAAe,QAAQ,CAAC,EAAE;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,gBAAgB,UAAkB;AACzC,UAAM,KAAK,IAAI,eAAe,SAAS,QAAQ,QAAQ,QAAQ,KAAK,KAAK,CAAC;AAE1E,OAAG,GAAG,SAAS,KAAK,YAAY;AAChC,OAAG,KAAK,QAAQ,KAAK,QAAQ;AAC7B,OAAG,GAAG,UAAU,KAAK,UAAU;AAC/B,OAAG,KAAK,SAAS,KAAK,SAAS;AAC/B,OAAG,GAAG,SAAS,KAAK,SAAS;AAE7B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,aAAa,OAAc;AAClC,SAAK,KAAK,SAAS,KAAK;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMQ,WAAW;AAClB,QAAI,KAAK,MAAM,SAAS,mBAAgC;AACvD,YAAM,SAAS;AAAA,QACd,IAAIC,cAAa;AAAA,QACjB,GAAG;AAAA,UACF,WAAW,KAAK,MAAM,kBAAkB;AAAA,UACxC,SAAS,KAAK,MAAM,kBAAkB;AAAA,UACtC,YAAY,KAAK,MAAM,kBAAkB;AAAA,UACzC,OAAO,KAAK,MAAM,kBAAkB;AAAA,QACrC;AAAA,MACD;AACA,WAAK,MAAM,GAAG,WAAW,MAAM;AAC/B,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,MAAM;AAAA,MACP;AAAA,IACD,WAAW,KAAK,MAAM,SAAS,kBAA+B;AAC7D,YAAM,SAAS;AAAA,QACd,IAAIA,cAAa;AAAA,QACjB,GAAG;AAAA,UACF,WAAW,KAAK,MAAM,kBAAkB;AAAA,UACxC,YAAY,KAAK,MAAM,kBAAkB;AAAA,UACzC,OAAO,KAAK,MAAM,kBAAkB;AAAA,QACrC;AAAA,MACD;AACA,WAAK,MAAM,GAAG,WAAW,MAAM;AAAA,IAChC;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,UAAU,EAAE,KAAK,GAAe;AACvC,UAAM,YAAY,SAAS,QAAS,OAAO;AAC3C,QAAI,aAAa,KAAK,MAAM,SAAS,eAA4B;AAChE,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,MAAM;AAAA,QACN,IAAI,KAAK,gBAAgB,KAAK,MAAM,kBAAkB,QAAQ;AAAA,MAC/D;AAAA,IACD,WAAW,KAAK,MAAM,SAAS,gBAA6B;AAC3D,WAAK,QAAQ;AACb,WAAK,KAAK,SAAS,IAAI;AAAA,IACxB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKQ,aAAa;AACpB,QAAI,KAAK,MAAM,SAAS,eAA4B;AACnD,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,MAAM;AAAA,QACN,IAAI,KAAK,gBAAgB,KAAK,MAAM,kBAAkB,QAAQ;AAAA,MAC/D;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,WAAW,QAAa;AAC/B,QAAI,OAAO,OAAOA,cAAa,SAAS,KAAK,MAAM,SAAS,gBAA6B;AACxF,WAAK,MAAM,GAAG,qBAAqB,OAAO,EAAE,kBAAkB;AAAA,IAC/D,WAAW,OAAO,OAAOA,cAAa,SAAS,KAAK,MAAM,SAAS,qBAAkC;AACpG,YAAM,EAAE,IAAI,MAAM,MAAM,MAAM,IAAI,OAAO;AAEzC,YAAM,MAAM,IAAI,eAAe,EAAE,IAAI,KAAK,CAAC;AAC3C,UAAI,GAAG,SAAS,KAAK,YAAY;AACjC,UAAI,GAAG,SAAS,KAAK,UAAU;AAC/B,UAAI,KAAK,SAAS,KAAK,UAAU;AACjC,UACE,mBAAmB,IAAI,EAEvB,KAAK,CAAC,gBAAgB;AACtB,YAAI,KAAK,MAAM,SAAS;AAAqC;AAC7D,aAAK,MAAM,GAAG,WAAW;AAAA,UACxB,IAAIA,cAAa;AAAA,UACjB,GAAG;AAAA,YACF,UAAU;AAAA,YACV,MAAM;AAAA,cACL,SAAS,YAAY;AAAA,cACrB,MAAM,YAAY;AAAA,cAClB,MAAM,qBAAqB,KAAK;AAAA,YACjC;AAAA,UACD;AAAA,QACD,CAAC;AACD,aAAK,QAAQ;AAAA,UACZ,GAAG,KAAK;AAAA,UACR,MAAM;AAAA,QACP;AAAA,MACD,CAAC,EAEA,MAAM,CAAC,UAAiB,KAAK,KAAK,SAAS,KAAK,CAAC;AAEnD,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA,gBAAgB;AAAA,UACf;AAAA,QACD;AAAA,MACD;AAAA,IACD,WACC,OAAO,OAAOA,cAAa,sBAC3B,KAAK,MAAM,SAAS,2BACnB;AACD,YAAM,EAAE,MAAM,gBAAgB,YAAY,UAAU,IAAI,OAAO;AAC/D,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,MAAM;AAAA,QACN,gBAAgB;AAAA,UACf,GAAG,KAAK,MAAM;AAAA,UACd;AAAA,UACA,WAAW,IAAI,WAAW,SAAS;AAAA,UACnC,UAAU,WAAW,EAAE;AAAA,UACvB,WAAW,WAAW,EAAE;AAAA,UACxB,OAAO;AAAA,UACP,aAAaH,QAAO,MAAM,EAAE;AAAA,UAC5B,UAAU;AAAA,UACV,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,IACD,WAAW,OAAO,OAAOG,cAAa,WAAW,KAAK,MAAM,SAAS,kBAA+B;AACnG,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,MAAM;AAAA,MACP;AACA,WAAK,MAAM,eAAe,WAAW;AAAA,IACtC;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,UAAU,SAAiB;AAClC,SAAK,QAAQ,QAAQ,OAAO,EAAE;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,WAAW,SAAiB;AACnC,SAAK,QAAQ,SAAS,OAAO,EAAE;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYO,mBAAmB,YAAoB;AAC7C,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM,SAAS;AAA4B;AAC/C,UAAM,iBAAiB,KAAK,kBAAkB,YAAY,MAAM,cAAc;AAC9E,WAAO,MAAM;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,gBAAgB;AACtB,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM,SAAS;AAA4B,aAAO;AACtD,QAAI,MAAM,mBAAmB,QAAW;AACvC,WAAK,gBAAgB,MAAM,cAAc;AACzC,YAAM,iBAAiB;AACvB,aAAO;AAAA,IACR;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,gBAAgB,aAAqB;AAC5C,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM,SAAS;AAA4B;AAC/C,UAAM,EAAE,eAAe,IAAI;AAC3B,mBAAe;AACf,mBAAe;AACf,mBAAe,aAAa;AAC5B,QAAI,eAAe,YAAY,KAAK;AAAI,qBAAe,WAAW;AAClE,QAAI,eAAe,aAAa,KAAK;AAAI,qBAAe,YAAY;AACpE,SAAK,YAAY,IAAI;AACrB,UAAM,IAAI,KAAK,WAAW;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,YAAY,UAAmB;AACrC,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM,SAAS;AAA4B;AAC/C,QAAI,MAAM,eAAe,aAAa;AAAU;AAChD,UAAM,eAAe,WAAW;AAChC,UAAM,GAAG,WAAW;AAAA,MACnB,IAAIA,cAAa;AAAA,MACjB,GAAG;AAAA,QACF,UAAU,WAAW,IAAI;AAAA,QACzB,OAAO;AAAA,QACP,MAAM,MAAM,eAAe;AAAA,MAC5B;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,kBAAkB,YAAoB,gBAAgC;AAC7E,UAAM,eAAeH,QAAO,MAAM,EAAE;AACpC,iBAAa,CAAC,IAAI;AAClB,iBAAa,CAAC,IAAI;AAElB,UAAM,EAAE,UAAU,WAAW,KAAK,IAAI;AAEtC,iBAAa,YAAY,UAAU,GAAG,CAAC;AACvC,iBAAa,YAAY,WAAW,GAAG,CAAC;AACxC,iBAAa,YAAY,MAAM,GAAG,CAAC;AAEnC,iBAAa,KAAK,OAAO,GAAG,GAAG,EAAE;AACjC,WAAOA,QAAO,OAAO,CAAC,cAAc,GAAG,KAAK,kBAAkB,YAAY,cAAc,CAAC,CAAC;AAAA,EAC3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQQ,kBAAkB,YAAoB,gBAAgC;AAC7E,UAAM,EAAE,WAAW,eAAe,IAAI;AAEtC,QAAI,mBAAmB,0BAA0B;AAChD,qBAAe;AACf,UAAI,eAAe,QAAQ;AAAgB,uBAAe,QAAQ;AAClE,qBAAe,YAAY,cAAc,eAAe,OAAO,CAAC;AAChE,aAAO;AAAA,QACI,QAAQ,MAAM,YAAY,eAAe,aAAa,SAAS;AAAA,QACzE,eAAe,YAAY,MAAM,GAAG,CAAC;AAAA,MACtC;AAAA,IACD,WAAW,mBAAmB,4BAA4B;AACzD,YAAM,SAAmB,QAAQ,OAAO,IAAI,eAAe,WAAW;AACtE,aAAO,CAAW,QAAQ,MAAM,YAAY,QAAQ,SAAS,GAAG,MAAM;AAAA,IACvE;AAEA,WAAO,CAAW,QAAQ,MAAM,YAAY,OAAO,SAAS,CAAC;AAAA,EAC9D;AACD;;;AKhlBA,SAAS,UAAAI,eAAc;AACvB,SAAS,gBAAAC,qBAAoB;;;ACF7B,SAAS,gBAAsC;;;ACA/C,SAAS,UAAAC,eAAc;AACvB,SAAS,gBAAAC,qBAAoB;;;ACItB,IAAM,mBAAN,cAA+B,MAAM;AAAA,EAN5C,OAM4C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAI3B;AAAA,EAET,YAAY,OAAc,UAAyB;AACzD,UAAM,MAAM,OAAO;AACnB,SAAK,WAAW;AAChB,SAAK,OAAO,MAAM;AAClB,SAAK,QAAQ,MAAM;AAAA,EACpB;AACD;;;ACVO,IAAM,qBAAN,MAAyB;AAAA,EARhC,OAQgC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIf;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EAET,YAAY,YAA6B,QAAqB;AACpE,SAAK,aAAa;AAClB,SAAK,SAAS;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,cAAc;AACpB,SAAK,WAAW,uBAAuB,EAAE,IAAI;AAC7C,SAAK,OAAO,aAAa,EAAE,IAAI;AAAA,EAChC;AACD;;;AFrBO,IAAM,gBAAgBC,QAAO,KAAK,CAAC,KAAM,KAAM,GAAI,CAAC;AAMpD,IAAK,uBAAL,kBAAKC,0BAAL;AAIN,EAAAA,sBAAA,WAAQ;AAKR,EAAAA,sBAAA,UAAO;AAKP,EAAAA,sBAAA,UAAO;AAdI,SAAAA;AAAA,GAAA;AAiBL,IAAK,oBAAL,kBAAKC,uBAAL;AAIN,EAAAA,mBAAA,gBAAa;AAKb,EAAAA,mBAAA,eAAY;AAKZ,EAAAA,mBAAA,UAAO;AAKP,EAAAA,mBAAA,YAAS;AAKT,EAAAA,mBAAA,aAAU;AAxBC,SAAAA;AAAA,GAAA;AAiKZ,SAASC,gBAAe,OAAyB;AAChD,SAAO,KAAK,UAAU;AAAA,IACrB,GAAG;AAAA,IACH,UAAU,QAAQ,IAAI,OAAO,UAAU;AAAA,IACvC,aAAa,QAAQ,IAAI,OAAO,aAAa;AAAA,EAC9C,CAAC;AACF;AANS,OAAAA,iBAAA;AAkBF,IAAM,cAAN,cAA0BC,cAAa;AAAA,EArN9C,OAqN8C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMS,cAAoC,CAAC;AAAA;AAAA;AAAA;AAAA,EAKrC;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA;AAAA;AAAA,EAKV,YAAY,UAAoC,CAAC,GAAG;AAC1D,UAAM;AACN,SAAK,SAAS,EAAE,QAAQ,kBAAuB;AAC/C,SAAK,YAAY;AAAA,MAChB,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,GAAG,QAAQ;AAAA,IACZ;AACA,SAAK,QAAQ,QAAQ,UAAU,QAAQ,OAAO,CAAC,YAAoB,KAAK,KAAK,SAAS,OAAO;AAAA,EAC9F;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,WAAW;AACrB,WAAO,KAAK,YACV,OAAO,CAAC,EAAE,WAAW,MAAM,WAAW,MAAM,8BAAsC,EAClF,IAAI,CAAC,EAAE,WAAW,MAAM,UAAU;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYQ,UAAU,YAA6B;AAC9C,UAAM,uBAAuB,KAAK,YAAY,KAAK,CAAC,iBAAiB,aAAa,eAAe,UAAU;AAC3G,QAAI,CAAC,sBAAsB;AAC1B,YAAM,eAAe,IAAI,mBAAmB,YAAY,IAAI;AAC5D,WAAK,YAAY,KAAK,YAAY;AAClC,mBAAa,MAAM,KAAK,KAAK,aAAa,YAAY,CAAC;AACvD,aAAO;AAAA,IACR;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWQ,YAAY,cAAkC;AACrD,UAAM,QAAQ,KAAK,YAAY,QAAQ,YAAY;AACnD,UAAM,SAAS,UAAU;AACzB,QAAI,QAAQ;AACX,WAAK,YAAY,OAAO,OAAO,CAAC;AAChC,mBAAa,WAAW,YAAY,KAAK;AACzC,WAAK,KAAK,eAAe,YAAY;AAAA,IACtC;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,QAAQ;AAClB,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,MAAM,UAA4B;AAC5C,UAAM,WAAW,KAAK;AACtB,UAAM,cAAc,QAAQ,IAAI,UAAU,UAAU;AAEpD,QAAI,SAAS,WAAW,qBAA0B,SAAS,aAAa,aAAa;AACpF,eAAS,SAAS,WAAW,GAAG,SAAS,IAAI;AAC7C,eAAS,SAAS,WAAW,IAAI,SAAS,SAAS,aAAa;AAChE,eAAS,SAAS,cAAc;AAChC,eAAS,SAAS,WAAW,QAAQ;AACrC,eAAS,SAAS,WAAW,KAAK;AAAA,IACnC;AAGA,QACC,SAAS,WAAW,gCACnB,SAAS,WAAW,+BAA+B,SAAS,aAAa,SAAS,WAClF;AACD,eAAS,SAAS,WAAW,IAAI,OAAO,SAAS,iBAAiB;AAClE,eAAS,SAAS,WAAW,IAAI,SAAS,SAAS,iBAAiB;AACpE,eAAS,SAAS,WAAW,IAAI,UAAU,SAAS,iBAAiB;AACrE,eAAS,SAAS,WAAW,IAAI,YAAY,SAAS,kBAAkB;AAAA,IACzE;AAGA,QAAI,SAAS,WAAW,mBAAwB;AAC/C,WAAK,oBAAoB;AACzB,wBAAkB,IAAI;AAAA,IACvB;AAGA,QAAI,aAAa;AAChB,qBAAe,IAAI;AAAA,IACpB;AAGA,UAAM,qBACL,SAAS,WAAW,qBACpB,SAAS,WAAW,2BACpB,SAAS,aAAa,SAAS;AAEhC,SAAK,SAAS;AAEd,SAAK,KAAK,eAAe,UAAU,KAAK,MAAM;AAC9C,QAAI,SAAS,WAAW,SAAS,UAAU,oBAAoB;AAC9D,WAAK,KAAK,SAAS,QAAQ,UAAU,KAAK,MAAa;AAAA,IACxD;AAEA,SAAK,QAAQ;AAAA,OAAuBD,gBAAe,QAAQ,CAAC;AAAA,KAAQA,gBAAe,QAAQ,CAAC,EAAE;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeO,KAAe,UAAmC;AACxD,QAAI,SAAS,OAAO;AACnB,YAAM,IAAI,MAAM,gDAAgD;AAAA,IACjE;AAEA,QAAI,SAAS,aAAa;AACzB,UAAI,SAAS,gBAAgB,MAAM;AAClC;AAAA,MACD;AAEA,YAAM,IAAI,MAAM,2DAA2D;AAAA,IAC5E;AAEA,aAAS,cAAc;AAIvB,UAAM,gBAAgB,wBAAC,UAAiB;AACvC,UAAI,KAAK,MAAM,WAAW,mBAAwB;AACjD,aAAK,KAAK,SAAS,IAAI,iBAAiB,OAAO,KAAK,MAAM,QAAQ,CAAC;AAAA,MACpE;AAEA,UAAI,KAAK,MAAM,WAAW,qBAA0B,KAAK,MAAM,aAAa,UAAU;AACrF,aAAK,QAAQ;AAAA,UACZ,QAAQ;AAAA,QACT;AAAA,MACD;AAAA,IACD,GAVsB;AAYtB,aAAS,WAAW,KAAK,SAAS,aAAa;AAE/C,QAAI,SAAS,SAAS;AACrB,WAAK,QAAQ;AAAA,QACZ,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB;AAAA,QACA;AAAA,MACD;AAAA,IACD,OAAO;AACN,YAAM,qBAAqB,6BAAM;AAChC,YAAI,KAAK,MAAM,WAAW,+BAA+B,KAAK,MAAM,aAAa,UAAU;AAC1F,eAAK,QAAQ;AAAA,YACZ,QAAQ;AAAA,YACR,cAAc;AAAA,YACd,kBAAkB;AAAA,YAClB;AAAA,YACA;AAAA,UACD;AAAA,QACD;AAAA,MACD,GAV2B;AAY3B,YAAM,oBAAoB,6BAAM;AAC/B,YAAI,KAAK,MAAM,WAAW,+BAA+B,KAAK,MAAM,aAAa,UAAU;AAC1F,eAAK,QAAQ;AAAA,YACZ,QAAQ;AAAA,UACT;AAAA,QACD;AAAA,MACD,GAN0B;AAQ1B,eAAS,WAAW,KAAK,YAAY,kBAAkB;AAEvD,eAAS,WAAW,KAAK,OAAO,iBAAiB;AACjD,eAAS,WAAW,KAAK,SAAS,iBAAiB;AACnD,eAAS,WAAW,KAAK,UAAU,iBAAiB;AAEpD,WAAK,QAAQ;AAAA,QACZ,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,MAAM,qBAAqB,MAAM;AACvC,QAAI,KAAK,MAAM,WAAW;AAA2B,aAAO;AAC5D,SAAK,QAAQ;AAAA,MACZ,GAAG,KAAK;AAAA,MACR,QAAQ;AAAA,MACR,yBAAyB,qBAAqB,IAAI;AAAA,IACnD;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,UAAU;AAChB,QAAI,KAAK,MAAM,WAAW;AAA0B,aAAO;AAC3D,SAAK,QAAQ;AAAA,MACZ,GAAG,KAAK;AAAA,MACR,QAAQ;AAAA,MACR,cAAc;AAAA,IACf;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,KAAK,QAAQ,OAAO;AAC1B,QAAI,KAAK,MAAM,WAAW;AAAwB,aAAO;AACzD,QAAI,SAAS,KAAK,MAAM,SAAS,yBAAyB,GAAG;AAC5D,WAAK,QAAQ;AAAA,QACZ,QAAQ;AAAA,MACT;AAAA,IACD,WAAW,KAAK,MAAM,SAAS,qBAAqB,IAAI;AACvD,WAAK,MAAM,SAAS,mBAAmB,KAAK,MAAM,SAAS;AAAA,IAC5D;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,gBAAgB;AACtB,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM,WAAW,qBAA0B,MAAM,WAAW;AAA6B,aAAO;AAGpG,QAAI,CAAC,MAAM,SAAS,UAAU;AAC7B,WAAK,QAAQ;AAAA,QACZ,QAAQ;AAAA,MACT;AACA,aAAO;AAAA,IACR;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,gBAAgB;AACvB,UAAM,QAAQ,KAAK;AAGnB,QAAI,MAAM,WAAW,qBAA0B,MAAM,WAAW;AAA6B;AAG7F,eAAW,cAAc,KAAK,UAAU;AACvC,iBAAW,cAAc;AAAA,IAC1B;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQQ,eAAe;AACtB,UAAM,QAAQ,KAAK;AAGnB,QAAI,MAAM,WAAW,qBAA0B,MAAM,WAAW;AAA6B;AAG7F,UAAM,WAAW,KAAK;AAItB,QAAI,MAAM,WAAW,iCAAgC,SAAS,SAAS,GAAG;AACzE,WAAK,QAAQ;AAAA,QACZ,GAAG;AAAA,QACH,QAAQ;AAAA,QACR,cAAc;AAAA,MACf;AAAA,IACD;AAIA,QAAI,MAAM,WAAW,yBAA4B,MAAM,WAAW,+BAA8B;AAC/F,UAAI,MAAM,0BAA0B,GAAG;AACtC,cAAM;AACN,aAAK,eAAe,eAAe,UAAU,KAAK;AAClD,YAAI,MAAM,4BAA4B,GAAG;AACxC,eAAK,oBAAoB;AAAA,QAC1B;AAAA,MACD;AAEA;AAAA,IACD;AAGA,QAAI,SAAS,WAAW,GAAG;AAC1B,UAAI,KAAK,UAAU,iBAAiB,qBAA4B;AAC/D,aAAK,QAAQ;AAAA,UACZ,GAAG;AAAA,UACH,QAAQ;AAAA,UACR,yBAAyB;AAAA,QAC1B;AACA;AAAA,MACD,WAAW,KAAK,UAAU,iBAAiB,mBAA2B;AACrE,aAAK,KAAK,IAAI;AAAA,MACf;AAAA,IACD;AAOA,UAAM,SAAwB,MAAM,SAAS,KAAK;AAElD,QAAI,MAAM,WAAW,yBAA2B;AAC/C,UAAI,QAAQ;AACX,aAAK,eAAe,QAAQ,UAAU,KAAK;AAC3C,cAAM,eAAe;AAAA,MACtB,OAAO;AACN,aAAK,eAAe,eAAe,UAAU,KAAK;AAClD,cAAM;AACN,YAAI,MAAM,gBAAgB,KAAK,UAAU,iBAAiB;AACzD,eAAK,KAAK;AAAA,QACX;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMQ,sBAAsB;AAC7B,eAAW,EAAE,WAAW,KAAK,KAAK,aAAa;AAC9C,iBAAW,YAAY,KAAK;AAAA,IAC7B;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,eACP,QACA,WACA,OACC;AACD,UAAM,oBAAoB;AAC1B,eAAW,cAAc,WAAW;AACnC,iBAAW,mBAAmB,MAAM;AAAA,IACrC;AAAA,EACD;AACD;AAKO,SAAS,kBAAkB,SAAoC;AACrE,SAAO,IAAI,YAAY,OAAO;AAC/B;AAFgB;;;ADhoBT,IAAK,kBAAL,kBAAKE,qBAAL;AAIN,EAAAA,kCAAA;AAKA,EAAAA,kCAAA;AAKA,EAAAA,kCAAA;AAdW,SAAAA;AAAA,GAAA;AA8BL,SAAS,yCAAoE;AACnF,SAAO;AAAA,IACN,KAAK;AAAA,MACJ,UAAU;AAAA,IACX;AAAA,EACD;AACD;AANgB;AAYT,IAAM,qBAAN,cAAiC,SAAS;AAAA,EAjDjD,OAiDiD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIhC;AAAA,EAER;AAAA,EAED,YAAY,EAAE,KAAK,GAAG,QAAQ,GAA8B;AAClE,UAAM;AAAA,MACL,GAAG;AAAA,MACH,YAAY;AAAA,IACb,CAAC;AAED,SAAK,MAAM;AAAA,EACZ;AAAA,EAEgB,KAAK,QAAuB;AAC3C,QACC,WACC,KAAK,IAAI,aAAa,2BACrB,KAAK,IAAI,aAAa,yBACrB,OAAO,QAAQ,aAAa,MAAM,KAAK,KAAK,eAAe,UAC7D;AACD,WAAK,gBAAgB,KAAK,GAAG;AAAA,IAC9B;AAEA,WAAO,MAAM,KAAK,MAAM;AAAA,EACzB;AAAA,EAEQ,gBAAgB,KAAyC;AAChE,QAAI,KAAK,YAAY;AACpB,mBAAa,KAAK,UAAU;AAAA,IAC7B;AAEA,SAAK,aAAa,WAAW,MAAM,KAAK,KAAK,IAAI,GAAG,IAAI,QAAQ;AAAA,EACjE;AAAA,EAEgB,QAAQ;AAAA,EAAC;AAC1B;;;AIxFA,SAAS,gBAAAC,qBAAoB;AAgCtB,IAAM,UAAN,cAAsBC,cAAa;AAAA,EAhC1C,OAgC0C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIxB;AAAA,EAEV,cAAc;AACpB,UAAM;AACN,SAAK,MAAM,oBAAI,IAAI;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,OAAO,MAAqB;AAClC,UAAM,WAAW,KAAK,IAAI,IAAI,KAAK,SAAS;AAE5C,UAAM,WAAW;AAAA,MAChB,GAAG,KAAK,IAAI,IAAI,KAAK,SAAS;AAAA,MAC9B,GAAG;AAAA,IACJ;AAEA,SAAK,IAAI,IAAI,KAAK,WAAW,QAAQ;AACrC,QAAI,CAAC;AAAU,WAAK,KAAK,UAAU,QAAQ;AAC3C,SAAK,KAAK,UAAU,UAAU,QAAQ;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,IAAI,QAAyB;AACnC,QAAI,OAAO,WAAW,UAAU;AAC/B,aAAO,KAAK,IAAI,IAAI,MAAM;AAAA,IAC3B;AAEA,eAAW,QAAQ,KAAK,IAAI,OAAO,GAAG;AACrC,UAAI,KAAK,WAAW,QAAQ;AAC3B,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,OAAO,QAAyB;AACtC,QAAI,OAAO,WAAW,UAAU;AAC/B,YAAM,WAAW,KAAK,IAAI,IAAI,MAAM;AACpC,UAAI,UAAU;AACb,aAAK,IAAI,OAAO,MAAM;AACtB,aAAK,KAAK,UAAU,QAAQ;AAAA,MAC7B;AAEA,aAAO;AAAA,IACR;AAEA,eAAW,CAAC,WAAW,IAAI,KAAK,KAAK,IAAI,QAAQ,GAAG;AACnD,UAAI,KAAK,WAAW,QAAQ;AAC3B,aAAK,IAAI,OAAO,SAAS;AACzB,aAAK,KAAK,UAAU,IAAI;AACxB,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AACD;;;AC1GA,SAAS,gBAAAC,qBAAoB;AAqBtB,IAAM,cAAN,MAAM,qBAAoBC,cAAa;AAAA,EAtB9C,OAsB8C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAI7C,OAAuB,QAAQ;AAAA;AAAA;AAAA;AAAA,EAKf;AAAA,EAEC;AAAA,EAEV,cAAc;AACpB,UAAM;AACN,SAAK,QAAQ,oBAAI,IAAI;AACrB,SAAK,mBAAmB,oBAAI,IAAI;AAAA,EACjC;AAAA,EAEO,SAAS,QAAgB;AAC/B,UAAM,UAAU,KAAK,iBAAiB,IAAI,MAAM;AAChD,QAAI,SAAS;AACZ,mBAAa,OAAO;AAAA,IACrB,OAAO;AACN,WAAK,MAAM,IAAI,QAAQ,KAAK,IAAI,CAAC;AACjC,WAAK,KAAK,SAAS,MAAM;AAAA,IAC1B;AAEA,SAAK,aAAa,MAAM;AAAA,EACzB;AAAA,EAEQ,aAAa,QAAgB;AACpC,SAAK,iBAAiB;AAAA,MACrB;AAAA,MACA,WAAW,MAAM;AAChB,aAAK,KAAK,OAAO,MAAM;AACvB,aAAK,iBAAiB,OAAO,MAAM;AACnC,aAAK,MAAM,OAAO,MAAM;AAAA,MACzB,GAAG,aAAY,KAAK;AAAA,IACrB;AAAA,EACD;AACD;;;AN1CO,IAAM,gBAAN,MAAoB;AAAA,EArB3B,OAqB2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIV;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT;AAAA;AAAA;AAAA;AAAA,EAKS;AAAA,EAET,YAAY,iBAAkC;AACpD,SAAK,kBAAkB;AACvB,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,WAAW,IAAI,YAAY;AAChC,SAAK,gBAAgB,oBAAI,IAAI;AAC7B,SAAK,iBAAiB,CAAC;AAEvB,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,WAAW,QAAa;AAC9B,QAAI,OAAO,OAAOC,cAAa,oBAAoB,OAAO,OAAO,GAAG,YAAY,UAAU;AACzF,WAAK,QAAQ,OAAO,OAAO,EAAE,OAAO;AAAA,IACrC,WACC,OAAO,OAAOA,cAAa,YAC3B,OAAO,OAAO,GAAG,YAAY,YAC7B,OAAO,OAAO,GAAG,SAAS,UACzB;AACD,WAAK,QAAQ,OAAO,EAAE,QAAQ,OAAO,EAAE,SAAS,WAAW,OAAO,EAAE,KAAK,CAAC;AAAA,IAC3E,WACC,OAAO,OAAOA,cAAa,iBAC3B,OAAO,OAAO,GAAG,YAAY,YAC7B,OAAO,OAAO,GAAG,eAAe,UAC/B;AACD,WAAK,QAAQ,OAAO;AAAA,QACnB,QAAQ,OAAO,EAAE;AAAA,QACjB,WAAW,OAAO,EAAE;AAAA,QACpB,WAAW,OAAO,EAAE,eAAe,IAAI,SAAY,OAAO,EAAE;AAAA,MAC7D,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EAEQ,QAAQ,QAAgB,MAAcC,QAAe,WAAuB;AAEnF,QAAI;AACJ,QAAI,SAAS,0BAA0B;AACtC,aAAO,KAAKA,QAAO,GAAG,OAAO,SAAS,CAAC;AACvC,YAAM,OAAO,SAAS;AAAA,IACvB,WAAW,SAAS,4BAA4B;AAC/C,aAAO,KAAKA,QAAO,GAAG,OAAO,SAAS,EAAE;AACxC,YAAM,OAAO,SAAS;AAAA,IACvB,OAAO;AACN,aAAO,KAAKA,QAAO,GAAG,GAAG,EAAE;AAAA,IAC5B;AAGA,UAAM,YAAY,QAAQ,KAAK,OAAO,MAAM,IAAI,GAAG,GAAGA,QAAO,SAAS;AACtE,QAAI,CAAC;AAAW;AAChB,WAAOC,QAAO,KAAK,SAAS;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWQ,YAAY,QAAgB,MAAcD,QAAe,WAAuB;AACvF,QAAI,SAAS,KAAK,QAAQ,QAAQ,MAAMA,QAAO,SAAS;AACxD,QAAI,CAAC;AAAQ;AAGb,QAAI,OAAO,CAAC,MAAM,OAAQ,OAAO,CAAC,MAAM,KAAM;AAC7C,YAAM,wBAAwB,OAAO,aAAa,CAAC;AACnD,eAAS,OAAO,SAAS,IAAI,IAAI,qBAAqB;AAAA,IACvD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,aAAa,KAAa;AAChC,QAAI,IAAI,UAAU;AAAG;AACrB,UAAM,OAAO,IAAI,aAAa,CAAC;AAE/B,UAAM,WAAW,KAAK,QAAQ,IAAI,IAAI;AACtC,QAAI,CAAC;AAAU;AAEf,SAAK,SAAS,SAAS,SAAS,MAAM;AAEtC,UAAM,SAAS,KAAK,cAAc,IAAI,SAAS,MAAM;AACrD,QAAI,CAAC;AAAQ;AAEb,QAAI,KAAK,eAAe,kBAAkB,KAAK,eAAe,eAAe,KAAK,eAAe,WAAW;AAC3G,YAAM,SAAS,KAAK;AAAA,QACnB;AAAA,QACA,KAAK,eAAe;AAAA,QACpB,KAAK,eAAe;AAAA,QACpB,KAAK,eAAe;AAAA,MACrB;AACA,UAAI,QAAQ;AACX,eAAO,KAAK,MAAM;AAAA,MACnB,OAAO;AACN,eAAO,QAAQ,IAAI,MAAM,wBAAwB,CAAC;AAAA,MACnD;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,UAAU,QAAgB,SAA8C;AAC9E,UAAM,WAAW,KAAK,cAAc,IAAI,MAAM;AAC9C,QAAI;AAAU,aAAO;AAErB,UAAM,SAAS,IAAI,mBAAmB;AAAA,MACrC,GAAG,uCAAuC;AAAA,MAC1C,GAAG;AAAA,IACJ,CAAC;AAED,WAAO,KAAK,SAAS,MAAM,KAAK,cAAc,OAAO,MAAM,CAAC;AAC5D,SAAK,cAAc,IAAI,QAAQ,MAAM;AACrC,WAAO;AAAA,EACR;AACD;;;AP9JO,IAAK,wBAAL,kBAAKE,2BAAL;AAIN,EAAAA,uBAAA,gBAAa;AAKb,EAAAA,uBAAA,eAAY;AAKZ,EAAAA,uBAAA,kBAAe;AAKf,EAAAA,uBAAA,WAAQ;AAKR,EAAAA,uBAAA,gBAAa;AAxBF,SAAAA;AAAA,GAAA;AAwCL,IAAK,kCAAL,kBAAKC,qCAAL;AAIN,EAAAA,kEAAA;AAKA,EAAAA,kEAAA;AAKA,EAAAA,kEAAA;AAKA,EAAAA,kEAAA;AAnBW,SAAAA;AAAA,GAAA;AAuIL,IAAM,kBAAN,cAA8BC,cAAa;AAAA,EAtMlD,OAsMkD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1C;AAAA;AAAA;AAAA;AAAA,EAKC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMC;AAAA;AAAA;AAAA;AAAA;AAAA,EASD;AAAA;AAAA;AAAA;AAAA,EAKC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQV,YAAY,YAAwB,SAAuC;AACjF,UAAM;AAEN,SAAK,QAAQ,QAAQ,QAAQ,CAAC,YAAoB,KAAK,KAAK,SAAS,OAAO,IAAI;AAChF,SAAK,iBAAiB;AAEtB,SAAK,WAAW,IAAI,cAAc,IAAI;AAEtC,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,0BAA0B,KAAK,wBAAwB,KAAK,IAAI;AACrE,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AAEzD,UAAM,UAAU,QAAQ,eAAe;AAAA,MACtC,qBAAqB,CAAC,SAAS,KAAK,gBAAgB,IAAI;AAAA,MACxD,oBAAoB,CAAC,SAAS,KAAK,eAAe,IAAI;AAAA,MACtD,SAAS,MAAM,KAAK,QAAQ,KAAK;AAAA,IAClC,CAAC;AAED,SAAK,SAAS,EAAE,QAAQ,+BAAkC,QAAQ;AAElE,SAAK,UAAU;AAAA,MACd,QAAQ;AAAA,MACR,OAAO;AAAA,IACR;AAEA,SAAK,aAAa;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,QAAQ;AAClB,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,MAAM,UAAgC;AAChD,UAAM,WAAW,KAAK;AACtB,UAAM,gBAAgB,QAAQ,IAAI,UAAU,YAAY;AACxD,UAAM,gBAAgB,QAAQ,IAAI,UAAU,YAAY;AAExD,UAAM,kBAAkB,QAAQ,IAAI,UAAU,cAAc;AAC5D,UAAM,kBAAkB,QAAQ,IAAI,UAAU,cAAc;AAE5D,QAAI,kBAAkB,eAAe;AACpC,UAAI,eAAe;AAClB,sBAAc,GAAG,SAAS,IAAI;AAC9B,sBAAc,IAAI,SAAS,KAAK,iBAAiB;AACjD,sBAAc,IAAI,SAAS,KAAK,iBAAiB;AACjD,sBAAc,IAAI,SAAS,KAAK,iBAAiB;AACjD,sBAAc,IAAI,eAAe,KAAK,uBAAuB;AAC7D,sBAAc,QAAQ;AAAA,MACvB;AAEA,UAAI;AAAe,aAAK,sBAAsB,cAAc,OAAO,eAAe,KAAK;AAAA,IACxF;AAEA,QAAI,SAAS,WAAW,qBAA6B;AACpD,WAAK,iBAAiB;AAAA,IACvB,WAAW,SAAS,WAAW,6BAAiC;AAC/D,iBAAW,UAAU,KAAK,SAAS,cAAc,OAAO,GAAG;AAC1D,YAAI,CAAC,OAAO;AAAW,iBAAO,QAAQ;AAAA,MACvC;AAAA,IACD;AAGA,QAAI,SAAS,WAAW,+BAAmC,SAAS,WAAW,6BAAiC;AAC/G,eAAS,QAAQ,QAAQ;AAAA,IAC1B;AAEA,SAAK,SAAS;AAEd,QAAI,mBAAmB,oBAAoB,iBAAiB;AAC3D,sBAAgB,YAAY;AAAA,IAC7B;AAEA,SAAK,KAAK,eAAe,UAAU,QAAQ;AAC3C,QAAI,SAAS,WAAW,SAAS,QAAQ;AACxC,WAAK,KAAK,SAAS,QAAQ,UAAU,QAAe;AAAA,IACrD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQQ,gBAAgB,QAA8C;AACrE,SAAK,QAAQ,SAAS;AACtB,QAAI,OAAO,UAAU;AACpB,WAAK,oBAAoB;AAAA,IAC1B,WAAW,KAAK,MAAM,WAAW,6BAAiC;AACjE,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQQ,eAAe,QAA6C;AACnE,SAAK,QAAQ,QAAQ;AAErB,QAAI,OAAO,cAAc;AAAW,WAAK,WAAW,WAAW,OAAO;AACtE,QAAI,OAAO,cAAc;AAAW,WAAK,WAAW,WAAW,OAAO;AACtE,QAAI,OAAO;AAAY,WAAK,WAAW,YAAY,OAAO;AAAA,EAM3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,sBAAsB,UAA2B,UAA4B;AACpF,UAAM,QAAQ,QAAQ,IAAI,YAAY,CAAC,GAAG,IAAI;AAC9C,UAAM,QAAQ,QAAQ,IAAI,UAAU,IAAI;AACxC,UAAM,SAAS,QAAQ,IAAI,YAAY,CAAC,GAAG,KAAK;AAChD,UAAM,SAAS,QAAQ,IAAI,UAAU,KAAK;AAE1C,QAAI,UAAU,OAAO;AACpB,aAAO,IAAI,UAAU,KAAK,SAAS,UAAU;AAC7C,aAAO,GAAG,UAAU,KAAK,SAAS,UAAU;AAAA,IAC7C;AAEA,QAAI,WAAW,QAAQ;AACtB,cAAQ,IAAI,WAAW,KAAK,SAAS,YAAY;AACjD,cAAQ,GAAG,WAAW,KAAK,SAAS,YAAY;AAAA,IACjD;AAEA,SAAK,SAAS,iBAAiB,QAAQ,IAAI,UAAU,gBAAgB,KAAK,CAAC;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaO,sBAAsB;AAC5B,UAAM,EAAE,QAAQ,MAAM,IAAI,KAAK;AAC/B,QAAI,CAAC,UAAU,CAAC,SAAS,KAAK,MAAM,WAAW,+BAAmC,CAAC,OAAO;AAAU;AAEpG,UAAM,aAAa,IAAI;AAAA,MACtB;AAAA,QACC,UAAU,OAAO;AAAA,QACjB,UAAU,OAAO;AAAA,QACjB,OAAO,OAAO;AAAA,QACd,WAAW,MAAM;AAAA,QACjB,QAAQ,MAAM;AAAA,MACf;AAAA,MACA,QAAQ,KAAK,KAAK;AAAA,IACnB;AAEA,eAAW,KAAK,SAAS,KAAK,iBAAiB;AAC/C,eAAW,GAAG,eAAe,KAAK,uBAAuB;AACzD,eAAW,GAAG,SAAS,KAAK,iBAAiB;AAC7C,eAAW,GAAG,SAAS,KAAK,iBAAiB;AAE7C,SAAK,QAAQ;AAAA,MACZ,GAAG,KAAK;AAAA,MACR,QAAQ;AAAA,MACR;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaQ,kBAAkB,MAAc;AACvC,QAAI,KAAK,MAAM,WAAW;AAAiC;AAE3D,QAAI,SAAS,MAAO;AAEnB,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,MACZ;AAAA,IACD,OAAO;AACN,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,QAAQ;AAAA,MACT;AACA,WAAK;AACL,UAAI,CAAC,KAAK,MAAM,QAAQ,YAAY,8BAA8B,KAAK,UAAU,CAAC,GAAG;AACpF,aAAK,QAAQ;AAAA,UACZ,GAAG,KAAK;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQQ,wBAAwB,UAA2B,UAA2B;AACrF,SAAK,sBAAsB,UAAU,QAAQ;AAC7C,QAAI,SAAS,SAAS,SAAS;AAAM;AACrC,QAAI,KAAK,MAAM,WAAW,iCAAoC,KAAK,MAAM,WAAW;AACnF;AAED,QAAI,SAAS,wBAAqC;AACjD,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,QAAQ;AAAA,MACT;AAAA,IACD,WAAW,SAAS,yBAAsC;AACzD,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,kBAAkB,OAAc;AACvC,SAAK,KAAK,SAAS,KAAK;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,kBAAkB,SAAiB;AAC1C,SAAK,QAAQ,QAAQ,OAAO,EAAE;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,mBAAmB,QAAgB;AACzC,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM,WAAW;AAA6B;AAClD,WAAO,MAAM,WAAW,mBAAmB,MAAM;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKO,gBAAgB;AACtB,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM,WAAW;AAA6B;AAClD,WAAO,MAAM,WAAW,cAAc;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eAAe,QAAgB;AACrC,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM,WAAW;AAA6B;AAClD,UAAM,WAAW,mBAAmB,MAAM;AAC1C,WAAO,MAAM,WAAW,cAAc;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,QAAQ,mBAAmB,MAAM;AACvC,QAAI,KAAK,MAAM,WAAW,6BAAiC;AAC1D,YAAM,IAAI,MAAM,gEAAgE;AAAA,IACjF;AAEA,QAAI,mBAAmB,KAAK,WAAW,SAAS,KAAK,WAAW,KAAK,MAAM,MAAM;AAChF,6BAAuB,IAAI;AAAA,IAC5B;AAEA,QAAI,kBAAkB;AACrB,WAAK,MAAM,QAAQ,YAAY,8BAA8B,EAAE,GAAG,KAAK,YAAY,WAAW,KAAK,CAAC,CAAC;AAAA,IACtG;AAEA,SAAK,QAAQ;AAAA,MACZ,QAAQ;AAAA,IACT;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,aAAa;AACnB,QACC,KAAK,MAAM,WAAW,+BACtB,KAAK,MAAM,WAAW,+BACrB;AACD,aAAO;AAAA,IACR;AAEA,SAAK,WAAW,YAAY;AAC5B,QAAI,CAAC,KAAK,MAAM,QAAQ,YAAY,8BAA8B,KAAK,UAAU,CAAC,GAAG;AACpF,WAAK,QAAQ;AAAA,QACZ,SAAS,KAAK,MAAM;AAAA,QACpB,cAAc,KAAK,MAAM;AAAA,QACzB,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AACA,aAAO;AAAA,IACR;AAEA,SAAK,QAAQ;AAAA,MACZ,SAAS,KAAK,MAAM;AAAA,MACpB,QAAQ;AAAA,MACR,QAAQ;AAAA,IACT;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYO,OAAO,YAAoD;AACjE,QAAI,KAAK,MAAM,WAAW,6BAAiC;AAC1D,aAAO;AAAA,IACR;AAEA,UAAM,WAAW,KAAK,MAAM,WAAW;AAEvC,QAAI;AAAU,WAAK;AACnB,WAAO,OAAO,KAAK,YAAY,UAAU;AACzC,QAAI,KAAK,MAAM,QAAQ,YAAY,8BAA8B,KAAK,UAAU,CAAC,GAAG;AACnF,UAAI,UAAU;AACb,aAAK,QAAQ;AAAA,UACZ,GAAG,KAAK;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,SAAK,QAAQ;AAAA,MACZ,SAAS,KAAK,MAAM;AAAA,MACpB,cAAc,KAAK,MAAM;AAAA,MACzB,QAAQ;AAAA,MACR,QAAQ;AAAA,IACT;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,YAAY,SAAkB;AACpC,QAAI,KAAK,MAAM,WAAW;AAA6B,aAAO;AAE9D,WAAO,KAAK,MAAM,WAAW,YAAY,OAAO;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,UAAU,QAAqB;AACrC,QAAI,KAAK,MAAM,WAAW;AAAiC;AAG3D,UAAM,eAAe,OAAO,WAAW,EAAE,IAAI;AAE7C,SAAK,QAAQ;AAAA,MACZ,GAAG,KAAK;AAAA,MACR;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAW,OAAO;AACjB,QACC,KAAK,MAAM,WAAW,uBACtB,KAAK,MAAM,WAAW,MAAM,wBAC3B;AACD,aAAO;AAAA,QACN,IAAI,KAAK,MAAM,WAAW,MAAM,GAAG;AAAA,QACnC,KAAK,KAAK,MAAM,WAAW,MAAM,IAAI;AAAA,MACtC;AAAA,IACD;AAEA,WAAO;AAAA,MACN,IAAI;AAAA,MACJ,KAAK;AAAA,IACN;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOU,sBAAsB,cAAkC;AACjE,QAAI,KAAK,MAAM,WAAW,+BAAmC,KAAK,MAAM,iBAAiB,cAAc;AACtG,WAAK,QAAQ;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,cAAc;AAAA,MACf;AAAA,IACD;AAAA,EACD;AACD;AAQO,SAAS,sBAAsB,YAAwB,SAAuC;AACpG,QAAM,UAAU,8BAA8B,UAAU;AACxD,QAAM,WAAW,mBAAmB,WAAW,SAAS,WAAW,KAAK;AACxE,MAAI,YAAY,SAAS,MAAM,WAAW,6BAAiC;AAC1E,QAAI,SAAS,MAAM,WAAW,mCAAoC;AACjE,eAAS,OAAO;AAAA,QACf,WAAW,WAAW;AAAA,QACtB,UAAU,WAAW;AAAA,QACrB,UAAU,WAAW;AAAA,MACtB,CAAC;AAAA,IACF,WAAW,CAAC,SAAS,MAAM,QAAQ,YAAY,OAAO,GAAG;AACxD,eAAS,QAAQ;AAAA,QAChB,GAAG,SAAS;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAEA,QAAM,kBAAkB,IAAI,gBAAgB,YAAY,OAAO;AAC/D,uBAAqB,eAAe;AACpC,MACC,gBAAgB,MAAM,WAAW,+BACjC,CAAC,gBAAgB,MAAM,QAAQ,YAAY,OAAO,GACjD;AACD,oBAAgB,QAAQ;AAAA,MACvB,GAAG,gBAAgB;AAAA,MACnB,QAAQ;AAAA,MACR,QAAQ;AAAA,IACT;AAAA,EACD;AAEA,SAAO;AACR;AAnCgB;;;AczpBT,SAAS,iBAAiB,SAAiE;AACjG,QAAM,aAAyB;AAAA,IAC9B,UAAU;AAAA,IACV,UAAU;AAAA,IACV,OAAO;AAAA,IACP,GAAG;AAAA,EACJ;AAEA,SAAO,sBAAsB,YAAY;AAAA,IACxC,gBAAgB,QAAQ;AAAA,IACxB,OAAO,QAAQ;AAAA,EAChB,CAAC;AACF;AAZgB;;;ACnDhB,SAAS,gBAA+B;AACxC,OAAOC,YAAW;;;ACDlB,OAAO,WAAW;AAOlB,IAAM,uBAAuB,CAAC,oBAAoB,KAAK,aAAa,KAAK,MAAM,SAAS,OAAO,SAAS,OAAO,GAAG;AAClH,IAAM,wBAAwB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAKO,IAAK,aAAL,kBAAKC,gBAAL;AAIN,EAAAA,YAAA,eAAY;AAIZ,EAAAA,YAAA,aAAU;AAIV,EAAAA,YAAA,UAAO;AAIP,EAAAA,YAAA,SAAM;AAIN,EAAAA,YAAA,cAAW;AApBA,SAAAA;AAAA,GAAA;AAkDL,IAAM,OAAN,MAAW;AAAA,EA7ElB,OA6EkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAID,QAAgB,CAAC;AAAA;AAAA;AAAA;AAAA,EAKjB;AAAA,EAET,YAAY,MAAkB;AACpC,SAAK,OAAO;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,QAAQ,MAA0B;AACxC,SAAK,MAAM,KAAK,EAAE,GAAG,MAAM,MAAM,KAAK,CAAC;AAAA,EACxC;AACD;AAGA,IAAI,QAAsC;AAOnC,SAAS,QAAQ,MAAkB;AACzC,QAAM,QAAQ,UAAU,gBAAgB,GAAG,IAAI,IAAI;AACnD,MAAI,CAAC;AAAM,UAAM,IAAI,MAAM,cAAc,IAAI,mBAAmB;AAChE,SAAO;AACR;AAJgB;AAOhB,SAAS,+BAAwC;AAChD,MAAI;AACH,WAAO,MAAM,OAAO,QAAQ,EAAE,OAAO,SAAS,kBAAkB;AAAA,EACjE,QAAQ;AAAA,EAAC;AAET,SAAO;AACR;AANS;AAQT,SAAS,kBAAyC;AACjD,QAAM,QAAQ,oBAAI,IAAsB;AACxC,aAAW,cAAc,OAAO,OAAO,UAAU,GAAG;AACnD,UAAM,IAAI,YAAY,IAAI,KAAK,UAAU,CAAC;AAAA,EAC3C;AAEA,QAAM,IAAI,eAAc,EAAG,QAAQ;AAAA,IAClC,MAAM;AAAA,IACN,IAAI,MAAM,IAAI,iBAAe;AAAA,IAC7B,MAAM;AAAA,IACN,aAAa,MAAM,IAAI,MAAM,KAAK,QAAQ,EAAE,MAAM,MAAQ,UAAU,GAAG,WAAW,IAAI,CAAC;AAAA,EACxF,CAAC;AAED,QAAM,IAAI,iBAAe,EAAG,QAAQ;AAAA,IACnC,MAAM;AAAA,IACN,IAAI,MAAM,IAAI,eAAc;AAAA,IAC5B,MAAM;AAAA,IACN,aAAa,MAAM,IAAI,MAAM,KAAK,QAAQ,EAAE,MAAM,MAAQ,UAAU,GAAG,WAAW,IAAI,CAAC;AAAA,EACxF,CAAC;AAED,QAAM,IAAI,wBAAkB,EAAG,QAAQ;AAAA,IACtC,MAAM;AAAA,IACN,IAAI,MAAM,IAAI,iBAAe;AAAA,IAC7B,MAAM;AAAA,IACN,aAAa,MAAM,IAAI,MAAM,KAAK,WAAW;AAAA,EAC9C,CAAC;AAED,QAAM,IAAI,0BAAmB,EAAG,QAAQ;AAAA,IACvC,MAAM;AAAA,IACN,IAAI,MAAM,IAAI,iBAAe;AAAA,IAC7B,MAAM;AAAA,IACN,aAAa,MAAM,IAAI,MAAM,KAAK,YAAY;AAAA,EAC/C,CAAC;AAED,QAAM,kBAAsC;AAAA,IAC3C,MAAM;AAAA,IACN,IAAI,MAAM,IAAI,eAAc;AAAA,IAC5B,MAAM;AAAA,IACN,aAAa,CAAC,UACb,IAAI,MAAM,OAAO;AAAA,MAChB,MAAM,CAAC,MAAM,OAAO,UAAU,WAAW,QAAQ,KAAK,GAAG,oBAAoB;AAAA,IAC9E,CAAC;AAAA,EACH;AAEA,QAAM,IAAI,2BAAoB,EAAG,QAAQ,eAAe;AACxD,QAAM,IAAI,wBAAkB,EAAG,QAAQ,eAAe;AACtD,QAAM,IAAI,0BAAmB,EAAG,QAAQ,eAAe;AAEvD,QAAM,IAAI,eAAc,EAAG,QAAQ;AAAA,IAClC,MAAM;AAAA,IACN,IAAI,MAAM,IAAI,eAAc;AAAA,IAC5B,MAAM;AAAA,IACN,aAAa,MAAM,IAAI,MAAM,kBAAkB,EAAE,MAAM,QAAQ,CAAC;AAAA,EACjE,CAAC;AAED,MAAI,6BAA6B,GAAG;AACnC,UAAM,kBAAsC;AAAA,MAC3C,MAAM;AAAA,MACN,IAAI,MAAM,IAAI,wBAAkB;AAAA,MAChC,MAAM;AAAA,MACN,aAAa,CAAC,UACb,IAAI,MAAM,OAAO;AAAA,QAChB,MAAM,CAAC,MAAM,OAAO,UAAU,WAAW,QAAQ,KAAK,GAAG,qBAAqB;AAAA,MAC/E,CAAC;AAAA,IACH;AACA,UAAM,IAAI,2BAAoB,EAAG,QAAQ,eAAe;AAIxD,UAAM,IAAI,wBAAkB,EAAG,QAAQ,eAAe;AACtD,UAAM,IAAI,0BAAmB,EAAG,QAAQ,eAAe;AAAA,EACxD;AAEA,SAAO;AACR;AA1ES;AAyGT,SAAS,SACR,MACA,aACA,OAAO,QAAQ,iBAAe,GAC9B,OAAe,CAAC,GAChB,QAAQ,GACD;AACP,MAAI,SAAS,QAAQ,YAAY,IAAI,GAAG;AACvC,WAAO,EAAE,MAAM,EAAE;AAAA,EAClB,WAAW,UAAU,GAAG;AACvB,WAAO,EAAE,MAAM,OAAO,kBAAkB;AAAA,EACzC;AAEA,MAAI;AACJ,aAAW,QAAQ,KAAK,OAAO;AAC9B,QAAI,eAAe,KAAK,OAAO,YAAY;AAAM;AACjD,UAAM,OAAO,SAAS,KAAK,IAAI,aAAa,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,QAAQ,CAAC;AAC5E,UAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,QAAI,CAAC,eAAe,OAAO,YAAY,MAAM;AAC5C,oBAAc,EAAE,MAAM,MAAM,KAAK;AAAA,IAClC;AAAA,EACD;AAEA,SAAO,eAAe,EAAE,MAAM,OAAO,kBAAkB;AACxD;AAxBS;AA+BT,SAAS,kBAAkB,MAAY;AACtC,QAAM,QAAQ,CAAC;AACf,MAAI,UAA4B;AAChC,SAAO,SAAS,MAAM;AACrB,UAAM,KAAK,QAAQ,IAAI;AACvB,cAAU,QAAQ;AAAA,EACnB;AAEA,SAAO;AACR;AATS;AAiBF,SAAS,aAAa,MAAkB,YAAuC;AACrF,SAAO,kBAAkB,SAAS,QAAQ,IAAI,GAAG,UAAU,CAAC;AAC7D;AAFgB;;;AD3OT,IAAM,gBAAN,MAAwC;AAAA,EA3C/C,OA2C+C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAI9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA,EAKT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMS;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA,EAKT;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAAA;AAAA;AAAA;AAAA,EAKnB,UAAU;AAAA;AAAA;AAAA;AAAA,EAKD;AAAA;AAAA;AAAA;AAAA,EAKT,mBAAmB;AAAA,EAEnB,YACN,OACA,SACA,UACA,sBACC;AACD,SAAK,QAAQ;AACb,SAAK,aAAa,QAAQ,SAAS,IAAK,SAAS,SAAS,IAAI,IAAwB,QAAQ,CAAC;AAC/F,SAAK,WAAW;AAChB,SAAK,uBAAuB;AAE5B,eAAW,UAAU,SAAS;AAC7B,UAAI,kBAAkBC,OAAM,mBAAmB;AAC9C,aAAK,SAAS;AAAA,MACf,WAAW,kBAAkBA,OAAM,KAAK,SAAS;AAChD,aAAK,UAAU;AAAA,MAChB;AAAA,IACD;AAEA,SAAK,WAAW,KAAK,YAAY,MAAO,KAAK,UAAU,IAAK;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAW,WAAW;AACrB,QAAI,KAAK,qBAAqB;AAAG,aAAO;AACxC,UAAM,OAAO,KAAK,WAAW;AAC7B,QAAI,CAAC,MAAM;AACV,UAAI,KAAK,qBAAqB;AAAI,aAAK,mBAAmB,KAAK;AAC/D,aAAO,KAAK,qBAAqB;AAAA,IAClC;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,QAAQ;AAClB,WAAO,KAAK,WAAW,iBAAiB,KAAK,WAAW,aAAa,KAAK,qBAAqB;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYO,OAAsB;AAC5B,QAAI,KAAK,qBAAqB,GAAG;AAChC,aAAO;AAAA,IACR,WAAW,KAAK,mBAAmB,GAAG;AACrC,WAAK;AACL,aAAO;AAAA,IACR;AAEA,UAAM,SAAS,KAAK,WAAW,KAAK;AACpC,QAAI,QAAQ;AACX,WAAK,oBAAoB;AAAA,IAC1B;AAEA,WAAO;AAAA,EACR;AACD;AAOO,IAAM,oBAAoB,wBAAC,SAAiB,KAAK,KAAK,CAAC,SAAS,KAAK,gDAAqC,GAAhF;AAE1B,IAAM,gBAAgB,6BAAM,MAAN;AAOtB,SAAS,gBAAgB,QAG9B;AACD,MAAI,kBAAkBA,OAAM,KAAK,SAAS;AACzC,WAAO,EAAE,+BAA6B,WAAW,MAAM;AAAA,EACxD,WAAW,kBAAkBA,OAAM,KAAK,SAAS;AAChD,WAAO,EAAE,6BAA4B,WAAW,MAAM;AAAA,EACvD,WAAW,kBAAkBA,OAAM,mBAAmB;AACrD,WAAO,EAAE,6BAA4B,WAAW,KAAK;AAAA,EACtD,WAAW,kBAAkBA,OAAM,KAAK,YAAY;AACnD,WAAO,EAAE,+BAA6B,WAAW,MAAM;AAAA,EACxD,WAAW,kBAAkBA,OAAM,KAAK,aAAa;AACpD,WAAO,EAAE,+BAA6B,WAAW,MAAM;AAAA,EACxD;AAEA,SAAO,EAAE,yCAAkC,WAAW,MAAM;AAC7D;AAjBgB;AA0ET,SAAS,oBACf,OACA,UAAgD,CAAC,GACvB;AAC1B,MAAI,YAAY,QAAQ;AACxB,MAAI,oBAAoB,QAAQ,QAAQ,YAAY;AAGpD,MAAI,OAAO,UAAU,UAAU;AAC9B;AAAA,EACD,WAAW,cAAc,QAAW;AACnC,UAAM,WAAW,gBAAgB,KAAK;AACtC,gBAAY,SAAS;AACrB,wBAAoB,qBAAqB,CAAC,SAAS;AAAA,EACpD;AAEA,QAAM,sBAAsB,aAAa,WAAW,oBAAoB,oBAAoB,aAAa;AAEzG,MAAI,oBAAoB,WAAW,GAAG;AACrC,QAAI,OAAO,UAAU;AAAU,YAAM,IAAI,MAAM,qDAAqD,KAAK,GAAG;AAE5G,WAAO,IAAI;AAAA,MACV,CAAC;AAAA,MACD,CAAC,KAAK;AAAA,MACL,QAAQ,YAAY;AAAA,MACrB,QAAQ,wBAAwB;AAAA,IACjC;AAAA,EACD;AAEA,QAAM,UAAU,oBAAoB,IAAI,CAAC,SAAS,KAAK,YAAY,KAAK,CAAC;AACzE,MAAI,OAAO,UAAU;AAAU,YAAQ,QAAQ,KAAK;AAEpD,SAAO,IAAI;AAAA,IACV;AAAA,IACA;AAAA,IACC,QAAQ,YAAY;AAAA,IACrB,QAAQ,wBAAwB;AAAA,EACjC;AACD;AAtCgB;;;AE/PhB,SAAS,SAAS,eAAe;AACjC,OAAOC,YAAW;AASlB,SAAS,gBACR,KACA,aACA,OACgD;AAChD,MAAI,UAAU;AAAG,WAAO;AACxB,QAAM,gBAAgB,QAAQ,KAAK,gBAAgB;AACnD,MAAI;AACH,UAAM,MAAM,UAAQ,aAAa;AACjC,QAAI,IAAI,SAAS;AAAa,YAAM,IAAI,MAAM,6BAA6B;AAC3E,WAAO;AAAA,EACR,QAAQ;AACP,WAAO,gBAAgB,QAAQ,KAAK,IAAI,GAAG,aAAa,QAAQ,CAAC;AAAA,EAClE;AACD;AAdS;AAqBT,SAAS,QAAQ,MAAsB;AACtC,MAAI;AACH,QAAI,SAAS,oBAAoB;AAChC,aAAO;AAAA,IACR;AAEA,UAAM,MAAM,gBAAgB,QAAQ,UAAQ,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC;AACnE,WAAO,KAAK,WAAW;AAAA,EACxB,QAAQ;AACP,WAAO;AAAA,EACR;AACD;AAXS;AAiBF,SAAS,2BAA2B;AAC1C,QAAM,SAAS,CAAC;AAChB,QAAM,aAAa,wBAAC,SAAiB,OAAO,KAAK,KAAK,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,GAA3D;AAEnB,SAAO,KAAK,mBAAmB;AAC/B,aAAW,kBAAkB;AAC7B,aAAW,aAAa;AACxB,SAAO,KAAK,EAAE;AAGd,SAAO,KAAK,gBAAgB;AAC5B,aAAW,iBAAiB;AAC5B,aAAW,YAAY;AACvB,SAAO,KAAK,EAAE;AAGd,SAAO,KAAK,sBAAsB;AAClC,aAAW,eAAe;AAC1B,aAAW,QAAQ;AACnB,aAAW,oBAAoB;AAC/B,aAAW,WAAW;AACtB,SAAO,KAAK,EAAE;AAGd,SAAO,KAAK,QAAQ;AACpB,MAAI;AACH,UAAM,OAAOC,OAAM,OAAO,QAAQ;AAClC,WAAO,KAAK,cAAc,KAAK,OAAO,EAAE;AACxC,WAAO,KAAK,cAAc,KAAK,OAAO,SAAS,kBAAkB,IAAI,QAAQ,IAAI,EAAE;AAAA,EACpF,QAAQ;AACP,WAAO,KAAK,aAAa;AAAA,EAC1B;AAEA,SAAO,CAAC,IAAI,OAAO,EAAE,GAAG,GAAG,QAAQ,IAAI,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI;AAC7D;AAlCgB;;;AClDhB,SAA4B,YAAY;;;ACKjC,SAAS,WAAW,OAA+C;AACzE,QAAM,KAAK,IAAI,gBAAgB;AAC/B,QAAM,UAAU,WAAW,MAAM,GAAG,MAAM,GAAG,KAAK;AAElD,KAAG,OAAO,iBAAiB,SAAS,MAAM,aAAa,OAAO,CAAC;AAC/D,SAAO,CAAC,IAAI,GAAG,MAAM;AACtB;AANgB;;;ADiChB,eAAsB,YACrB,QACA,QACA,iBACC;AACD,MAAI,OAAO,MAAM,WAAW,QAAQ;AACnC,UAAM,CAAC,IAAI,MAAM,IAChB,OAAO,oBAAoB,WAAW,WAAW,eAAe,IAAI,CAAC,QAAW,eAAe;AAChG,QAAI;AACH,YAAM,KAAK,QAAwB,QAAQ,EAAE,OAAO,CAAC;AAAA,IACtD,UAAE;AACD,UAAI,MAAM;AAAA,IACX;AAAA,EACD;AAEA,SAAO;AACR;AAhBsB;;;AEtCtB,SAAS,UAAAC,eAAc;AACvB,OAAO,aAAa;AACpB,SAAS,YAAAC,iBAAgB;AACzB,OAAOC,YAAW;AAUX,SAAS,wBAAwB,UAA2B;AAClE,QAAM,WAAW,SAAS,UAAU,CAAC;AACrC,QAAM,aAAa,SAAS,aAAa,EAAE;AAC3C,SAAO,aAAa,KAAK,eAAe;AACzC;AAJgB;AA8BhB,eAAsB,WACrB,QACA,YAAY,MACZ,YAAY,yBACS;AACrB,SAAO,IAAI,QAAQ,CAACC,UAAS,WAAW;AAEvC,QAAI,OAAO,oBAAoB;AAC9B,aAAO,IAAI,MAAM,+CAA+C,CAAC;AACjE;AAAA,IACD;AAEA,QAAI,OAAO,eAAe;AACzB,aAAO,IAAI,MAAM,sCAAsC,CAAC;AACxD;AAAA,IACD;AAEA,QAAI,aAAaC,QAAO,MAAM,CAAC;AAE/B,QAAI;AAEJ,UAAM,SAAS,wBAAC,SAAqB;AAEpC,aAAO,IAAI,QAAQ,MAAM;AAEzB,aAAO,IAAI,SAAS,OAAO;AAE3B,aAAO,IAAI,OAAO,OAAO;AACzB,aAAO,MAAM;AACb,iBAAW;AACX,UAAI,OAAO,eAAe;AACzB,QAAAD,SAAQ;AAAA,UACP,QAAQE,UAAS,KAAK,UAAU;AAAA,UAChC;AAAA,QACD,CAAC;AAAA,MACF,OAAO;AACN,YAAI,WAAW,SAAS,GAAG;AAC1B,iBAAO,KAAK,UAAU;AAAA,QACvB;AAEA,QAAAF,SAAQ;AAAA,UACP;AAAA,UACA;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD,GAxBe;AA0Bf,UAAM,YAAY,wBAAC,SAAqB,CAAC,SAAiB;AACzD,UAAI,UAAU,IAAI,GAAG;AACpB,eAAO,IAAI;AAAA,MACZ;AAAA,IACD,GAJkB;AAMlB,UAAM,OAAO,IAAIG,OAAM,KAAK,YAAY;AACxC,SAAK,KAAK,SAAS,IAAI;AACvB,SAAK,GAAG,QAAQ,oCAA6B,CAAC;AAE9C,UAAM,MAAM,IAAIA,OAAM,KAAK,WAAW;AACtC,QAAI,KAAK,SAAS,IAAI;AACtB,QAAI,GAAG,QAAQ,kCAA4B,CAAC;AAE5C,UAAM,UAAU,6BAAM;AACrB,UAAI,CAAC,UAAU;AACd,0CAA2B;AAAA,MAC5B;AAAA,IACD,GAJgB;AAMhB,UAAM,SAAS,wBAAC,WAAmB;AAClC,mBAAaF,QAAO,OAAO,CAAC,YAAY,MAAM,CAAC;AAE/C,WAAK,MAAM,MAAM;AACjB,UAAI,MAAM,MAAM;AAEhB,UAAI,WAAW,UAAU,WAAW;AACnC,eAAO,IAAI,QAAQ,MAAM;AACzB,eAAO,MAAM;AACb,gBAAQ,SAAS,OAAO;AAAA,MACzB;AAAA,IACD,GAXe;AAaf,WAAO,KAAK,SAAS,MAAM;AAC3B,WAAO,GAAG,QAAQ,MAAM;AACxB,WAAO,KAAK,SAAS,OAAO;AAC5B,WAAO,KAAK,OAAO,OAAO;AAAA,EAC3B,CAAC;AACF;AArFsB;;;AChBf,IAAMG,WAAU;", "names": ["EventEmitter", "<PERSON><PERSON><PERSON>", "EventEmitter", "VoiceOpcodes", "<PERSON><PERSON><PERSON>", "nonce", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "resolve", "EventEmitter", "EventEmitter", "nonce", "<PERSON><PERSON><PERSON>", "option", "EventEmitter", "VoiceOpcodes", "<PERSON><PERSON><PERSON>", "VoiceOpcodes", "<PERSON><PERSON><PERSON>", "EventEmitter", "<PERSON><PERSON><PERSON>", "NoSubscriberBehavior", "AudioPlayerStatus", "stringifyState", "EventEmitter", "EndBehaviorType", "EventEmitter", "EventEmitter", "EventEmitter", "EventEmitter", "VoiceOpcodes", "nonce", "<PERSON><PERSON><PERSON>", "VoiceConnectionStatus", "VoiceConnectionDisconnectReason", "EventEmitter", "prism", "StreamType", "prism", "prism", "prism", "<PERSON><PERSON><PERSON>", "Readable", "prism", "resolve", "<PERSON><PERSON><PERSON>", "Readable", "prism", "version"]}