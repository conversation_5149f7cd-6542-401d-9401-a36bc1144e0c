const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const embeds = require('../../utils/embeds');

module.exports = {
  name: 'help',
  description: 'Shows help information for commands',
  aliases: ['h', 'commands'],
  usage: 'help [command]',
  examples: ['help', 'help kick', 'help music'],
  cooldown: 3,
  permissions: [],
  
  data: new SlashCommandBuilder()
    .setName('help')
    .setDescription('Shows help information for commands')
    .addStringOption(option =>
      option.setName('command')
        .setDescription('Specific command to get help for')
        .setRequired(false)
    ),

  async execute(messageOrInteraction, args, client) {
    try {
      const isSlash = messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.();
      const commandName = isSlash 
        ? messageOrInteraction.options.getString('command')
        : args[0];

      if (commandName) {
        // Show help for specific command
        const command = client.commands.get(commandName.toLowerCase()) || 
                       client.slashCommands.get(commandName.toLowerCase());

        if (!command) {
          const embed = embeds.error('Command Not Found', `No command found with the name \`${commandName}\``);
          
          if (isSlash) {
            return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
          } else {
            return messageOrInteraction.reply({ embeds: [embed] });
          }
        }

        const helpEmbed = embeds.help(commandName, command);
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [helpEmbed] });
        } else {
          return messageOrInteraction.reply({ embeds: [helpEmbed] });
        }
      } else {
        // Show command list
        const categories = {};
        
        // Organize commands by category
        client.commands.forEach(command => {
          if (!command.category) return;
          
          if (!categories[command.category]) {
            categories[command.category] = [];
          }
          
          // Avoid duplicates (aliases)
          if (!categories[command.category].find(cmd => cmd.name === command.name)) {
            categories[command.category].push(command);
          }
        });

        const prefix = client.config.bot.defaultPrefix; // TODO: Get server-specific prefix
        const helpEmbed = embeds.commandList(categories, prefix);
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [helpEmbed] });
        } else {
          return messageOrInteraction.reply({ embeds: [helpEmbed] });
        }
      }
    } catch (error) {
      client.logger.error('Error in help command:', error);
      
      const embed = embeds.error('Error', 'An error occurred while processing the help command.');
      
      if (messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.()) {
        if (messageOrInteraction.replied || messageOrInteraction.deferred) {
          return messageOrInteraction.followUp({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        }
      } else {
        return messageOrInteraction.reply({ embeds: [embed] });
      }
    }
  }
};
