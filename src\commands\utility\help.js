const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const embeds = require('../../utils/embeds');

module.exports = {
  name: 'help',
  description: 'Shows help information for commands',
  aliases: ['h', 'commands'],
  usage: 'help [command]',
  examples: ['help', 'help kick', 'help music'],
  cooldown: 3,
  permissions: [],
  
  data: new SlashCommandBuilder()
    .setName('help')
    .setDescription('Shows help information for commands')
    .addStringOption(option =>
      option.setName('command')
        .setDescription('Specific command to get help for')
        .setRequired(false)
    ),

  async execute(messageOrInteraction, args, client) {
    try {
      const isSlash = messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.();
      const commandName = isSlash 
        ? messageOrInteraction.options.getString('command')
        : args[0];

      if (commandName) {
        // Show help for specific command
        const commands = {
          'help': {
            description: 'Shows help information for commands',
            usage: 'help [command]',
            examples: ['help', 'help ping'],
            aliases: ['h'],
            cooldown: 3
          },
          'ping': {
            description: 'Shows bot latency and API response time',
            usage: 'ping',
            examples: ['ping'],
            aliases: ['latency'],
            cooldown: 3
          },
          '8ball': {
            description: 'Ask the magic 8-ball a question',
            usage: '8ball <question>',
            examples: ['8ball Will it rain tomorrow?'],
            aliases: ['eightball'],
            cooldown: 3
          },
          'kick': {
            description: 'Kick a member from the server',
            usage: 'kick <user> [reason]',
            examples: ['kick @user Spamming'],
            permissions: ['Kick Members'],
            cooldown: 5
          }
        };

        const command = commands[commandName.toLowerCase()];

        if (!command) {
          const embed = embeds.error('Command Not Found', `No command found with the name \`${commandName}\``);

          if (isSlash) {
            return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
          } else {
            return messageOrInteraction.reply({ embeds: [embed] });
          }
        }

        const helpEmbed = embeds.help(commandName, command);

        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [helpEmbed] });
        } else {
          return messageOrInteraction.reply({ embeds: [helpEmbed] });
        }
      } else {
        // Show command list
        const categories = {
          utility: [
            { name: 'help', description: 'Shows help information for commands' },
            { name: 'ping', description: 'Shows bot latency and API response time' }
          ],
          entertainment: [
            { name: '8ball', description: 'Ask the magic 8-ball a question' }
          ],
          moderation: [
            { name: 'kick', description: 'Kick a member from the server' }
          ]
        };

        const prefix = '-'; // Default prefix
        const helpEmbed = embeds.commandList(categories, prefix);
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [helpEmbed] });
        } else {
          return messageOrInteraction.reply({ embeds: [helpEmbed] });
        }
      }
    } catch (error) {
      const logger = require('../../utils/logger');
      logger.error('Error in help command:', error);
      
      const embed = embeds.error('Error', 'An error occurred while processing the help command.');
      
      if (messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.()) {
        if (messageOrInteraction.replied || messageOrInteraction.deferred) {
          return messageOrInteraction.followUp({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        }
      } else {
        return messageOrInteraction.reply({ embeds: [embed] });
      }
    }
  }
};
