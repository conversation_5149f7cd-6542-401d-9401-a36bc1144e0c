/* ===== SETUP PAGE STYLES ===== */

.setup-header {
  padding: 120px 0 60px;
  background: var(--gradient-dark);
  position: relative;
  overflow: hidden;
}

.setup-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(107, 70, 193, 0.1) 0%, transparent 50%);
  z-index: -1;
}

.setup-hero {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.setup-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
}

.setup-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

/* ===== PROGRESS INDICATOR ===== */
.setup-progress {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  position: relative;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 25px;
  left: calc(100% + var(--spacing-md));
  width: var(--spacing-lg);
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
  transition: background var(--transition-normal);
}

.progress-step.active:not(:last-child)::after,
.progress-step.completed:not(:last-child)::after {
  background: var(--primary-color);
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--text-secondary);
  transition: all var(--transition-normal);
}

.progress-step.active .step-number,
.progress-step.completed .step-number {
  background: var(--gradient-primary);
  border-color: var(--primary-color);
  color: white;
}

.step-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
  transition: color var(--transition-normal);
}

.progress-step.active .step-label,
.progress-step.completed .step-label {
  color: var(--primary-color);
}

/* ===== SETUP CONTENT ===== */
.setup-content {
  padding: var(--spacing-xxl) 0;
}

.setup-step {
  margin-bottom: var(--spacing-xxl);
  opacity: 0.6;
  transition: opacity var(--transition-normal);
}

.setup-step.active {
  opacity: 1;
}

.step-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.step-icon {
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  flex-shrink: 0;
}

.step-info {
  flex: 1;
}

.step-title {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.step-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* ===== SETUP GRID ===== */
.setup-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
  align-items: start;
}

.setup-instructions h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.setup-instructions h3:first-child {
  margin-top: 0;
}

.setup-list {
  color: var(--text-secondary);
  line-height: 1.8;
  padding-left: var(--spacing-md);
}

.setup-list li {
  margin-bottom: var(--spacing-sm);
}

.setup-list a {
  color: var(--primary-color);
  text-decoration: none;
}

.setup-list a:hover {
  text-decoration: underline;
}

.invite-btn {
  margin: var(--spacing-md) 0 var(--spacing-lg);
}

/* ===== INFO BOX ===== */
.setup-info-box {
  background: var(--surface-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  position: sticky;
  top: 100px;
}

.setup-info-box h4 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  font-size: 1.2rem;
}

.permissions-grid {
  display: grid;
  gap: var(--spacing-xs);
}

.permission-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.permission-item i {
  color: var(--accent-color);
  font-size: 0.8rem;
}

/* ===== CONFIGURATION SECTION ===== */
.config-section h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.config-section h3:first-child {
  margin-top: 0;
}

.config-section p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.6;
}

.code-example {
  background: var(--surface-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  margin: var(--spacing-sm) 0 var(--spacing-lg);
  border-left: 4px solid var(--primary-color);
}

.code-example code {
  font-family: 'Courier New', monospace;
  color: var(--accent-color);
  font-size: 1rem;
  font-weight: 500;
}

/* ===== CUSTOMIZATION GRID ===== */
.customization-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.custom-section {
  background: var(--surface-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border-top: 4px solid var(--primary-color);
}

.custom-section h4 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  font-size: 1.2rem;
}

.custom-section ul {
  list-style: none;
  padding: 0;
}

.custom-section li {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  position: relative;
  padding-left: var(--spacing-md);
  line-height: 1.5;
}

.custom-section li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-weight: bold;
}

/* ===== COMPLETION CONTENT ===== */
.completion-content {
  text-align: center;
}

.success-message {
  background: var(--surface-color);
  border: 1px solid var(--accent-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.success-message i {
  font-size: 4rem;
  color: var(--accent-color);
  margin-bottom: var(--spacing-md);
}

.success-message h3 {
  color: var(--accent-color);
  margin-bottom: var(--spacing-sm);
  font-size: 2rem;
}

.success-message p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.next-steps {
  margin-bottom: var(--spacing-xl);
}

.next-steps h4 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  font-size: 1.5rem;
}

.next-steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.next-step-card {
  background: var(--surface-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-decoration: none;
  transition: all var(--transition-normal);
  display: block;
}

.next-step-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.next-step-card i {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.next-step-card h5 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-size: 1.2rem;
}

.next-step-card p {
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* ===== QUICK TEST ===== */
.quick-test {
  background: var(--surface-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
}

.quick-test h4 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.quick-test p {
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.test-commands {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.test-command {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--background-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
}

.test-command code {
  font-family: 'Courier New', monospace;
  color: var(--accent-color);
  font-weight: 500;
  background: none;
  padding: 0;
}

.test-command span {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .setup-title {
    font-size: 2.5rem;
  }
  
  .setup-progress {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .progress-step:not(:last-child)::after {
    display: none;
  }
  
  .step-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }
  
  .setup-grid {
    grid-template-columns: 1fr;
  }
  
  .setup-info-box {
    position: static;
  }
  
  .customization-grid {
    grid-template-columns: 1fr;
  }
  
  .next-steps-grid {
    grid-template-columns: 1fr;
  }
  
  .test-command {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}
