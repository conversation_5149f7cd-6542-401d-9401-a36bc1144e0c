const commandHandler = require('../utils/commandHandler');
const logger = require('../utils/logger');

module.exports = {
  name: 'interactionCreate',
  once: false,
  async execute(interaction, client) {
    try {
      // Handle slash commands
      if (interaction.isChatInputCommand()) {
        await commandHandler.handleSlashCommand(interaction, client);
      }

      // Handle button interactions
      if (interaction.isButton()) {
        // TODO: Add button interaction handling
        logger.debug(`Button interaction: ${interaction.customId}`);
      }

      // Handle select menu interactions
      if (interaction.isStringSelectMenu()) {
        // TODO: Add select menu interaction handling
        logger.debug(`Select menu interaction: ${interaction.customId}`);
      }

      // Handle modal submissions
      if (interaction.isModalSubmit()) {
        // TODO: Add modal submission handling
        logger.debug(`Modal submission: ${interaction.customId}`);
      }

    } catch (error) {
      logger.error('Error in interactionCreate event:', error);
    }
  }
};
