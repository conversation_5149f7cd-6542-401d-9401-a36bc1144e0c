const commandHandler = require('../utils/commandHandler');
const logger = require('../utils/logger');

module.exports = {
  name: 'messageCreate',
  once: false,
  async execute(message, client) {
    try {
      // Ignore bot messages
      if (message.author.bot) return;

      // Handle prefix commands
      await commandHandler.handlePrefixCommand(message, client);

      // TODO: Add XP/leveling system here
      // TODO: Add auto-moderation checks here
      // TODO: Add spam detection here

    } catch (error) {
      logger.error('Error in messageCreate event:', error);
    }
  }
};
