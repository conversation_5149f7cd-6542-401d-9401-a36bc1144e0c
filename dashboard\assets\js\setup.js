// ===== SETUP PAGE FUNCTIONALITY =====

document.addEventListener('DOMContentLoaded', function() {
    initSetupPage();
});

function initSetupPage() {
    initProgressSteps();
    initStepNavigation();
    initCodeCopying();
    initScrollProgress();
}

// ===== PROGRESS STEPS =====
function initProgressSteps() {
    const progressSteps = document.querySelectorAll('.progress-step');
    const setupSteps = document.querySelectorAll('.setup-step');
    
    // Set initial active step
    updateActiveStep(1);
    
    // Add click handlers for progress steps
    progressSteps.forEach((step, index) => {
        step.addEventListener('click', () => {
            updateActiveStep(index + 1);
            scrollToStep(index + 1);
        });
    });
}

function updateActiveStep(stepNumber) {
    const progressSteps = document.querySelectorAll('.progress-step');
    const setupSteps = document.querySelectorAll('.setup-step');
    
    // Update progress indicators
    progressSteps.forEach((step, index) => {
        step.classList.remove('active', 'completed');
        
        if (index + 1 === stepNumber) {
            step.classList.add('active');
        } else if (index + 1 < stepNumber) {
            step.classList.add('completed');
        }
    });
    
    // Update step content visibility
    setupSteps.forEach((step, index) => {
        step.classList.remove('active');
        
        if (index + 1 === stepNumber) {
            step.classList.add('active');
        }
    });
}

function scrollToStep(stepNumber) {
    const targetStep = document.getElementById(`step-${stepNumber}`);
    if (targetStep) {
        const offsetTop = targetStep.offsetTop - 100; // Account for fixed navbar
        
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }
}

// ===== STEP NAVIGATION =====
function initStepNavigation() {
    // Auto-advance based on scroll position
    window.addEventListener('scroll', debounce(updateStepBasedOnScroll, 100));
}

function updateStepBasedOnScroll() {
    const setupSteps = document.querySelectorAll('.setup-step');
    const scrollPosition = window.scrollY + window.innerHeight / 2;
    
    let activeStepNumber = 1;
    
    setupSteps.forEach((step, index) => {
        const stepTop = step.offsetTop;
        const stepBottom = stepTop + step.offsetHeight;
        
        if (scrollPosition >= stepTop && scrollPosition <= stepBottom) {
            activeStepNumber = index + 1;
        }
    });
    
    // Only update if different from current active step
    const currentActiveStep = document.querySelector('.progress-step.active');
    const currentStepNumber = currentActiveStep ? 
        parseInt(currentActiveStep.getAttribute('data-step')) : 1;
    
    if (activeStepNumber !== currentStepNumber) {
        updateActiveStep(activeStepNumber);
    }
}

// ===== CODE COPYING =====
function initCodeCopying() {
    const codeExamples = document.querySelectorAll('.code-example');
    const testCommands = document.querySelectorAll('.test-command code');
    
    // Add copy functionality to code examples
    codeExamples.forEach(example => {
        example.style.cursor = 'pointer';
        example.title = 'Click to copy';
        
        example.addEventListener('click', () => {
            const code = example.textContent.trim();
            copyToClipboard(code);
        });
    });
    
    // Add copy functionality to test commands
    testCommands.forEach(command => {
        command.style.cursor = 'pointer';
        command.title = 'Click to copy';
        
        command.addEventListener('click', (e) => {
            e.stopPropagation();
            const code = command.textContent.trim();
            copyToClipboard(code);
        });
    });
}

// ===== SCROLL PROGRESS =====
function initScrollProgress() {
    // Create progress bar
    const progressBar = document.createElement('div');
    progressBar.className = 'scroll-progress';
    progressBar.innerHTML = '<div class="scroll-progress-bar"></div>';
    
    // Add styles
    progressBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: rgba(255, 255, 255, 0.1);
        z-index: 9999;
        pointer-events: none;
    `;
    
    const progressBarFill = progressBar.querySelector('.scroll-progress-bar');
    progressBarFill.style.cssText = `
        height: 100%;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        width: 0%;
        transition: width 0.1s ease;
    `;
    
    document.body.appendChild(progressBar);
    
    // Update progress on scroll
    window.addEventListener('scroll', () => {
        const scrollTop = window.scrollY;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        progressBarFill.style.width = Math.min(scrollPercent, 100) + '%';
    });
}

// ===== STEP COMPLETION TRACKING =====
function markStepCompleted(stepNumber) {
    const progressStep = document.querySelector(`.progress-step[data-step="${stepNumber}"]`);
    if (progressStep) {
        progressStep.classList.add('completed');
        
        // Save completion state
        localStorage.setItem(`nexusbot-setup-step-${stepNumber}`, 'completed');
    }
}

function loadCompletionState() {
    for (let i = 1; i <= 4; i++) {
        const isCompleted = localStorage.getItem(`nexusbot-setup-step-${i}`) === 'completed';
        if (isCompleted) {
            markStepCompleted(i);
        }
    }
}

// ===== INVITE BUTTON ENHANCEMENT =====
document.addEventListener('click', (e) => {
    if (e.target.closest('.invite-btn')) {
        e.preventDefault();
        
        // Mark step 1 as completed
        markStepCompleted(1);
        
        // Open invite URL
        const inviteUrl = 'https://discord.com/api/oauth2/authorize?client_id=1396212968743505930&permissions=8&scope=bot%20applications.commands';
        window.open(inviteUrl, '_blank');
        
        // Show success message
        showNotification('Opening Discord invite...', 'info');
        
        // Auto-advance to step 2 after a delay
        setTimeout(() => {
            updateActiveStep(2);
            scrollToStep(2);
        }, 2000);
    }
});

// ===== CONFIGURATION HELPERS =====
function showConfigurationTips() {
    const tips = [
        {
            title: 'Pro Tip: Permissions',
            message: 'Make sure NexusBot has the necessary permissions in each channel where you want it to work.'
        },
        {
            title: 'Pro Tip: Channels',
            message: 'Create dedicated channels for bot commands to keep your main channels clean.'
        },
        {
            title: 'Pro Tip: Roles',
            message: 'Set up role hierarchies properly to ensure moderation commands work correctly.'
        }
    ];
    
    // Show random tip
    const randomTip = tips[Math.floor(Math.random() * tips.length)];
    showNotification(`${randomTip.title}: ${randomTip.message}`, 'info', 5000);
}

// ===== KEYBOARD SHORTCUTS =====
document.addEventListener('keydown', (e) => {
    // Navigate steps with arrow keys
    if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
        const currentStep = document.querySelector('.progress-step.active');
        if (currentStep) {
            const currentStepNumber = parseInt(currentStep.getAttribute('data-step'));
            if (currentStepNumber < 4) {
                updateActiveStep(currentStepNumber + 1);
                scrollToStep(currentStepNumber + 1);
            }
        }
    } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
        const currentStep = document.querySelector('.progress-step.active');
        if (currentStep) {
            const currentStepNumber = parseInt(currentStep.getAttribute('data-step'));
            if (currentStepNumber > 1) {
                updateActiveStep(currentStepNumber - 1);
                scrollToStep(currentStepNumber - 1);
            }
        }
    }
});

// ===== ANIMATION HELPERS =====
function animateStepTransition(fromStep, toStep) {
    const fromElement = document.getElementById(`step-${fromStep}`);
    const toElement = document.getElementById(`step-${toStep}`);
    
    if (fromElement && toElement) {
        // Fade out current step
        fromElement.style.transition = 'opacity 0.3s ease';
        fromElement.style.opacity = '0.6';
        
        // Fade in new step
        setTimeout(() => {
            toElement.style.transition = 'opacity 0.3s ease';
            toElement.style.opacity = '1';
        }, 150);
    }
}

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Copied to clipboard!', 'success');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Copied to clipboard!', 'success');
    });
}

function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        padding: 1rem 1.5rem;
        background: var(--${type === 'success' ? 'success' : type === 'error' ? 'error' : 'info'}-color);
        color: white;
        border-radius: 0.5rem;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after duration
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, duration);
}

// ===== INITIALIZE ON LOAD =====
window.addEventListener('load', () => {
    loadCompletionState();
    
    // Show welcome tip after a delay
    setTimeout(() => {
        showConfigurationTips();
    }, 3000);
});

// ===== EXPORT FOR EXTERNAL USE =====
window.setupPageAPI = {
    updateActiveStep,
    scrollToStep,
    markStepCompleted,
    showConfigurationTips
};
