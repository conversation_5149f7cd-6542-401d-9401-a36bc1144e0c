require('dotenv').config();

module.exports = {
  // Discord Configuration
  discord: {
    token: process.env.DISCORD_TOKEN,
    clientId: process.env.CLIENT_ID,
    guildId: process.env.GUILD_ID,
    ownerId: process.env.BOT_OWNER_ID,
    supportServer: process.env.SUPPORT_SERVER_INVITE,
    website: process.env.BOT_WEBSITE
  },

  // Supabase Configuration
  supabase: {
    url: process.env.SUPABASE_URL,
    anonKey: process.env.SUPABASE_ANON_KEY,
    serviceKey: process.env.SUPABASE_SERVICE_KEY
  },

  // Database Configuration (fallback)
  database: {
    url: process.env.DATABASE_URL || 'sqlite:./data/nexusbot.db',
    dialect: process.env.DATABASE_URL?.startsWith('postgresql') ? 'postgres' : 'sqlite',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  },

  // API Keys
  apis: {
    openWeather: process.env.OPENWEATHER_API_KEY,
    googleTranslate: process.env.GOOGLE_TRANSLATE_API_KEY,
    youtube: process.env.YOUTUBE_API_KEY,
    spotify: {
      clientId: process.env.SPOTIFY_CLIENT_ID,
      clientSecret: process.env.SPOTIFY_CLIENT_SECRET,
      redirectUri: process.env.SPOTIFY_REDIRECT_URI
    },
    huggingface: {
      apiKey: process.env.HUGGINGFACE_API_KEY,
      baseUrl: process.env.HUGGINGFACE_API_URL || 'https://api-inference.huggingface.co',
      models: {
        toxicity: process.env.HF_TOXICITY_MODEL || 'unitary/toxic-bert',
        sentiment: process.env.HF_SENTIMENT_MODEL || 'cardiffnlp/twitter-roberta-base-sentiment-latest',
        textGeneration: process.env.HF_TEXT_MODEL || 'microsoft/DialoGPT-medium',
        classification: process.env.HF_CLASSIFICATION_MODEL || 'facebook/bart-large-mnli',
        imageCaption: process.env.HF_IMAGE_CAPTION_MODEL || 'Salesforce/blip-image-captioning-large',
        objectDetection: process.env.HF_OBJECT_DETECTION_MODEL || 'facebook/detr-resnet-50',
        nsfwDetection: process.env.HF_NSFW_MODEL || 'Falconsai/nsfw_image_detection',
        translation: process.env.HF_TRANSLATION_MODEL || 'Helsinki-NLP/opus-mt-en-es',
        summarization: process.env.HF_SUMMARIZATION_MODEL || 'facebook/bart-large-cnn',
        questionAnswering: process.env.HF_QA_MODEL || 'deepset/roberta-base-squad2'
      }
    },
    newsApi: process.env.NEWS_API_KEY,
    tmdb: process.env.TMDB_API_KEY
  },

  // Bot Settings
  bot: {
    defaultPrefix: process.env.DEFAULT_PREFIX || '!',
    embedColors: {
      primary: '#00D4FF',    // Electric Blue
      secondary: '#6B46C1',  // Deep Purple
      accent: '#00FF88',     // Neon Green
      background: '#1A1A1A', // Dark Gray
      success: '#00FF88',
      warning: '#FFD700',
      error: '#FF4444',
      info: '#00D4FF'
    },
    emojis: {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️',
      loading: '⏳',
      music: '🎵',
      money: '💰',
      level: '⭐'
    }
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs/',
    maxFiles: parseInt(process.env.MAX_LOG_FILES) || 14,
    datePattern: 'YYYY-MM-DD'
  },

  // Web Dashboard
  web: {
    port: parseInt(process.env.WEB_PORT) || 3000,
    host: process.env.WEB_HOST || 'localhost',
    jwtSecret: process.env.JWT_SECRET,
    sessionSecret: process.env.SESSION_SECRET
  },

  // Rate Limiting
  rateLimiting: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 60000,
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 10
  },

  // Economy System
  economy: {
    dailyReward: parseInt(process.env.DAILY_REWARD_AMOUNT) || 100,
    dailyCooldown: parseInt(process.env.DAILY_REWARD_COOLDOWN) || 86400000,
    startingBalance: parseInt(process.env.STARTING_BALANCE) || 1000,
    currency: {
      name: 'NexusCoins',
      symbol: '🪙'
    }
  },

  // Music System
  music: {
    maxQueueSize: parseInt(process.env.MAX_QUEUE_SIZE) || 50,
    defaultVolume: parseInt(process.env.DEFAULT_VOLUME) || 50,
    timeout: parseInt(process.env.MUSIC_TIMEOUT) || 300000
  },

  // Moderation
  moderation: {
    maxWarnings: parseInt(process.env.MAX_WARNINGS_BEFORE_ACTION) || 3,
    autoModEnabled: process.env.AUTO_MOD_ENABLED === 'true',
    spamThreshold: parseInt(process.env.SPAM_THRESHOLD) || 5,
    spamWindow: parseInt(process.env.SPAM_WINDOW) || 10000
  },

  // Redis Configuration
  redis: {
    enabled: process.env.REDIS_ENABLED === 'true',
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB) || 0
  },

  // Backup Configuration
  backup: {
    enabled: process.env.BACKUP_ENABLED !== 'false',
    maxBackups: parseInt(process.env.MAX_BACKUPS) || 30,
    compression: process.env.BACKUP_COMPRESSION !== 'false',
    schedule: {
      daily: process.env.DAILY_BACKUP_TIME || '0 2 * * *',
      weekly: process.env.WEEKLY_BACKUP_TIME || '0 3 * * 0',
      monthly: process.env.MONTHLY_BACKUP_TIME || '0 4 1 * *'
    }
  },

  // AI Features
  ai: {
    enabled: process.env.AI_FEATURES_ENABLED === 'true',
    chatbot: {
      enabled: process.env.AI_CHATBOT_ENABLED === 'true',
      personality: process.env.AI_PERSONALITY || 'helpful',
      maxHistory: parseInt(process.env.AI_MAX_HISTORY) || 10
    },
    moderation: {
      enabled: process.env.AI_MODERATION_ENABLED === 'true',
      toxicityThreshold: parseFloat(process.env.AI_TOXICITY_THRESHOLD) || 0.7
    }
  },

  // Advanced Features
  features: {
    autoMod: {
      enabled: process.env.AUTO_MOD_ENABLED === 'true',
      antiSpam: process.env.ANTI_SPAM_ENABLED !== 'false',
      antiRaid: process.env.ANTI_RAID_ENABLED !== 'false',
      linkFilter: process.env.LINK_FILTER_ENABLED === 'true',
      wordFilter: process.env.WORD_FILTER_ENABLED === 'true'
    },
    leveling: {
      enabled: process.env.LEVELING_ENABLED !== 'false',
      xpPerMessage: parseInt(process.env.XP_PER_MESSAGE) || 15,
      xpCooldown: parseInt(process.env.XP_COOLDOWN) || 60000,
      levelUpMessage: process.env.LEVEL_UP_MESSAGE !== 'false'
    },
    reminders: {
      enabled: process.env.REMINDERS_ENABLED !== 'false',
      maxPerUser: parseInt(process.env.MAX_REMINDERS_PER_USER) || 10
    },
    customCommands: {
      enabled: process.env.CUSTOM_COMMANDS_ENABLED !== 'false',
      maxPerGuild: parseInt(process.env.MAX_CUSTOM_COMMANDS_PER_GUILD) || 50
    }
  },

  // Security
  security: {
    encryptionKey: process.env.ENCRYPTION_KEY,
    rateLimiting: {
      enabled: process.env.RATE_LIMITING_ENABLED !== 'false',
      strict: process.env.STRICT_RATE_LIMITING === 'true'
    },
    permissions: {
      strictMode: process.env.STRICT_PERMISSIONS === 'true',
      ownerBypass: process.env.OWNER_BYPASS !== 'false'
    }
  },

  // Monitoring
  monitoring: {
    enabled: process.env.MONITORING_ENABLED !== 'false',
    healthChecks: process.env.HEALTH_CHECKS_ENABLED !== 'false',
    metrics: process.env.METRICS_ENABLED !== 'false',
    alerts: {
      discord: process.env.ALERT_DISCORD_WEBHOOK,
      email: process.env.ALERT_EMAIL
    }
  },

  // Development
  development: {
    nodeEnv: process.env.NODE_ENV || 'development',
    debug: process.env.DEBUG === 'true',
    testMode: process.env.TEST_MODE === 'true',
    mockApis: process.env.MOCK_APIS === 'true'
  }
};
