require('dotenv').config();

module.exports = {
  // Discord Configuration
  discord: {
    token: process.env.DISCORD_TOKEN,
    clientId: process.env.CLIENT_ID,
    guildId: process.env.GUILD_ID,
    ownerId: process.env.BOT_OWNER_ID,
    supportServer: process.env.SUPPORT_SERVER_INVITE,
    website: process.env.BOT_WEBSITE
  },

  // Supabase Configuration
  supabase: {
    url: process.env.SUPABASE_URL,
    anonKey: process.env.SUPABASE_ANON_KEY,
    serviceKey: process.env.SUPABASE_SERVICE_KEY
  },

  // Database Configuration (fallback)
  database: {
    url: process.env.DATABASE_URL || 'sqlite:./data/nexusbot.db',
    dialect: process.env.DATABASE_URL?.startsWith('postgresql') ? 'postgres' : 'sqlite',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  },

  // API Keys
  apis: {
    openWeather: process.env.OPENWEATHER_API_KEY,
    googleTranslate: process.env.GOOGLE_TRANSLATE_API_KEY,
    youtube: process.env.YOUTUBE_API_KEY,
    spotify: {
      clientId: process.env.SPOTIFY_CLIENT_ID,
      clientSecret: process.env.SPOTIFY_CLIENT_SECRET
    }
  },

  // Bot Settings
  bot: {
    defaultPrefix: process.env.DEFAULT_PREFIX || '!',
    embedColors: {
      primary: '#00D4FF',    // Electric Blue
      secondary: '#6B46C1',  // Deep Purple
      accent: '#00FF88',     // Neon Green
      background: '#1A1A1A', // Dark Gray
      success: '#00FF88',
      warning: '#FFD700',
      error: '#FF4444',
      info: '#00D4FF'
    },
    emojis: {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️',
      loading: '⏳',
      music: '🎵',
      money: '💰',
      level: '⭐'
    }
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs/',
    maxFiles: parseInt(process.env.MAX_LOG_FILES) || 14,
    datePattern: 'YYYY-MM-DD'
  },

  // Web Dashboard
  web: {
    port: parseInt(process.env.WEB_PORT) || 3000,
    host: process.env.WEB_HOST || 'localhost',
    jwtSecret: process.env.JWT_SECRET,
    sessionSecret: process.env.SESSION_SECRET
  },

  // Rate Limiting
  rateLimiting: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 60000,
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 10
  },

  // Economy System
  economy: {
    dailyReward: parseInt(process.env.DAILY_REWARD_AMOUNT) || 100,
    dailyCooldown: parseInt(process.env.DAILY_REWARD_COOLDOWN) || 86400000,
    startingBalance: parseInt(process.env.STARTING_BALANCE) || 1000,
    currency: {
      name: 'NexusCoins',
      symbol: '🪙'
    }
  },

  // Music System
  music: {
    maxQueueSize: parseInt(process.env.MAX_QUEUE_SIZE) || 50,
    defaultVolume: parseInt(process.env.DEFAULT_VOLUME) || 50,
    timeout: parseInt(process.env.MUSIC_TIMEOUT) || 300000
  },

  // Moderation
  moderation: {
    maxWarnings: parseInt(process.env.MAX_WARNINGS_BEFORE_ACTION) || 3,
    autoModEnabled: process.env.AUTO_MOD_ENABLED === 'true',
    spamThreshold: parseInt(process.env.SPAM_THRESHOLD) || 5,
    spamWindow: parseInt(process.env.SPAM_WINDOW) || 10000
  },

  // Development
  development: {
    nodeEnv: process.env.NODE_ENV || 'development',
    debug: process.env.DEBUG === 'true'
  }
};
