const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const fs = require('fs');
const chalk = require('chalk');
const config = require('../config/config');

// Ensure logs directory exists
const logsDir = path.resolve(config.logging.filePath);
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Enhanced console format with better colors and formatting
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    // Color coding for different log levels
    const colors = {
      error: chalk.red,
      warn: chalk.yellow,
      info: chalk.blue,
      debug: chalk.gray,
      verbose: chalk.cyan
    };

    const colorFn = colors[level] || chalk.white;
    const levelIcon = {
      error: '❌',
      warn: '⚠️',
      info: 'ℹ️',
      debug: '🔍',
      verbose: '📝'
    };

    let output = `${chalk.gray(timestamp)} ${levelIcon[level] || '•'} ${colorFn(level.toUpperCase())}: ${message}`;

    // Add stack trace for errors
    if (stack) {
      output += '\n' + chalk.red(stack);
    }

    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      output += '\n' + chalk.gray(JSON.stringify(meta, null, 2));
    }

    return output;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
  level: config.logging.level,
  format: fileFormat,
  defaultMeta: { service: 'nexusbot' },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: consoleFormat,
      silent: config.development.nodeEnv === 'production'
    }),

    // Daily rotate file for all logs
    new DailyRotateFile({
      filename: path.join(logsDir, 'nexusbot-%DATE%.log'),
      datePattern: config.logging.datePattern,
      maxFiles: config.logging.maxFiles,
      format: fileFormat,
      level: 'info'
    }),

    // Separate file for errors
    new DailyRotateFile({
      filename: path.join(logsDir, 'error-%DATE%.log'),
      datePattern: config.logging.datePattern,
      maxFiles: config.logging.maxFiles,
      format: fileFormat,
      level: 'error'
    }),

    // Debug file for development
    new DailyRotateFile({
      filename: path.join(logsDir, 'debug-%DATE%.log'),
      datePattern: config.logging.datePattern,
      maxFiles: config.logging.maxFiles,
      format: fileFormat,
      level: 'debug',
      silent: config.development.nodeEnv === 'production'
    })
  ],

  // Handle exceptions and rejections
  exceptionHandlers: [
    new DailyRotateFile({
      filename: path.join(logsDir, 'exceptions-%DATE%.log'),
      datePattern: config.logging.datePattern,
      maxFiles: config.logging.maxFiles,
      format: fileFormat
    })
  ],

  rejectionHandlers: [
    new DailyRotateFile({
      filename: path.join(logsDir, 'rejections-%DATE%.log'),
      datePattern: config.logging.datePattern,
      maxFiles: config.logging.maxFiles,
      format: fileFormat
    })
  ]
});

// Add custom logging methods for Discord-specific events
logger.command = (commandName, userId, guildId, args = []) => {
  logger.info('Command executed', {
    command: commandName,
    user: userId,
    guild: guildId,
    arguments: args,
    timestamp: new Date().toISOString()
  });
};

logger.moderation = (action, moderator, target, reason, guildId) => {
  logger.warn('Moderation action', {
    action,
    moderator,
    target,
    reason,
    guild: guildId,
    timestamp: new Date().toISOString()
  });
};

logger.economy = (action, userId, amount, balance, guildId) => {
  logger.info('Economy transaction', {
    action,
    user: userId,
    amount,
    newBalance: balance,
    guild: guildId,
    timestamp: new Date().toISOString()
  });
};

logger.music = (action, userId, guildId, details = {}) => {
  logger.info('Music action', {
    action,
    user: userId,
    guild: guildId,
    details,
    timestamp: new Date().toISOString()
  });
};

logger.security = (event, userId, guildId, details = {}) => {
  logger.warn('Security event', {
    event,
    user: userId,
    guild: guildId,
    details,
    timestamp: new Date().toISOString()
  });
};

// Enhanced logging methods for new features
logger.automod = (action, userId, guildId, feature, details = {}) => {
  logger.info('Auto-moderation action', {
    action,
    user: userId,
    guild: guildId,
    feature,
    details,
    timestamp: new Date().toISOString()
  });
};

logger.reminder = (action, userId, guildId, reminderData = {}) => {
  logger.info('Reminder action', {
    action,
    user: userId,
    guild: guildId,
    reminder: reminderData,
    timestamp: new Date().toISOString()
  });
};

logger.customCommand = (action, commandName, userId, guildId, details = {}) => {
  logger.info('Custom command action', {
    action,
    command: commandName,
    user: userId,
    guild: guildId,
    details,
    timestamp: new Date().toISOString()
  });
};

logger.backup = (action, type, success, details = {}) => {
  const level = success ? 'info' : 'error';
  logger[level]('Backup operation', {
    action,
    type,
    success,
    details,
    timestamp: new Date().toISOString()
  });
};

logger.health = (component, status, metrics = {}) => {
  const level = status === 'healthy' ? 'info' : status === 'warning' ? 'warn' : 'error';
  logger[level]('Health check', {
    component,
    status,
    metrics,
    timestamp: new Date().toISOString()
  });
};

logger.performance = (operation, duration, details = {}) => {
  const level = duration > 5000 ? 'warn' : 'debug'; // Warn if operation takes > 5 seconds
  logger[level]('Performance metric', {
    operation,
    duration: `${duration}ms`,
    details,
    timestamp: new Date().toISOString()
  });
};

logger.api = (service, endpoint, status, duration, details = {}) => {
  const level = status >= 400 ? 'error' : status >= 300 ? 'warn' : 'debug';
  logger[level]('API call', {
    service,
    endpoint,
    status,
    duration: `${duration}ms`,
    details,
    timestamp: new Date().toISOString()
  });
};

// Utility methods
logger.logWithContext = (level, message, context = {}) => {
  logger[level](message, {
    ...context,
    timestamp: new Date().toISOString()
  });
};

logger.startTimer = (label) => {
  const start = Date.now();
  return {
    end: (details = {}) => {
      const duration = Date.now() - start;
      logger.performance(label, duration, details);
      return duration;
    }
  };
};

// Error handling with context
logger.errorWithContext = (error, context = {}) => {
  logger.error(error.message, {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    },
    context,
    timestamp: new Date().toISOString()
  });
};

module.exports = logger;
