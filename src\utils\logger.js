const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const fs = require('fs');
const config = require('../config/config');

// Ensure logs directory exists
const logsDir = path.resolve(config.logging.filePath);
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let metaString = '';
    if (Object.keys(meta).length > 0) {
      metaString = '\n' + JSON.stringify(meta, null, 2);
    }
    return `${timestamp} [${level}]: ${message}${metaString}`;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
  level: config.logging.level,
  format: fileFormat,
  defaultMeta: { service: 'nexusbot' },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: consoleFormat,
      silent: config.development.nodeEnv === 'production'
    }),

    // Daily rotate file for all logs
    new DailyRotateFile({
      filename: path.join(logsDir, 'nexusbot-%DATE%.log'),
      datePattern: config.logging.datePattern,
      maxFiles: config.logging.maxFiles,
      format: fileFormat,
      level: 'info'
    }),

    // Separate file for errors
    new DailyRotateFile({
      filename: path.join(logsDir, 'error-%DATE%.log'),
      datePattern: config.logging.datePattern,
      maxFiles: config.logging.maxFiles,
      format: fileFormat,
      level: 'error'
    }),

    // Debug file for development
    new DailyRotateFile({
      filename: path.join(logsDir, 'debug-%DATE%.log'),
      datePattern: config.logging.datePattern,
      maxFiles: config.logging.maxFiles,
      format: fileFormat,
      level: 'debug',
      silent: config.development.nodeEnv === 'production'
    })
  ],

  // Handle exceptions and rejections
  exceptionHandlers: [
    new DailyRotateFile({
      filename: path.join(logsDir, 'exceptions-%DATE%.log'),
      datePattern: config.logging.datePattern,
      maxFiles: config.logging.maxFiles,
      format: fileFormat
    })
  ],

  rejectionHandlers: [
    new DailyRotateFile({
      filename: path.join(logsDir, 'rejections-%DATE%.log'),
      datePattern: config.logging.datePattern,
      maxFiles: config.logging.maxFiles,
      format: fileFormat
    })
  ]
});

// Add custom logging methods for Discord-specific events
logger.command = (commandName, userId, guildId, args = []) => {
  logger.info('Command executed', {
    command: commandName,
    user: userId,
    guild: guildId,
    arguments: args,
    timestamp: new Date().toISOString()
  });
};

logger.moderation = (action, moderator, target, reason, guildId) => {
  logger.warn('Moderation action', {
    action,
    moderator,
    target,
    reason,
    guild: guildId,
    timestamp: new Date().toISOString()
  });
};

logger.economy = (action, userId, amount, balance, guildId) => {
  logger.info('Economy transaction', {
    action,
    user: userId,
    amount,
    newBalance: balance,
    guild: guildId,
    timestamp: new Date().toISOString()
  });
};

logger.music = (action, userId, guildId, details = {}) => {
  logger.info('Music action', {
    action,
    user: userId,
    guild: guildId,
    details,
    timestamp: new Date().toISOString()
  });
};

logger.security = (event, userId, guildId, details = {}) => {
  logger.warn('Security event', {
    event,
    user: userId,
    guild: guildId,
    details,
    timestamp: new Date().toISOString()
  });
};

module.exports = logger;
