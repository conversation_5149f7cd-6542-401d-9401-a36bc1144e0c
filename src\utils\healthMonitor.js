/**
 * Health Monitor for NexusBot
 * Monitors bot health, performance metrics, and system status
 * <AUTHOR> Team
 * @version 2.0.0
 */

const os = require('os');
const process = require('process');
const logger = require('./logger');
const cron = require('node-cron');

class HealthMonitor {
  constructor() {
    this.client = null;
    this.metrics = {
      startTime: Date.now(),
      commandsExecuted: 0,
      messagesProcessed: 0,
      errorsCount: 0,
      memoryUsage: [],
      cpuUsage: [],
      latency: [],
      guildCount: 0,
      userCount: 0,
      voiceConnections: 0
    };
    
    this.thresholds = {
      memoryUsage: 80, // Percentage
      cpuUsage: 80, // Percentage
      latency: 1000, // Milliseconds
      errorRate: 10 // Errors per minute
    };

    this.alerts = {
      highMemory: false,
      highCpu: false,
      highLatency: false,
      highErrorRate: false,
      databaseDown: false
    };

    this.isMonitoring = false;
  }

  /**
   * Initialize health monitor
   * @param {Client} client - Discord client instance
   * @returns {Promise<void>}
   */
  async initialize(client) {
    this.client = client;
    this.startMonitoring();
    this.setupEventListeners();
    logger.info('✅ Health monitor initialized');
  }

  /**
   * Start monitoring processes
   */
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;

    // Monitor every 30 seconds
    this.monitorInterval = setInterval(() => {
      this.collectMetrics();
      this.checkThresholds();
    }, 30000);

    // Detailed health check every 5 minutes
    cron.schedule('*/5 * * * *', () => {
      this.performHealthCheck();
    });

    // Clean old metrics every hour
    cron.schedule('0 * * * *', () => {
      this.cleanOldMetrics();
    });

    logger.info('Health monitoring started');
  }

  /**
   * Set up event listeners for metrics collection
   */
  setupEventListeners() {
    if (!this.client) return;

    // Track command executions
    this.client.on('interactionCreate', (interaction) => {
      if (interaction.isCommand()) {
        this.metrics.commandsExecuted++;
      }
    });

    // Track message processing
    this.client.on('messageCreate', () => {
      this.metrics.messagesProcessed++;
    });

    // Track errors
    this.client.on('error', (error) => {
      this.metrics.errorsCount++;
      logger.error('Discord client error:', error);
    });

    // Track warnings
    this.client.on('warn', (warning) => {
      logger.warn('Discord client warning:', warning);
    });

    // Track ready state
    this.client.on('ready', () => {
      this.updateGuildMetrics();
    });

    // Track guild changes
    this.client.on('guildCreate', () => {
      this.updateGuildMetrics();
    });

    this.client.on('guildDelete', () => {
      this.updateGuildMetrics();
    });
  }

  /**
   * Collect system and bot metrics
   */
  collectMetrics() {
    try {
      // Memory usage
      const memUsage = process.memoryUsage();
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const usedMemory = totalMemory - freeMemory;
      const memoryPercentage = (usedMemory / totalMemory) * 100;

      this.metrics.memoryUsage.push({
        timestamp: Date.now(),
        heap: memUsage.heapUsed,
        rss: memUsage.rss,
        external: memUsage.external,
        systemUsed: usedMemory,
        systemTotal: totalMemory,
        percentage: memoryPercentage
      });

      // CPU usage
      const cpuUsage = process.cpuUsage();
      const cpuPercentage = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds

      this.metrics.cpuUsage.push({
        timestamp: Date.now(),
        user: cpuUsage.user,
        system: cpuUsage.system,
        percentage: cpuPercentage
      });

      // Bot latency
      if (this.client && this.client.ws) {
        this.metrics.latency.push({
          timestamp: Date.now(),
          ws: this.client.ws.ping,
          api: this.client.rest.ping || 0
        });
      }

      // Update guild metrics
      this.updateGuildMetrics();

    } catch (error) {
      logger.error('Error collecting metrics:', error);
    }
  }

  /**
   * Update guild-related metrics
   */
  updateGuildMetrics() {
    if (!this.client || !this.client.guilds) return;

    this.metrics.guildCount = this.client.guilds.cache.size;
    this.metrics.userCount = this.client.guilds.cache.reduce(
      (acc, guild) => acc + guild.memberCount, 0
    );
    this.metrics.voiceConnections = this.client.voice?.adapters?.size || 0;
  }

  /**
   * Check if metrics exceed thresholds and trigger alerts
   */
  checkThresholds() {
    try {
      // Check memory usage
      const latestMemory = this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1];
      if (latestMemory && latestMemory.percentage > this.thresholds.memoryUsage) {
        if (!this.alerts.highMemory) {
          this.triggerAlert('highMemory', `High memory usage: ${latestMemory.percentage.toFixed(2)}%`);
        }
      } else {
        this.alerts.highMemory = false;
      }

      // Check latency
      const latestLatency = this.metrics.latency[this.metrics.latency.length - 1];
      if (latestLatency && latestLatency.ws > this.thresholds.latency) {
        if (!this.alerts.highLatency) {
          this.triggerAlert('highLatency', `High latency: ${latestLatency.ws}ms`);
        }
      } else {
        this.alerts.highLatency = false;
      }

      // Check error rate (errors in last minute)
      const oneMinuteAgo = Date.now() - 60000;
      const recentErrors = this.metrics.errorsCount; // Simplified for now
      if (recentErrors > this.thresholds.errorRate) {
        if (!this.alerts.highErrorRate) {
          this.triggerAlert('highErrorRate', `High error rate: ${recentErrors} errors/min`);
        }
      } else {
        this.alerts.highErrorRate = false;
      }

    } catch (error) {
      logger.error('Error checking thresholds:', error);
    }
  }

  /**
   * Trigger health alert
   * @param {string} alertType - Type of alert
   * @param {string} message - Alert message
   */
  triggerAlert(alertType, message) {
    this.alerts[alertType] = true;
    logger.warn(`🚨 HEALTH ALERT [${alertType}]: ${message}`);
    
    // Here you could send alerts to Discord channels, webhooks, etc.
    // For now, we'll just log them
  }

  /**
   * Perform comprehensive health check
   * @returns {Promise<Object>} Health check results
   */
  async performHealthCheck() {
    const healthCheck = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      uptime: Date.now() - this.metrics.startTime,
      checks: {}
    };

    try {
      // Discord connection check
      healthCheck.checks.discord = {
        status: this.client && this.client.isReady() ? 'healthy' : 'unhealthy',
        latency: this.client?.ws?.ping || 0,
        guilds: this.metrics.guildCount,
        users: this.metrics.userCount
      };

      // Database check
      try {
        const dbManager = require('../database');
        const dbHealth = await dbManager.healthCheck();
        healthCheck.checks.database = dbHealth;
      } catch (error) {
        healthCheck.checks.database = {
          status: 'unhealthy',
          error: error.message
        };
      }

      // Memory check
      const latestMemory = this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1];
      healthCheck.checks.memory = {
        status: latestMemory?.percentage > this.thresholds.memoryUsage ? 'warning' : 'healthy',
        usage: latestMemory?.percentage || 0,
        heap: latestMemory?.heap || 0
      };

      // Performance check
      healthCheck.checks.performance = {
        commandsExecuted: this.metrics.commandsExecuted,
        messagesProcessed: this.metrics.messagesProcessed,
        errorsCount: this.metrics.errorsCount,
        averageLatency: this.getAverageLatency()
      };

      // Determine overall status
      const unhealthyChecks = Object.values(healthCheck.checks)
        .filter(check => check.status === 'unhealthy');
      
      if (unhealthyChecks.length > 0) {
        healthCheck.status = 'unhealthy';
      } else {
        const warningChecks = Object.values(healthCheck.checks)
          .filter(check => check.status === 'warning');
        if (warningChecks.length > 0) {
          healthCheck.status = 'warning';
        }
      }

      logger.info(`Health check completed - Status: ${healthCheck.status}`);
      return healthCheck;

    } catch (error) {
      logger.error('Health check failed:', error);
      healthCheck.status = 'error';
      healthCheck.error = error.message;
      return healthCheck;
    }
  }

  /**
   * Get average latency from recent measurements
   * @returns {number} Average latency in milliseconds
   */
  getAverageLatency() {
    if (this.metrics.latency.length === 0) return 0;
    
    const recent = this.metrics.latency.slice(-10); // Last 10 measurements
    const sum = recent.reduce((acc, measurement) => acc + measurement.ws, 0);
    return Math.round(sum / recent.length);
  }

  /**
   * Clean old metrics to prevent memory leaks
   */
  cleanOldMetrics() {
    const oneHourAgo = Date.now() - 3600000; // 1 hour ago
    
    this.metrics.memoryUsage = this.metrics.memoryUsage.filter(
      metric => metric.timestamp > oneHourAgo
    );
    
    this.metrics.cpuUsage = this.metrics.cpuUsage.filter(
      metric => metric.timestamp > oneHourAgo
    );
    
    this.metrics.latency = this.metrics.latency.filter(
      metric => metric.timestamp > oneHourAgo
    );

    logger.debug('Old metrics cleaned');
  }

  /**
   * Get current health status
   * @returns {Object} Current health metrics
   */
  getHealthStatus() {
    return {
      status: this.alerts,
      metrics: {
        uptime: Date.now() - this.metrics.startTime,
        commandsExecuted: this.metrics.commandsExecuted,
        messagesProcessed: this.metrics.messagesProcessed,
        errorsCount: this.metrics.errorsCount,
        guildCount: this.metrics.guildCount,
        userCount: this.metrics.userCount,
        averageLatency: this.getAverageLatency(),
        memoryUsage: this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]
      }
    };
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    this.isMonitoring = false;
    logger.info('Health monitoring stopped');
  }
}

module.exports = new HealthMonitor();
