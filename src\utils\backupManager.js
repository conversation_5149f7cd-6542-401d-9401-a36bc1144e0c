/**
 * Backup Manager for NexusBot
 * Handles automated backups of database and configuration data
 * <AUTHOR> Team
 * @version 2.0.0
 */

const fs = require('fs').promises;
const path = require('path');
const cron = require('node-cron');
const { createGzip } = require('zlib');
const { pipeline } = require('stream/promises');
const logger = require('./logger');
const config = require('../config/config');

class BackupManager {
  constructor() {
    this.backupDir = path.join(process.cwd(), 'backups');
    this.isInitialized = false;
    this.scheduledJobs = new Map();
    this.maxBackups = config.backup?.maxBackups || 30;
    this.compressionEnabled = config.backup?.compression !== false;
  }

  /**
   * Initialize backup manager
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Create backup directory if it doesn't exist
      await this.ensureBackupDirectory();
      
      // Schedule automatic backups
      this.scheduleBackups();
      
      this.isInitialized = true;
      logger.info('✅ Backup manager initialized');
    } catch (error) {
      logger.error('Failed to initialize backup manager:', error);
      throw error;
    }
  }

  /**
   * Ensure backup directory exists
   * @returns {Promise<void>}
   */
  async ensureBackupDirectory() {
    try {
      await fs.access(this.backupDir);
    } catch (error) {
      // Directory doesn't exist, create it
      await fs.mkdir(this.backupDir, { recursive: true });
      logger.info(`Created backup directory: ${this.backupDir}`);
    }
  }

  /**
   * Schedule automatic backups
   */
  scheduleBackups() {
    // Daily database backup at 2 AM
    const dailyBackup = cron.schedule('0 2 * * *', async () => {
      await this.createDatabaseBackup('daily');
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    // Weekly full backup on Sundays at 3 AM
    const weeklyBackup = cron.schedule('0 3 * * 0', async () => {
      await this.createFullBackup('weekly');
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    // Monthly backup on the 1st at 4 AM
    const monthlyBackup = cron.schedule('0 4 1 * *', async () => {
      await this.createFullBackup('monthly');
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.scheduledJobs.set('daily', dailyBackup);
    this.scheduledJobs.set('weekly', weeklyBackup);
    this.scheduledJobs.set('monthly', monthlyBackup);

    // Start scheduled jobs
    dailyBackup.start();
    weeklyBackup.start();
    monthlyBackup.start();

    logger.info('Backup schedules configured');
  }

  /**
   * Create database backup
   * @param {string} type - Backup type (daily, weekly, monthly, manual)
   * @returns {Promise<string>} Backup file path
   */
  async createDatabaseBackup(type = 'manual') {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `database-${type}-${timestamp}.json`;
      const filepath = path.join(this.backupDir, filename);

      logger.info(`Creating database backup: ${filename}`);

      // Get database manager
      const dbManager = require('../database');
      
      // Export all data
      const backupData = {
        metadata: {
          timestamp: new Date().toISOString(),
          type: type,
          version: '2.0.0',
          databaseType: dbManager.getDatabaseType()
        },
        data: {}
      };

      // Export each table/collection
      try {
        backupData.data.guilds = await dbManager.exportTable('guilds');
        backupData.data.users = await dbManager.exportTable('users');
        backupData.data.economy = await dbManager.exportTable('economy');
        backupData.data.moderation = await dbManager.exportTable('moderation');
        backupData.data.music = await dbManager.exportTable('music');
        backupData.data.settings = await dbManager.exportTable('settings');
      } catch (error) {
        logger.warn('Some tables could not be exported:', error.message);
      }

      // Write backup file
      const backupJson = JSON.stringify(backupData, null, 2);
      
      if (this.compressionEnabled) {
        // Compress the backup
        const compressedPath = filepath + '.gz';
        await this.compressData(backupJson, compressedPath);
        await fs.unlink(filepath).catch(() => {}); // Remove uncompressed file
        
        logger.info(`Database backup created and compressed: ${compressedPath}`);
        return compressedPath;
      } else {
        await fs.writeFile(filepath, backupJson, 'utf8');
        logger.info(`Database backup created: ${filepath}`);
        return filepath;
      }

    } catch (error) {
      logger.error('Failed to create database backup:', error);
      throw error;
    }
  }

  /**
   * Create full backup including configuration and logs
   * @param {string} type - Backup type
   * @returns {Promise<string>} Backup file path
   */
  async createFullBackup(type = 'manual') {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `full-backup-${type}-${timestamp}.json`;
      const filepath = path.join(this.backupDir, filename);

      logger.info(`Creating full backup: ${filename}`);

      // Create database backup data
      const dbBackupPath = await this.createDatabaseBackup(type);
      
      // Read database backup
      let dbBackupData;
      if (dbBackupPath.endsWith('.gz')) {
        dbBackupData = await this.decompressData(dbBackupPath);
      } else {
        dbBackupData = await fs.readFile(dbBackupPath, 'utf8');
      }

      const fullBackup = {
        metadata: {
          timestamp: new Date().toISOString(),
          type: type,
          version: '2.0.0'
        },
        database: JSON.parse(dbBackupData),
        configuration: await this.backupConfiguration(),
        logs: await this.backupRecentLogs()
      };

      // Write full backup
      const backupJson = JSON.stringify(fullBackup, null, 2);
      
      if (this.compressionEnabled) {
        const compressedPath = filepath + '.gz';
        await this.compressData(backupJson, compressedPath);
        logger.info(`Full backup created and compressed: ${compressedPath}`);
        return compressedPath;
      } else {
        await fs.writeFile(filepath, backupJson, 'utf8');
        logger.info(`Full backup created: ${filepath}`);
        return filepath;
      }

    } catch (error) {
      logger.error('Failed to create full backup:', error);
      throw error;
    }
  }

  /**
   * Backup configuration files
   * @returns {Promise<Object>} Configuration backup data
   */
  async backupConfiguration() {
    try {
      const configBackup = {
        config: { ...config },
        packageJson: {}
      };

      // Remove sensitive data
      if (configBackup.config.discord) {
        configBackup.config.discord.token = '[REDACTED]';
      }
      if (configBackup.config.supabase) {
        configBackup.config.supabase.key = '[REDACTED]';
        configBackup.config.supabase.serviceKey = '[REDACTED]';
      }
      if (configBackup.config.openai) {
        configBackup.config.openai.apiKey = '[REDACTED]';
      }

      // Backup package.json
      try {
        const packagePath = path.join(process.cwd(), 'package.json');
        const packageData = await fs.readFile(packagePath, 'utf8');
        configBackup.packageJson = JSON.parse(packageData);
      } catch (error) {
        logger.warn('Could not backup package.json:', error.message);
      }

      return configBackup;
    } catch (error) {
      logger.error('Failed to backup configuration:', error);
      return {};
    }
  }

  /**
   * Backup recent log files
   * @returns {Promise<Object>} Log backup data
   */
  async backupRecentLogs() {
    try {
      const logsDir = path.join(process.cwd(), 'logs');
      const logBackup = {};

      try {
        const logFiles = await fs.readdir(logsDir);
        const recentLogs = logFiles
          .filter(file => file.endsWith('.log'))
          .sort()
          .slice(-5); // Last 5 log files

        for (const logFile of recentLogs) {
          try {
            const logPath = path.join(logsDir, logFile);
            const logContent = await fs.readFile(logPath, 'utf8');
            logBackup[logFile] = logContent.split('\n').slice(-100).join('\n'); // Last 100 lines
          } catch (error) {
            logger.warn(`Could not read log file ${logFile}:`, error.message);
          }
        }
      } catch (error) {
        logger.warn('Could not access logs directory:', error.message);
      }

      return logBackup;
    } catch (error) {
      logger.error('Failed to backup logs:', error);
      return {};
    }
  }

  /**
   * Compress data using gzip
   * @param {string} data - Data to compress
   * @param {string} outputPath - Output file path
   * @returns {Promise<void>}
   */
  async compressData(data, outputPath) {
    const { createReadStream, createWriteStream } = require('fs');
    const { Readable } = require('stream');
    
    const readable = Readable.from([data]);
    const writeStream = createWriteStream(outputPath);
    const gzip = createGzip();

    await pipeline(readable, gzip, writeStream);
  }

  /**
   * Decompress gzipped data
   * @param {string} filePath - Path to compressed file
   * @returns {Promise<string>} Decompressed data
   */
  async decompressData(filePath) {
    const { createReadStream } = require('fs');
    const { createGunzip } = require('zlib');
    const { Readable } = require('stream');

    const chunks = [];
    const readStream = createReadStream(filePath);
    const gunzip = createGunzip();

    await pipeline(
      readStream,
      gunzip,
      new Readable({
        write(chunk, encoding, callback) {
          chunks.push(chunk);
          callback();
        }
      })
    );

    return Buffer.concat(chunks).toString('utf8');
  }

  /**
   * Restore from backup
   * @param {string} backupPath - Path to backup file
   * @returns {Promise<boolean>} Success status
   */
  async restoreFromBackup(backupPath) {
    try {
      logger.info(`Restoring from backup: ${backupPath}`);

      // Read backup file
      let backupData;
      if (backupPath.endsWith('.gz')) {
        backupData = await this.decompressData(backupPath);
      } else {
        backupData = await fs.readFile(backupPath, 'utf8');
      }

      const backup = JSON.parse(backupData);
      
      // Get database manager
      const dbManager = require('../database');

      // Restore database data
      if (backup.data || backup.database?.data) {
        const dataToRestore = backup.data || backup.database.data;
        
        for (const [tableName, tableData] of Object.entries(dataToRestore)) {
          if (Array.isArray(tableData) && tableData.length > 0) {
            await dbManager.importTable(tableName, tableData);
            logger.info(`Restored ${tableData.length} records to ${tableName}`);
          }
        }
      }

      logger.info('Backup restoration completed successfully');
      return true;

    } catch (error) {
      logger.error('Failed to restore from backup:', error);
      return false;
    }
  }

  /**
   * List available backups
   * @returns {Promise<Array>} List of backup files
   */
  async listBackups() {
    try {
      const files = await fs.readdir(this.backupDir);
      const backups = [];

      for (const file of files) {
        if (file.endsWith('.json') || file.endsWith('.json.gz')) {
          const filepath = path.join(this.backupDir, file);
          const stats = await fs.stat(filepath);
          
          backups.push({
            filename: file,
            path: filepath,
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime
          });
        }
      }

      return backups.sort((a, b) => b.created - a.created);
    } catch (error) {
      logger.error('Failed to list backups:', error);
      return [];
    }
  }

  /**
   * Clean old backups based on retention policy
   * @returns {Promise<void>}
   */
  async cleanOldBackups() {
    try {
      const backups = await this.listBackups();
      
      if (backups.length > this.maxBackups) {
        const toDelete = backups.slice(this.maxBackups);
        
        for (const backup of toDelete) {
          await fs.unlink(backup.path);
          logger.info(`Deleted old backup: ${backup.filename}`);
        }
        
        logger.info(`Cleaned ${toDelete.length} old backups`);
      }
    } catch (error) {
      logger.error('Failed to clean old backups:', error);
    }
  }

  /**
   * Get backup statistics
   * @returns {Promise<Object>} Backup statistics
   */
  async getStats() {
    try {
      const backups = await this.listBackups();
      const totalSize = backups.reduce((sum, backup) => sum + backup.size, 0);
      
      return {
        totalBackups: backups.length,
        totalSize: totalSize,
        oldestBackup: backups[backups.length - 1]?.created || null,
        newestBackup: backups[0]?.created || null,
        scheduledJobs: Array.from(this.scheduledJobs.keys())
      };
    } catch (error) {
      logger.error('Failed to get backup stats:', error);
      return {};
    }
  }

  /**
   * Stop all scheduled backup jobs
   */
  stopScheduledJobs() {
    for (const [name, job] of this.scheduledJobs) {
      job.stop();
      logger.info(`Stopped scheduled backup job: ${name}`);
    }
    this.scheduledJobs.clear();
  }
}

module.exports = new BackupManager();
