const { SlashCommandBuilder } = require('discord.js');
const embeds = require('../../utils/embeds');

module.exports = {
  name: 'dice',
  description: 'Roll dice with customizable sides and quantity',
  aliases: ['roll', 'd'],
  usage: 'dice [quantity]d[sides]',
  examples: ['dice', 'dice 2d6', 'dice 1d20', 'dice 3d10'],
  cooldown: 2,
  permissions: [],
  
  data: new SlashCommandBuilder()
    .setName('dice')
    .setDescription('Roll dice with customizable sides and quantity')
    .addStringOption(option =>
      option.setName('dice')
        .setDescription('Dice notation (e.g., 2d6, 1d20, 3d10)')
        .setRequired(false)
    ),

  async execute(messageOrInteraction, args, client) {
    try {
      const isSlash = messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.();
      
      // Get dice notation
      let diceNotation;
      
      if (isSlash) {
        diceNotation = messageOrInteraction.options.getString('dice') || '1d6';
      } else {
        diceNotation = args[0] || '1d6';
      }

      // Parse dice notation (e.g., "2d6", "1d20", "3d10")
      const diceRegex = /^(\d+)?d(\d+)$/i;
      const match = diceNotation.match(diceRegex);
      
      if (!match) {
        const embed = embeds.error(
          'Invalid Dice Notation', 
          'Please use the format `XdY` where X is the number of dice and Y is the number of sides.\n\n' +
          '**Examples:**\n' +
          '• `1d6` - Roll one 6-sided die\n' +
          '• `2d6` - Roll two 6-sided dice\n' +
          '• `1d20` - Roll one 20-sided die\n' +
          '• `3d10` - Roll three 10-sided dice'
        );
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed] });
        }
      }

      const quantity = parseInt(match[1]) || 1;
      const sides = parseInt(match[2]);

      // Validation
      if (quantity < 1 || quantity > 20) {
        const embed = embeds.error('Invalid Quantity', 'You can roll between 1 and 20 dice at once.');
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed] });
        }
      }

      if (sides < 2 || sides > 1000) {
        const embed = embeds.error('Invalid Sides', 'Dice must have between 2 and 1000 sides.');
        
        if (isSlash) {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed] });
        }
      }

      // Roll the dice
      const rolls = [];
      let total = 0;

      for (let i = 0; i < quantity; i++) {
        const roll = Math.floor(Math.random() * sides) + 1;
        rolls.push(roll);
        total += roll;
      }

      // Create result embed
      const embed = embeds.createBaseEmbed(client.config.bot.embedColors.primary)
        .setTitle('🎲 Dice Roll')
        .addFields(
          { name: 'Dice', value: `${quantity}d${sides}`, inline: true },
          { name: 'Total', value: `**${total}**`, inline: true }
        );

      // Add individual rolls if multiple dice
      if (quantity > 1) {
        const rollsText = rolls.map((roll, index) => {
          // Highlight critical rolls
          if (roll === 1) {
            return `~~${roll}~~`; // Critical fail
          } else if (roll === sides) {
            return `**${roll}**`; // Critical success
          } else {
            return roll.toString();
          }
        }).join(', ');

        embed.addFields({ name: 'Individual Rolls', value: rollsText, inline: false });
      } else {
        // For single die, show if it's a critical roll
        const roll = rolls[0];
        if (roll === 1 && sides > 2) {
          embed.setDescription('💥 **Critical Fail!**');
          embed.setColor(client.config.bot.embedColors.error);
        } else if (roll === sides) {
          embed.setDescription('✨ **Critical Success!**');
          embed.setColor(client.config.bot.embedColors.success);
        }
      }

      // Add statistics for multiple dice
      if (quantity > 1) {
        const average = (total / quantity).toFixed(1);
        const min = Math.min(...rolls);
        const max = Math.max(...rolls);
        
        embed.addFields(
          { name: 'Average', value: average, inline: true },
          { name: 'Lowest', value: min.toString(), inline: true },
          { name: 'Highest', value: max.toString(), inline: true }
        );
      }

      // Add possible range
      const minPossible = quantity;
      const maxPossible = quantity * sides;
      embed.addFields({ 
        name: 'Possible Range', 
        value: `${minPossible} - ${maxPossible}`, 
        inline: true 
      });

      if (isSlash) {
        return messageOrInteraction.reply({ embeds: [embed] });
      } else {
        return messageOrInteraction.reply({ embeds: [embed] });
      }

    } catch (error) {
      client.logger.error('Error in dice command:', error);
      
      const embed = embeds.error('Error', 'The dice got lost! Please try again.');
      
      if (messageOrInteraction.isCommand?.() || messageOrInteraction.isChatInputCommand?.()) {
        if (messageOrInteraction.replied || messageOrInteraction.deferred) {
          return messageOrInteraction.followUp({ embeds: [embed], ephemeral: true });
        } else {
          return messageOrInteraction.reply({ embeds: [embed], ephemeral: true });
        }
      } else {
        return messageOrInteraction.reply({ embeds: [embed] });
      }
    }
  }
};
