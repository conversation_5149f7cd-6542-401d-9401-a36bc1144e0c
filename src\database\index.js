const config = require('../config/config');
const logger = require('../utils/logger');

// Database abstraction layer that automatically chooses between Supabase and Sequelize
class DatabaseManager {
  constructor() {
    this.useSupabase = !!(config.supabase.url && config.supabase.serviceKey);
    this.utils = null;
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      if (this.useSupabase) {
        logger.info('Using Supabase as primary database');
        this.utils = require('./supabaseUtils');
      } else {
        logger.info('Using Sequelize as fallback database');
        this.utils = require('./utils');
      }
      
      this.initialized = true;
      logger.info('Database manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database manager:', error);
      throw error;
    }
  }

  // Proxy all method calls to the appropriate utils
  async getOrCreateGuild(guildId, guildData = {}) {
    await this.initialize();
    return this.utils.getOrCreateGuild(guildId, guildData);
  }

  async updateGuildSettings(guildId, settings) {
    await this.initialize();
    return this.utils.updateGuildSettings(guildId, settings);
  }

  async getOrCreateUser(userId, userData = {}) {
    await this.initialize();
    return this.utils.getOrCreateUser(userId, userData);
  }

  async getOrCreateEconomy(userId, guildId) {
    await this.initialize();
    return this.utils.getOrCreateEconomy(userId, guildId);
  }

  async updateBalance(userId, guildId, amount, operation = 'add') {
    await this.initialize();
    return this.utils.updateBalance(userId, guildId, amount, operation);
  }

  async getOrCreateLevel(userId, guildId) {
    await this.initialize();
    return this.utils.getOrCreateLevel(userId, guildId);
  }

  async addXp(userId, guildId, xpAmount) {
    await this.initialize();
    return this.utils.addXp(userId, guildId, xpAmount);
  }

  calculateLevel(totalXp) {
    return Math.floor(Math.sqrt(totalXp / 100)) + 1;
  }

  calculateXpForLevel(level) {
    return Math.pow(level - 1, 2) * 100;
  }

  async addWarning(userId, guildId, moderatorId, reason, expiresAt = null) {
    await this.initialize();
    return this.utils.addWarning(userId, guildId, moderatorId, reason, expiresAt);
  }

  async getActiveWarnings(userId, guildId) {
    await this.initialize();
    return this.utils.getActiveWarnings(userId, guildId);
  }

  async logModerationAction(data) {
    await this.initialize();
    return this.utils.logModerationAction(data);
  }

  async getEconomyLeaderboard(guildId, limit = 10) {
    await this.initialize();
    return this.utils.getEconomyLeaderboard(guildId, limit);
  }

  async getLevelLeaderboard(guildId, limit = 10) {
    await this.initialize();
    return this.utils.getLevelLeaderboard(guildId, limit);
  }

  async cleanupExpiredWarnings() {
    await this.initialize();
    return this.utils.cleanupExpiredWarnings();
  }

  async cleanupOldLogs(daysOld = 90) {
    await this.initialize();
    return this.utils.cleanupOldLogs(daysOld);
  }

  // Health check
  async healthCheck() {
    try {
      await this.initialize();
      
      if (this.useSupabase) {
        const { SupabaseUtils } = require('./supabase');
        return await SupabaseUtils.healthCheck();
      } else {
        const { healthCheck } = require('./database');
        return await healthCheck();
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Get database type
  getDatabaseType() {
    return this.useSupabase ? 'supabase' : 'sequelize';
  }

  // Enhanced methods for new features
  async getOrCreateReminder(userId, guildId, reminderData) {
    await this.initialize();
    return this.utils.getOrCreateReminder(userId, guildId, reminderData);
  }

  async getUserReminders(userId, guildId) {
    await this.initialize();
    return this.utils.getUserReminders(userId, guildId);
  }

  async deleteReminder(reminderId) {
    await this.initialize();
    return this.utils.deleteReminder(reminderId);
  }

  async getExpiredReminders() {
    await this.initialize();
    return this.utils.getExpiredReminders();
  }

  async createCustomCommand(guildId, commandData) {
    await this.initialize();
    return this.utils.createCustomCommand(guildId, commandData);
  }

  async getCustomCommands(guildId) {
    await this.initialize();
    return this.utils.getCustomCommands(guildId);
  }

  async deleteCustomCommand(guildId, commandName) {
    await this.initialize();
    return this.utils.deleteCustomCommand(guildId, commandName);
  }

  async updateAutoModSettings(guildId, settings) {
    await this.initialize();
    return this.utils.updateAutoModSettings(guildId, settings);
  }

  async getAutoModSettings(guildId) {
    await this.initialize();
    return this.utils.getAutoModSettings(guildId);
  }

  async logAutoModAction(guildId, actionData) {
    await this.initialize();
    return this.utils.logAutoModAction(guildId, actionData);
  }

  async createBackup(backupData) {
    await this.initialize();
    return this.utils.createBackup(backupData);
  }

  async getBackups(limit = 10) {
    await this.initialize();
    return this.utils.getBackups(limit);
  }

  async restoreBackup(backupId) {
    await this.initialize();
    return this.utils.restoreBackup(backupId);
  }

  // Migration support
  async runMigrations() {
    await this.initialize();
    if (this.utils.runMigrations) {
      return this.utils.runMigrations();
    }
    return true; // No migrations needed
  }

  // Export/Import for backups
  async exportTable(tableName) {
    await this.initialize();
    if (this.utils.exportTable) {
      return this.utils.exportTable(tableName);
    }
    return [];
  }

  async importTable(tableName, data) {
    await this.initialize();
    if (this.utils.importTable) {
      return this.utils.importTable(tableName, data);
    }
    return false;
  }

  // Advanced analytics
  async getGuildAnalytics(guildId, timeframe = '7d') {
    await this.initialize();
    if (this.utils.getGuildAnalytics) {
      return this.utils.getGuildAnalytics(guildId, timeframe);
    }
    return {};
  }

  async getUserAnalytics(userId, guildId, timeframe = '7d') {
    await this.initialize();
    if (this.utils.getUserAnalytics) {
      return this.utils.getUserAnalytics(userId, guildId, timeframe);
    }
    return {};
  }

  // Cleanup methods
  async performMaintenance() {
    await this.initialize();
    const results = {
      expiredWarnings: await this.cleanupExpiredWarnings(),
      oldLogs: await this.cleanupOldLogs(),
      expiredReminders: 0
    };

    // Clean expired reminders
    if (this.utils.cleanupExpiredReminders) {
      results.expiredReminders = await this.utils.cleanupExpiredReminders();
    }

    return results;
  }

  // Close database connection
  async close() {
    if (this.utils && this.utils.close) {
      await this.utils.close();
    }
    this.initialized = false;
  }
}

module.exports = new DatabaseManager();
