<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Commands - NexusBot Dashboard</title>
    <meta name="description" content="Complete list of NexusBot commands with examples and usage instructions">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/commands.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-robot"></i>
                <span>NexusBot</span>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="index.html#features" class="nav-link">Features</a>
                <a href="commands.html" class="nav-link active">Commands</a>
                <a href="setup.html" class="nav-link">Setup</a>
                <a href="index.html#stats" class="nav-link">Stats</a>
            </div>
            <div class="nav-actions">
                <a href="#invite" class="btn btn-primary">
                    <i class="fab fa-discord"></i>
                    Invite Bot
                </a>
                <div class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Commands Header -->
    <section class="commands-header">
        <div class="container">
            <div class="commands-hero">
                <h1 class="commands-title">
                    <span class="gradient-text">Command Reference</span>
                </h1>
                <p class="commands-description">
                    Complete documentation for all NexusBot commands. Use either slash commands (/) or prefix commands (-).
                </p>
                <div class="command-search">
                    <div class="search-container">
                        <i class="fas fa-search"></i>
                        <input type="text" id="command-search" placeholder="Search commands..." autocomplete="off">
                    </div>
                    <div class="search-filters">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="moderation">Moderation</button>
                        <button class="filter-btn" data-filter="music">Music</button>
                        <button class="filter-btn" data-filter="economy">Economy</button>
                        <button class="filter-btn" data-filter="entertainment">Entertainment</button>
                        <button class="filter-btn" data-filter="utility">Utility</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Commands Content -->
    <section class="commands-content">
        <div class="container">
            <div class="commands-grid" id="commands-grid">
                <!-- Commands will be populated by JavaScript -->
            </div>
            
            <div class="no-results" id="no-results" style="display: none;">
                <div class="no-results-content">
                    <i class="fas fa-search"></i>
                    <h3>No commands found</h3>
                    <p>Try adjusting your search or filter criteria.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Command Modal -->
    <div class="modal" id="command-modal">
        <div class="modal-overlay" id="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title"></h3>
                <button class="modal-close" id="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Command details will be populated here -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-robot"></i>
                        <span>NexusBot</span>
                    </div>
                    <p class="footer-description">
                        The ultimate Discord bot for community management and entertainment.
                    </p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="#invite">Invite Bot</a></li>
                        <li><a href="commands.html">Commands</a></li>
                        <li><a href="setup.html">Setup Guide</a></li>
                        <li><a href="#support">Support</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul class="footer-links">
                        <li><a href="#discord">Discord Server</a></li>
                        <li><a href="#github">GitHub</a></li>
                        <li><a href="#docs">Documentation</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 NexusBot. Made with ❤️ for the Discord community.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/commands.js"></script>
</body>
</html>
