/**
 * Custom Command Model for NexusBot
 * Handles guild-specific custom commands
 * <AUTHOR> Team
 * @version 2.0.0
 */

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const CustomCommand = sequelize.define('CustomCommand', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    guildId: {
      type: DataTypes.STRING,
      allowNull: false,
      index: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 32],
        is: /^[a-zA-Z0-9_-]+$/
      }
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 100]
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        len: [1, 2000]
      }
    },
    creatorId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    isEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    usageCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    lastUsed: {
      type: DataTypes.DATE,
      allowNull: true
    },
    permissions: {
      type: DataTypes.JSON,
      defaultValue: {
        roles: [],
        users: [],
        channels: [],
        requirePermission: null
      }
    },
    aliases: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    cooldown: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0,
        max: 3600 // Max 1 hour cooldown
      }
    },
    embedConfig: {
      type: DataTypes.JSON,
      defaultValue: null
    },
    variables: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    metadata: {
      type: DataTypes.JSON,
      defaultValue: {}
    }
  }, {
    tableName: 'custom_commands',
    indexes: [
      {
        unique: true,
        fields: ['guildId', 'name']
      },
      {
        fields: ['guildId', 'isEnabled']
      },
      {
        fields: ['creatorId']
      }
    ],
    hooks: {
      beforeValidate: (command) => {
        // Normalize command name
        if (command.name) {
          command.name = command.name.toLowerCase().trim();
        }
      },
      beforeCreate: (command) => {
        // Validate aliases
        if (command.aliases && Array.isArray(command.aliases)) {
          command.aliases = command.aliases.map(alias => 
            alias.toLowerCase().trim()
          ).filter(alias => alias.length > 0);
        }
      }
    }
  });

  // Instance methods
  CustomCommand.prototype.incrementUsage = async function() {
    this.usageCount += 1;
    this.lastUsed = new Date();
    return await this.save();
  };

  CustomCommand.prototype.canUserUse = function(member) {
    if (!this.isEnabled) return false;

    const permissions = this.permissions || {};
    
    // Check if user is specifically allowed
    if (permissions.users && permissions.users.includes(member.id)) {
      return true;
    }

    // Check if user has required permission
    if (permissions.requirePermission) {
      if (!member.permissions.has(permissions.requirePermission)) {
        return false;
      }
    }

    // Check if user has required role
    if (permissions.roles && permissions.roles.length > 0) {
      const hasRole = permissions.roles.some(roleId => 
        member.roles.cache.has(roleId)
      );
      if (!hasRole) return false;
    }

    return true;
  };

  CustomCommand.prototype.canUseInChannel = function(channelId) {
    const permissions = this.permissions || {};
    
    // If no channel restrictions, allow everywhere
    if (!permissions.channels || permissions.channels.length === 0) {
      return true;
    }

    return permissions.channels.includes(channelId);
  };

  CustomCommand.prototype.processContent = function(context = {}) {
    let content = this.content;
    
    // Replace built-in variables
    const variables = {
      '{user}': context.user?.toString() || '{user}',
      '{user.mention}': context.user?.toString() || '{user.mention}',
      '{user.name}': context.user?.username || '{user.name}',
      '{user.id}': context.user?.id || '{user.id}',
      '{guild}': context.guild?.name || '{guild}',
      '{guild.name}': context.guild?.name || '{guild.name}',
      '{guild.id}': context.guild?.id || '{guild.id}',
      '{channel}': context.channel?.toString() || '{channel}',
      '{channel.name}': context.channel?.name || '{channel.name}',
      '{channel.id}': context.channel?.id || '{channel.id}',
      '{date}': new Date().toLocaleDateString(),
      '{time}': new Date().toLocaleTimeString(),
      '{timestamp}': Math.floor(Date.now() / 1000),
      ...this.variables
    };

    // Replace all variables
    for (const [variable, value] of Object.entries(variables)) {
      content = content.replace(new RegExp(variable.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
    }

    return content;
  };

  // Class methods
  CustomCommand.findByGuild = async function(guildId, includeDisabled = false) {
    const where = { guildId };
    if (!includeDisabled) {
      where.isEnabled = true;
    }

    return await this.findAll({
      where,
      order: [['name', 'ASC']]
    });
  };

  CustomCommand.findByName = async function(guildId, name) {
    return await this.findOne({
      where: {
        guildId,
        name: name.toLowerCase(),
        isEnabled: true
      }
    });
  };

  CustomCommand.findByAlias = async function(guildId, alias) {
    return await this.findOne({
      where: {
        guildId,
        aliases: {
          [sequelize.Sequelize.Op.contains]: [alias.toLowerCase()]
        },
        isEnabled: true
      }
    });
  };

  CustomCommand.countByGuild = async function(guildId) {
    return await this.count({
      where: { guildId }
    });
  };

  CustomCommand.getPopularCommands = async function(guildId, limit = 10) {
    return await this.findAll({
      where: {
        guildId,
        isEnabled: true,
        usageCount: {
          [sequelize.Sequelize.Op.gt]: 0
        }
      },
      order: [['usageCount', 'DESC']],
      limit
    });
  };

  CustomCommand.cleanupUnusedCommands = async function(daysOld = 90) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const deleted = await this.destroy({
      where: {
        usageCount: 0,
        createdAt: {
          [sequelize.Sequelize.Op.lt]: cutoffDate
        }
      }
    });

    return deleted;
  };

  return CustomCommand;
};
