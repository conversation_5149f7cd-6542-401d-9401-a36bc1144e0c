/* ===== COMMANDS PAGE STYLES ===== */

.commands-header {
  padding: 120px 0 60px;
  background: var(--gradient-dark);
  position: relative;
  overflow: hidden;
}

.commands-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(107, 70, 193, 0.1) 0%, transparent 50%);
  z-index: -1;
}

.commands-hero {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.commands-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
}

.commands-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

/* ===== SEARCH & FILTERS ===== */
.command-search {
  max-width: 600px;
  margin: 0 auto;
}

.search-container {
  position: relative;
  margin-bottom: var(--spacing-md);
}

.search-container i {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1.1rem;
}

#command-search {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 3rem;
  background: var(--surface-color);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all var(--transition-normal);
}

#command-search:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

#command-search::placeholder {
  color: var(--text-muted);
}

.search-filters {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--spacing-xs) var(--spacing-md);
  background: var(--surface-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.9rem;
  font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* ===== COMMANDS CONTENT ===== */
.commands-content {
  padding: var(--spacing-xxl) 0;
}

.commands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.command-card {
  background: var(--surface-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.command-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.command-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.command-card:hover::before {
  transform: scaleX(1);
}

.command-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.command-name {
  font-family: 'Courier New', monospace;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--accent-color);
}

.command-category {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.command-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.command-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.9rem;
  color: var(--text-muted);
}

.command-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.command-permissions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: var(--spacing-sm);
}

.permission-tag {
  background: rgba(255, 68, 68, 0.2);
  color: var(--error-color);
  padding: 0.2rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

/* ===== NO RESULTS ===== */
.no-results {
  text-align: center;
  padding: var(--spacing-xxl) 0;
}

.no-results-content {
  max-width: 400px;
  margin: 0 auto;
}

.no-results i {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.no-results h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.no-results p {
  color: var(--text-secondary);
}

/* ===== MODAL ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  display: none;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
}

.modal.active {
  display: flex;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: var(--background-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  z-index: 1;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  font-family: 'Courier New', monospace;
  color: var(--accent-color);
  font-size: 1.5rem;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--surface-color);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-section {
  margin-bottom: var(--spacing-lg);
}

.modal-section:last-child {
  margin-bottom: 0;
}

.modal-section h4 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  font-size: 1.1rem;
}

.modal-section p,
.modal-section li {
  color: var(--text-secondary);
  line-height: 1.6;
}

.modal-section ul {
  padding-left: var(--spacing-md);
}

.modal-section li {
  margin-bottom: 0.25rem;
}

.usage-example {
  background: var(--surface-color);
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
  border-left: 4px solid var(--primary-color);
  font-family: 'Courier New', monospace;
  color: var(--accent-color);
  margin: var(--spacing-sm) 0;
}

.aliases-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.alias-tag {
  background: var(--surface-color);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .commands-title {
    font-size: 2.5rem;
  }
  
  .commands-grid {
    grid-template-columns: 1fr;
  }
  
  .search-filters {
    flex-direction: column;
    align-items: center;
  }
  
  .filter-btn {
    width: 120px;
    text-align: center;
  }
  
  .command-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .command-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .modal-content {
    margin: var(--spacing-sm);
    max-height: calc(100vh - 2rem);
  }
}
